// Database Models based on SQL Schema
// Represents the entities from the my_* tables with validation and relationships

// Base Model class with common functionality
class BaseModel {
    constructor(data = {}) {
        this.data = data;
        this.errors = {};
    }

    validate() {
        this.errors = {};
        return this.isValid();
    }

    isValid() {
        return Object.keys(this.errors).length === 0;
    }

    addError(field, message) {
        if (!this.errors[field]) {
            this.errors[field] = [];
        }
        this.errors[field].push(message);
    }

    getErrors() {
        return this.errors;
    }

    toJSON() {
        return this.data;
    }
}

// User Model (my_users table)
export class User extends BaseModel {
    constructor(data = {}) {
        super(data);
        this.id = data.id || null;
        this.user_name = data.user_name || '';
        this.password = data.password || '';
        this.role_id = data.role_id || null;
        this.group_id = data.group_id || null;
        this.created_on = data.created_on || new Date().toISOString();
        this.modified_on = data.modified_on || new Date().toISOString();
        this.status = data.status || 1; // 1 = active, 0 = inactive
        this.ip_address = data.ip_address || '';
        this.last_logged_in = data.last_logged_in || new Date().toISOString();
        this.mobile_number = data.mobile_number || '';
        this.email = data.email || '';
    }

    validate() {
        super.validate();

        // Username validation
        if (!this.user_name || this.user_name.trim().length < 3) {
            this.addError('user_name', 'Username must be at least 3 characters long');
        }

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!this.email || !emailRegex.test(this.email)) {
            this.addError('email', 'Valid email address is required');
        }

        // Mobile number validation
        const mobileRegex = /^\+?[\d\s\-\(\)]{10,}$/;
        if (!this.mobile_number || !mobileRegex.test(this.mobile_number)) {
            this.addError('mobile_number', 'Valid mobile number is required');
        }

        // Role validation
        if (!this.role_id || this.role_id < 1) {
            this.addError('role_id', 'Valid role is required');
        }

        // Group validation
        if (!this.group_id || this.group_id < 1) {
            this.addError('group_id', 'Valid group is required');
        }

        return this.isValid();
    }

    toJSON() {
        return {
            id: this.id,
            user_name: this.user_name,
            password: this.password,
            role_id: this.role_id,
            group_id: this.group_id,
            created_on: this.created_on,
            modified_on: this.modified_on,
            status: this.status,
            ip_address: this.ip_address,
            last_logged_in: this.last_logged_in,
            mobile_number: this.mobile_number,
            email: this.email
        };
    }
}

// Role Model (my_role table)
export class Role extends BaseModel {
    constructor(data = {}) {
        super(data);
        this.id = data.id || null;
        this.role = data.role || '';
        this.role_description = data.role_description || '';
    }

    validate() {
        super.validate();

        if (!this.role || this.role.trim().length < 2) {
            this.addError('role', 'Role name must be at least 2 characters long');
        }

        if (!this.role_description || this.role_description.trim().length < 5) {
            this.addError('role_description', 'Role description must be at least 5 characters long');
        }

        return this.isValid();
    }

    toJSON() {
        return {
            id: this.id,
            role: this.role,
            role_description: this.role_description
        };
    }
}

// Group Data Model (my_group_data table)
export class GroupData extends BaseModel {
    constructor(data = {}) {
        super(data);
        this.id = data.id || null;
        this.name = data.name || '';
        this.logo = data.logo || '';
        this.icon = data.icon || '';
        this.background_color = data.background_color || '#ffffff';
        this.color = data.color || '#000000';
        this.group_url = data.group_url || '';
        this.group_name = data.group_name || '';
    }

    validate() {
        super.validate();

        if (!this.name || this.name.trim().length < 2) {
            this.addError('name', 'Group name must be at least 2 characters long');
        }

        if (!this.group_name || this.group_name.trim().length < 2) {
            this.addError('group_name', 'Group display name must be at least 2 characters long');
        }

        // Color validation (hex format)
        const colorRegex = /^#[0-9A-F]{6}$/i;
        if (!colorRegex.test(this.background_color)) {
            this.addError('background_color', 'Background color must be a valid hex color');
        }

        if (!colorRegex.test(this.color)) {
            this.addError('color', 'Text color must be a valid hex color');
        }

        return this.isValid();
    }

    toJSON() {
        return {
            id: this.id,
            name: this.name,
            logo: this.logo,
            icon: this.icon,
            background_color: this.background_color,
            color: this.color,
            group_url: this.group_url,
            group_name: this.group_name
        };
    }
}

// User Details Model (my_user_details table)
export class UserDetails extends BaseModel {
    constructor(data = {}) {
        super(data);
        this.id = data.id || null;
        this.first_name = data.first_name || '';
        this.middle_name = data.middle_name || '';
        this.last_name = data.last_name || '';
        this.user_id = data.user_id || null;
        this.display_name = data.display_name || '';
        this.gender = data.gender || '';
        this.martial_status = data.martial_status || '';
        this.coutry_id = data.coutry_id || null; // Note: typo in schema
        this.state_id = data.state_id || null;
        this.district_id = data.district_id || null;
        this.nationality = data.nationality || '';
        this.education = data.education || null;
        this.profession = data.profession || null;
        this.photo = data.photo || '';
        this.date_of_birth = data.date_of_birth || '';
        this.created_at = data.created_at || new Date().toISOString();
        this.update_at = data.update_at || new Date().toISOString();
        this.language_id = data.language_id || null;
    }

    validate() {
        super.validate();

        if (!this.first_name || this.first_name.trim().length < 2) {
            this.addError('first_name', 'First name must be at least 2 characters long');
        }

        if (!this.last_name || this.last_name.trim().length < 2) {
            this.addError('last_name', 'Last name must be at least 2 characters long');
        }

        if (!this.user_id || this.user_id < 1) {
            this.addError('user_id', 'Valid user ID is required');
        }

        if (this.gender && !['Male', 'Female', 'Other'].includes(this.gender)) {
            this.addError('gender', 'Gender must be Male, Female, or Other');
        }

        if (this.date_of_birth) {
            const birthDate = new Date(this.date_of_birth);
            const today = new Date();
            if (birthDate >= today) {
                this.addError('date_of_birth', 'Date of birth must be in the past');
            }
        }

        return this.isValid();
    }

    toJSON() {
        return {
            id: this.id,
            first_name: this.first_name,
            middle_name: this.middle_name,
            last_name: this.last_name,
            user_id: this.user_id,
            display_name: this.display_name,
            gender: this.gender,
            martial_status: this.martial_status,
            coutry_id: this.coutry_id,
            state_id: this.state_id,
            district_id: this.district_id,
            nationality: this.nationality,
            education: this.education,
            profession: this.profession,
            photo: this.photo,
            date_of_birth: this.date_of_birth,
            created_at: this.created_at,
            update_at: this.update_at,
            language_id: this.language_id
        };
    }
}

// Country Model (my_country table)
export class Country extends BaseModel {
    constructor(data = {}) {
        super(data);
        this.id = data.id || null;
        this.name = data.name || '';
        this.continent_id = data.continent_id || null;
        this.code = data.code || '';
        this.currency = data.currency || '';
        this.flag = data.flag || '';
        this.iso_code = data.iso_code || '';
        this.nationality = data.nationality || null;
        this.display_order = data.display_order || 0;
    }

    validate() {
        super.validate();

        if (!this.name || this.name.trim().length < 2) {
            this.addError('name', 'Country name must be at least 2 characters long');
        }

        if (!this.code || this.code.trim().length !== 2) {
            this.addError('code', 'Country code must be exactly 2 characters');
        }

        if (!this.iso_code || this.iso_code.trim().length !== 3) {
            this.addError('iso_code', 'ISO code must be exactly 3 characters');
        }

        if (!this.continent_id || this.continent_id < 1) {
            this.addError('continent_id', 'Valid continent is required');
        }

        return this.isValid();
    }

    toJSON() {
        return {
            id: this.id,
            name: this.name,
            continent_id: this.continent_id,
            code: this.code,
            currency: this.currency,
            flag: this.flag,
            iso_code: this.iso_code,
            nationality: this.nationality,
            display_order: this.display_order
        };
    }
}

// State Model (my_state table)
export class State extends BaseModel {
    constructor(data = {}) {
        super(data);
        this.id = data.id || null;
        this.country_id = data.country_id || null;
        this.name = data.name || '';
        this.code = data.code || '';
        this.display_order = data.display_order || 0;
        this.status = data.status || 'Active';
    }

    validate() {
        super.validate();

        if (!this.name || this.name.trim().length < 2) {
            this.addError('name', 'State name must be at least 2 characters long');
        }

        if (!this.country_id || this.country_id < 1) {
            this.addError('country_id', 'Valid country is required');
        }

        if (!['Active', 'Inactive'].includes(this.status)) {
            this.addError('status', 'Status must be Active or Inactive');
        }

        return this.isValid();
    }

    toJSON() {
        return {
            id: this.id,
            country_id: this.country_id,
            name: this.name,
            code: this.code,
            display_order: this.display_order,
            status: this.status
        };
    }
}

// District Model (my_district table)
export class District extends BaseModel {
    constructor(data = {}) {
        super(data);
        this.id = data.id || null;
        this.state_id = data.state_id || null;
        this.name = data.name || '';
        this.code = data.code || '';
        this.display_order = data.display_order || 0;
    }

    validate() {
        super.validate();

        if (!this.name || this.name.trim().length < 2) {
            this.addError('name', 'District name must be at least 2 characters long');
        }

        if (!this.state_id || this.state_id < 1) {
            this.addError('state_id', 'Valid state is required');
        }

        return this.isValid();
    }

    toJSON() {
        return {
            id: this.id,
            state_id: this.state_id,
            name: this.name,
            code: this.code,
            display_order: this.display_order
        };
    }
}

// Advertisement Model (my_ads table)
export class Advertisement extends BaseModel {
    constructor(data = {}) {
        super(data);
        this.id = data.id || null;
        this.titile = data.titile || ''; // Note: typo in schema
        this.description = data.description || '';
        this.my_user_id = data.my_user_id || null;
        this.my_group_category_id = data.my_group_category_id || null;
        this.my_ads_type_id = data.my_ads_type_id || null;
        this.country_id = data.country_id || null;
        this.state_id = data.state_id || null;
        this.district_id = data.district_id || null;
        this.start_date = data.start_date || '';
        this.end_date = data.end_date || '';
        this.status = data.status || 'Pending';
        this.created_at = data.created_at || new Date().toISOString();
        this.update_at = data.update_at || new Date().toISOString();
    }

    validate() {
        super.validate();

        if (!this.titile || this.titile.trim().length < 3) {
            this.addError('titile', 'Title must be at least 3 characters long');
        }

        if (!this.description || this.description.trim().length < 10) {
            this.addError('description', 'Description must be at least 10 characters long');
        }

        if (!this.my_user_id || this.my_user_id < 1) {
            this.addError('my_user_id', 'Valid user is required');
        }

        if (!this.start_date) {
            this.addError('start_date', 'Start date is required');
        }

        if (!this.end_date) {
            this.addError('end_date', 'End date is required');
        }

        if (this.start_date && this.end_date) {
            const startDate = new Date(this.start_date);
            const endDate = new Date(this.end_date);
            if (endDate <= startDate) {
                this.addError('end_date', 'End date must be after start date');
            }
        }

        return this.isValid();
    }

    toJSON() {
        return {
            id: this.id,
            titile: this.titile,
            description: this.description,
            my_user_id: this.my_user_id,
            my_group_category_id: this.my_group_category_id,
            my_ads_type_id: this.my_ads_type_id,
            country_id: this.country_id,
            state_id: this.state_id,
            district_id: this.district_id,
            start_date: this.start_date,
            end_date: this.end_date,
            status: this.status,
            created_at: this.created_at,
            update_at: this.update_at
        };
    }
}

// Model Factory for creating instances
export class ModelFactory {
    static createUser(data) {
        return new User(data);
    }

    static createRole(data) {
        return new Role(data);
    }

    static createGroupData(data) {
        return new GroupData(data);
    }

    static createUserDetails(data) {
        return new UserDetails(data);
    }

    static createCountry(data) {
        return new Country(data);
    }

    static createState(data) {
        return new State(data);
    }

    static createDistrict(data) {
        return new District(data);
    }

    static createAdvertisement(data) {
        return new Advertisement(data);
    }
}
