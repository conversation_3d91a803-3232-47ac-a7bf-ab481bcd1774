// Test login functionality
import fetch from 'node-fetch';

async function testLogin() {
  try {
    console.log('Testing login with admin credentials...');
    
    const response = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'password123'
      })
    });

    const result = await response.json();
    console.log('Login result:', JSON.stringify(result, null, 2));

    if (result.success) {
      console.log('✅ Login successful!');
      console.log('User:', result.user);
    } else {
      console.log('❌ Login failed:', result.error);
    }

  } catch (error) {
    console.error('❌ Test error:', error.message);
  }
}

testLogin();
