// Test database schema
import { Pool } from 'pg';
import dotenv from 'dotenv';

dotenv.config();

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'my_group_db',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'admin',
});

async function checkSchema() {
  try {
    console.log('Checking database schema...\n');

    // Check if all required tables exist
    const tablesQuery = `
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name LIKE 'my_%'
      ORDER BY table_name;
    `;

    const result = await pool.query(tablesQuery);
    console.log('📋 Tables with "my_" prefix:');
    result.rows.forEach(row => {
      console.log(`  ✅ ${row.table_name}`);
    });

    // Check user data
    console.log('\n👥 Users in my_group_user table:');
    const usersQuery = 'SELECT id, username, role, is_active FROM my_group_user ORDER BY username';
    const users = await pool.query(usersQuery);
    users.rows.forEach(user => {
      console.log(`  ${user.is_active ? '✅' : '❌'} ${user.username} (${user.role})`);
    });

    // Check apps
    console.log('\n📱 Apps in my_group_app table:');
    const appsQuery = 'SELECT id, name, url FROM my_group_app';
    const apps = await pool.query(appsQuery);
    apps.rows.forEach(app => {
      console.log(`  ✅ ${app.name} - ${app.url}`);
    });

    // Check location data
    console.log('\n🌍 Location data:');
    const continentsQuery = 'SELECT COUNT(*) as count FROM continents';
    const continents = await pool.query(continentsQuery);
    console.log(`  Continents: ${continents.rows[0].count}`);

    const countriesQuery = 'SELECT COUNT(*) as count FROM countries';
    const countries = await pool.query(countriesQuery);
    console.log(`  Countries: ${countries.rows[0].count}`);

    console.log('\n✅ Database schema check completed successfully!');

  } catch (error) {
    console.error('❌ Schema check error:', error.message);
  } finally {
    await pool.end();
  }
}

checkSchema();
