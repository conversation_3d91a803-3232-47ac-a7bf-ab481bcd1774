// Test authentication for all user roles
import fetch from 'node-fetch';

const API_BASE = 'http://localhost:3001/api';

// Test users from the database
const testUsers = [
  { username: 'admin', password: 'password123', expectedRole: 'SUPER_ADMIN' },
  { username: 'superadmin', password: 'password123', expectedRole: 'SUPER_ADMIN' },
  { username: 'corporate1', password: 'password123', expectedRole: 'CORPORATE' },
  { username: 'corporate2', password: 'password123', expectedRole: 'CORPORATE' },
  { username: 'branch1', password: 'password123', expectedRole: 'BRANCH' },
  { username: 'branch2', password: 'password123', expectedRole: 'BRANCH' },
  { username: 'branch3', password: 'password123', expectedRole: 'BRANCH' },
  { username: '8884284844', password: 'password123', expectedRole: 'USER' }
];

async function testLogin(username, password) {
  try {
    const response = await fetch(`${API_BASE}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ username, password })
    });

    const result = await response.json();
    return result;
  } catch (error) {
    return { success: false, error: error.message };
  }
}

async function testAllUsers() {
  console.log('🔐 Testing Authentication for All User Roles\n');
  console.log('=' .repeat(60));

  let successCount = 0;
  let failCount = 0;

  for (const user of testUsers) {
    console.log(`\n👤 Testing: ${user.username} (Expected: ${user.expectedRole})`);
    console.log('-'.repeat(40));

    const result = await testLogin(user.username, user.password);

    if (result.success) {
      const actualRole = result.user.role;
      const roleMatch = actualRole === user.expectedRole;
      
      console.log(`✅ Login: SUCCESS`);
      console.log(`📋 Role: ${actualRole} ${roleMatch ? '✅' : '❌'}`);
      console.log(`🆔 User ID: ${result.user.id}`);
      console.log(`📧 Email: ${result.user.email_id || 'Not set'}`);
      console.log(`👤 Name: ${result.user.full_name || 'Not set'}`);
      
      if (roleMatch) {
        successCount++;
      } else {
        failCount++;
        console.log(`❌ Role mismatch! Expected: ${user.expectedRole}, Got: ${actualRole}`);
      }
    } else {
      failCount++;
      console.log(`❌ Login: FAILED`);
      console.log(`💬 Error: ${result.error}`);
    }
  }

  console.log('\n' + '='.repeat(60));
  console.log('📊 AUTHENTICATION TEST SUMMARY');
  console.log('='.repeat(60));
  console.log(`✅ Successful logins: ${successCount}`);
  console.log(`❌ Failed logins: ${failCount}`);
  console.log(`📈 Success rate: ${((successCount / testUsers.length) * 100).toFixed(1)}%`);

  if (successCount === testUsers.length) {
    console.log('\n🎉 All authentication tests passed!');
  } else {
    console.log('\n⚠️  Some authentication tests failed. Check the details above.');
  }
}

// Test invalid credentials
async function testInvalidCredentials() {
  console.log('\n\n🚫 Testing Invalid Credentials');
  console.log('='.repeat(40));

  const invalidTests = [
    { username: 'nonexistent', password: 'password123' },
    { username: 'admin', password: 'wrongpassword' },
    { username: '', password: 'password123' },
    { username: 'admin', password: '' }
  ];

  for (const test of invalidTests) {
    console.log(`\n🔍 Testing: "${test.username}" / "${test.password}"`);
    const result = await testLogin(test.username, test.password);
    
    if (result.success) {
      console.log(`❌ SECURITY ISSUE: Invalid credentials accepted!`);
    } else {
      console.log(`✅ Correctly rejected: ${result.error}`);
    }
  }
}

// Run all tests
async function runAllTests() {
  await testAllUsers();
  await testInvalidCredentials();
  console.log('\n🏁 Authentication testing completed!');
}

runAllTests();
