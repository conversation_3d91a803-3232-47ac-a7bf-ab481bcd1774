{"version": 3, "sources": ["../../postgres-array/index.js", "../../pg-types/lib/arrayParser.js", "../../postgres-date/index.js", "../../xtend/mutable.js", "../../postgres-interval/index.js", "../../postgres-bytea/index.js", "../../pg-types/lib/textParsers.js", "../../pg-int8/index.js", "../../pg-types/lib/binaryParsers.js", "../../pg-types/lib/builtins.js", "../../pg-types/index.js", "../../pg/lib/defaults.js", "../../pg/lib/utils.js", "../../pg/lib/crypto/utils-legacy.js", "../../pg/lib/crypto/utils-webcrypto.js", "../../pg/lib/crypto/utils.js", "../../pg/lib/crypto/cert-signatures.js", "../../pg/lib/crypto/sasl.js", "../../pg/lib/type-overrides.js", "browser-external:dns", "../../pg-connection-string/index.js", "../../pg/lib/connection-parameters.js", "../../pg/lib/result.js", "../../pg/lib/query.js", "../../pg-protocol/src/messages.ts", "../../pg-protocol/src/buffer-writer.ts", "../../pg-protocol/src/serializer.ts", "../../pg-protocol/src/buffer-reader.ts", "../../pg-protocol/src/parser.ts", "../../pg-protocol/src/index.ts", "../../pg-cloudflare/src/empty.ts", "../../pg/lib/stream.js", "../../pg/lib/connection.js", "../../split2/index.js", "../../pgpass/lib/helper.js", "../../pgpass/lib/index.js", "../../pg/lib/client.js", "../../pg-pool/index.js", "optional-peer-dep:__vite-optional-peer-dep:pg-native:pg", "../../pg/lib/native/query.js", "../../pg/lib/native/client.js", "../../pg/lib/native/index.js", "../../pg/lib/index.js", "../../pg/esm/index.mjs"], "sourcesContent": ["'use strict'\r\n\r\nexports.parse = function (source, transform) {\r\n  return new ArrayParser(source, transform).parse()\r\n}\r\n\r\nclass ArrayParser {\r\n  constructor (source, transform) {\r\n    this.source = source\r\n    this.transform = transform || identity\r\n    this.position = 0\r\n    this.entries = []\r\n    this.recorded = []\r\n    this.dimension = 0\r\n  }\r\n\r\n  isEof () {\r\n    return this.position >= this.source.length\r\n  }\r\n\r\n  nextCharacter () {\r\n    var character = this.source[this.position++]\r\n    if (character === '\\\\') {\r\n      return {\r\n        value: this.source[this.position++],\r\n        escaped: true\r\n      }\r\n    }\r\n    return {\r\n      value: character,\r\n      escaped: false\r\n    }\r\n  }\r\n\r\n  record (character) {\r\n    this.recorded.push(character)\r\n  }\r\n\r\n  newEntry (includeEmpty) {\r\n    var entry\r\n    if (this.recorded.length > 0 || includeEmpty) {\r\n      entry = this.recorded.join('')\r\n      if (entry === 'NULL' && !includeEmpty) {\r\n        entry = null\r\n      }\r\n      if (entry !== null) entry = this.transform(entry)\r\n      this.entries.push(entry)\r\n      this.recorded = []\r\n    }\r\n  }\r\n\r\n  consumeDimensions () {\r\n    if (this.source[0] === '[') {\r\n      while (!this.isEof()) {\r\n        var char = this.nextCharacter()\r\n        if (char.value === '=') break\r\n      }\r\n    }\r\n  }\r\n\r\n  parse (nested) {\r\n    var character, parser, quote\r\n    this.consumeDimensions()\r\n    while (!this.isEof()) {\r\n      character = this.nextCharacter()\r\n      if (character.value === '{' && !quote) {\r\n        this.dimension++\r\n        if (this.dimension > 1) {\r\n          parser = new ArrayParser(this.source.substr(this.position - 1), this.transform)\r\n          this.entries.push(parser.parse(true))\r\n          this.position += parser.position - 2\r\n        }\r\n      } else if (character.value === '}' && !quote) {\r\n        this.dimension--\r\n        if (!this.dimension) {\r\n          this.newEntry()\r\n          if (nested) return this.entries\r\n        }\r\n      } else if (character.value === '\"' && !character.escaped) {\r\n        if (quote) this.newEntry(true)\r\n        quote = !quote\r\n      } else if (character.value === ',' && !quote) {\r\n        this.newEntry()\r\n      } else {\r\n        this.record(character.value)\r\n      }\r\n    }\r\n    if (this.dimension !== 0) {\r\n      throw new Error('array dimension not balanced')\r\n    }\r\n    return this.entries\r\n  }\r\n}\r\n\r\nfunction identity (value) {\r\n  return value\r\n}\r\n", "var array = require('postgres-array');\r\n\r\nmodule.exports = {\r\n  create: function (source, transform) {\r\n    return {\r\n      parse: function() {\r\n        return array.parse(source, transform);\r\n      }\r\n    };\r\n  }\r\n};\r\n", "'use strict'\r\n\r\nvar DATE_TIME = /(\\d{1,})-(\\d{2})-(\\d{2}) (\\d{2}):(\\d{2}):(\\d{2})(\\.\\d{1,})?.*?( BC)?$/\r\nvar DATE = /^(\\d{1,})-(\\d{2})-(\\d{2})( BC)?$/\r\nvar TIME_ZONE = /([Z+-])(\\d{2})?:?(\\d{2})?:?(\\d{2})?/\r\nvar INFINITY = /^-?infinity$/\r\n\r\nmodule.exports = function parseDate (isoDate) {\r\n  if (INFINITY.test(isoDate)) {\r\n    // Capitalize to Infinity before passing to Number\r\n    return Number(isoDate.replace('i', 'I'))\r\n  }\r\n  var matches = DATE_TIME.exec(isoDate)\r\n\r\n  if (!matches) {\r\n    // Force YYYY-MM-DD dates to be parsed as local time\r\n    return getDate(isoDate) || null\r\n  }\r\n\r\n  var isBC = !!matches[8]\r\n  var year = parseInt(matches[1], 10)\r\n  if (isBC) {\r\n    year = bcYearToNegativeYear(year)\r\n  }\r\n\r\n  var month = parseInt(matches[2], 10) - 1\r\n  var day = matches[3]\r\n  var hour = parseInt(matches[4], 10)\r\n  var minute = parseInt(matches[5], 10)\r\n  var second = parseInt(matches[6], 10)\r\n\r\n  var ms = matches[7]\r\n  ms = ms ? 1000 * parseFloat(ms) : 0\r\n\r\n  var date\r\n  var offset = timeZoneOffset(isoDate)\r\n  if (offset != null) {\r\n    date = new Date(Date.UTC(year, month, day, hour, minute, second, ms))\r\n\r\n    // Account for years from 0 to 99 being interpreted as 1900-1999\r\n    // by Date.UTC / the multi-argument form of the Date constructor\r\n    if (is0To99(year)) {\r\n      date.setUTCFullYear(year)\r\n    }\r\n\r\n    if (offset !== 0) {\r\n      date.setTime(date.getTime() - offset)\r\n    }\r\n  } else {\r\n    date = new Date(year, month, day, hour, minute, second, ms)\r\n\r\n    if (is0To99(year)) {\r\n      date.setFullYear(year)\r\n    }\r\n  }\r\n\r\n  return date\r\n}\r\n\r\nfunction getDate (isoDate) {\r\n  var matches = DATE.exec(isoDate)\r\n  if (!matches) {\r\n    return\r\n  }\r\n\r\n  var year = parseInt(matches[1], 10)\r\n  var isBC = !!matches[4]\r\n  if (isBC) {\r\n    year = bcYearToNegativeYear(year)\r\n  }\r\n\r\n  var month = parseInt(matches[2], 10) - 1\r\n  var day = matches[3]\r\n  // YYYY-MM-DD will be parsed as local time\r\n  var date = new Date(year, month, day)\r\n\r\n  if (is0To99(year)) {\r\n    date.setFullYear(year)\r\n  }\r\n\r\n  return date\r\n}\r\n\r\n// match timezones:\r\n// Z (UTC)\r\n// -05\r\n// +06:30\r\nfunction timeZoneOffset (isoDate) {\r\n  if (isoDate.endsWith('+00')) {\r\n    return 0\r\n  }\r\n\r\n  var zone = TIME_ZONE.exec(isoDate.split(' ')[1])\r\n  if (!zone) return\r\n  var type = zone[1]\r\n\r\n  if (type === 'Z') {\r\n    return 0\r\n  }\r\n  var sign = type === '-' ? -1 : 1\r\n  var offset = parseInt(zone[2], 10) * 3600 +\r\n    parseInt(zone[3] || 0, 10) * 60 +\r\n    parseInt(zone[4] || 0, 10)\r\n\r\n  return offset * sign * 1000\r\n}\r\n\r\nfunction bcYearToNegativeYear (year) {\r\n  // Account for numerical difference between representations of BC years\r\n  // See: https://github.com/bendrucker/postgres-date/issues/5\r\n  return -(year - 1)\r\n}\r\n\r\nfunction is0To99 (num) {\r\n  return num >= 0 && num < 100\r\n}\r\n", "module.exports = extend\r\n\r\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\r\n\r\nfunction extend(target) {\r\n    for (var i = 1; i < arguments.length; i++) {\r\n        var source = arguments[i]\r\n\r\n        for (var key in source) {\r\n            if (hasOwnProperty.call(source, key)) {\r\n                target[key] = source[key]\r\n            }\r\n        }\r\n    }\r\n\r\n    return target\r\n}\r\n", "'use strict'\r\n\r\nvar extend = require('xtend/mutable')\r\n\r\nmodule.exports = PostgresInterval\r\n\r\nfunction PostgresInterval (raw) {\r\n  if (!(this instanceof PostgresInterval)) {\r\n    return new PostgresInterval(raw)\r\n  }\r\n  extend(this, parse(raw))\r\n}\r\nvar properties = ['seconds', 'minutes', 'hours', 'days', 'months', 'years']\r\nPostgresInterval.prototype.toPostgres = function () {\r\n  var filtered = properties.filter(this.hasOwnProperty, this)\r\n\r\n  // In addition to `properties`, we need to account for fractions of seconds.\r\n  if (this.milliseconds && filtered.indexOf('seconds') < 0) {\r\n    filtered.push('seconds')\r\n  }\r\n\r\n  if (filtered.length === 0) return '0'\r\n  return filtered\r\n    .map(function (property) {\r\n      var value = this[property] || 0\r\n\r\n      // Account for fractional part of seconds,\r\n      // remove trailing zeroes.\r\n      if (property === 'seconds' && this.milliseconds) {\r\n        value = (value + this.milliseconds / 1000).toFixed(6).replace(/\\.?0+$/, '')\r\n      }\r\n\r\n      return value + ' ' + property\r\n    }, this)\r\n    .join(' ')\r\n}\r\n\r\nvar propertiesISOEquivalent = {\r\n  years: 'Y',\r\n  months: 'M',\r\n  days: 'D',\r\n  hours: 'H',\r\n  minutes: 'M',\r\n  seconds: 'S'\r\n}\r\nvar dateProperties = ['years', 'months', 'days']\r\nvar timeProperties = ['hours', 'minutes', 'seconds']\r\n// according to ISO 8601\r\nPostgresInterval.prototype.toISOString = PostgresInterval.prototype.toISO = function () {\r\n  var datePart = dateProperties\r\n    .map(buildProperty, this)\r\n    .join('')\r\n\r\n  var timePart = timeProperties\r\n    .map(buildProperty, this)\r\n    .join('')\r\n\r\n  return 'P' + datePart + 'T' + timePart\r\n\r\n  function buildProperty (property) {\r\n    var value = this[property] || 0\r\n\r\n    // Account for fractional part of seconds,\r\n    // remove trailing zeroes.\r\n    if (property === 'seconds' && this.milliseconds) {\r\n      value = (value + this.milliseconds / 1000).toFixed(6).replace(/0+$/, '')\r\n    }\r\n\r\n    return value + propertiesISOEquivalent[property]\r\n  }\r\n}\r\n\r\nvar NUMBER = '([+-]?\\\\d+)'\r\nvar YEAR = NUMBER + '\\\\s+years?'\r\nvar MONTH = NUMBER + '\\\\s+mons?'\r\nvar DAY = NUMBER + '\\\\s+days?'\r\nvar TIME = '([+-])?([\\\\d]*):(\\\\d\\\\d):(\\\\d\\\\d)\\\\.?(\\\\d{1,6})?'\r\nvar INTERVAL = new RegExp([YEAR, MONTH, DAY, TIME].map(function (regexString) {\r\n  return '(' + regexString + ')?'\r\n})\r\n  .join('\\\\s*'))\r\n\r\n// Positions of values in regex match\r\nvar positions = {\r\n  years: 2,\r\n  months: 4,\r\n  days: 6,\r\n  hours: 9,\r\n  minutes: 10,\r\n  seconds: 11,\r\n  milliseconds: 12\r\n}\r\n// We can use negative time\r\nvar negatives = ['hours', 'minutes', 'seconds', 'milliseconds']\r\n\r\nfunction parseMilliseconds (fraction) {\r\n  // add omitted zeroes\r\n  var microseconds = fraction + '000000'.slice(fraction.length)\r\n  return parseInt(microseconds, 10) / 1000\r\n}\r\n\r\nfunction parse (interval) {\r\n  if (!interval) return {}\r\n  var matches = INTERVAL.exec(interval)\r\n  var isNegative = matches[8] === '-'\r\n  return Object.keys(positions)\r\n    .reduce(function (parsed, property) {\r\n      var position = positions[property]\r\n      var value = matches[position]\r\n      // no empty string\r\n      if (!value) return parsed\r\n      // milliseconds are actually microseconds (up to 6 digits)\r\n      // with omitted trailing zeroes.\r\n      value = property === 'milliseconds'\r\n        ? parseMilliseconds(value)\r\n        : parseInt(value, 10)\r\n      // no zeros\r\n      if (!value) return parsed\r\n      if (isNegative && ~negatives.indexOf(property)) {\r\n        value *= -1\r\n      }\r\n      parsed[property] = value\r\n      return parsed\r\n    }, {})\r\n}\r\n", "'use strict'\r\n\r\nmodule.exports = function parseBytea (input) {\r\n  if (/^\\\\x/.test(input)) {\r\n    // new 'hex' style response (pg >9.0)\r\n    return new Buffer(input.substr(2), 'hex')\r\n  }\r\n  var output = ''\r\n  var i = 0\r\n  while (i < input.length) {\r\n    if (input[i] !== '\\\\') {\r\n      output += input[i]\r\n      ++i\r\n    } else {\r\n      if (/[0-7]{3}/.test(input.substr(i + 1, 3))) {\r\n        output += String.fromCharCode(parseInt(input.substr(i + 1, 3), 8))\r\n        i += 4\r\n      } else {\r\n        var backslashes = 1\r\n        while (i + backslashes < input.length && input[i + backslashes] === '\\\\') {\r\n          backslashes++\r\n        }\r\n        for (var k = 0; k < Math.floor(backslashes / 2); ++k) {\r\n          output += '\\\\'\r\n        }\r\n        i += Math.floor(backslashes / 2) * 2\r\n      }\r\n    }\r\n  }\r\n  return new Buffer(output, 'binary')\r\n}\r\n", "var array = require('postgres-array')\r\nvar arrayParser = require('./arrayParser');\r\nvar parseDate = require('postgres-date');\r\nvar parseInterval = require('postgres-interval');\r\nvar parseByteA = require('postgres-bytea');\r\n\r\nfunction allowNull (fn) {\r\n  return function nullAllowed (value) {\r\n    if (value === null) return value\r\n    return fn(value)\r\n  }\r\n}\r\n\r\nfunction parseBool (value) {\r\n  if (value === null) return value\r\n  return value === 'TRUE' ||\r\n    value === 't' ||\r\n    value === 'true' ||\r\n    value === 'y' ||\r\n    value === 'yes' ||\r\n    value === 'on' ||\r\n    value === '1';\r\n}\r\n\r\nfunction parseBoolArray (value) {\r\n  if (!value) return null\r\n  return array.parse(value, parseBool)\r\n}\r\n\r\nfunction parseBaseTenInt (string) {\r\n  return parseInt(string, 10)\r\n}\r\n\r\nfunction parseIntegerArray (value) {\r\n  if (!value) return null\r\n  return array.parse(value, allowNull(parseBaseTenInt))\r\n}\r\n\r\nfunction parseBigIntegerArray (value) {\r\n  if (!value) return null\r\n  return array.parse(value, allowNull(function (entry) {\r\n    return parseBigInteger(entry).trim()\r\n  }))\r\n}\r\n\r\nvar parsePointArray = function(value) {\r\n  if(!value) { return null; }\r\n  var p = arrayParser.create(value, function(entry) {\r\n    if(entry !== null) {\r\n      entry = parsePoint(entry);\r\n    }\r\n    return entry;\r\n  });\r\n\r\n  return p.parse();\r\n};\r\n\r\nvar parseFloatArray = function(value) {\r\n  if(!value) { return null; }\r\n  var p = arrayParser.create(value, function(entry) {\r\n    if(entry !== null) {\r\n      entry = parseFloat(entry);\r\n    }\r\n    return entry;\r\n  });\r\n\r\n  return p.parse();\r\n};\r\n\r\nvar parseStringArray = function(value) {\r\n  if(!value) { return null; }\r\n\r\n  var p = arrayParser.create(value);\r\n  return p.parse();\r\n};\r\n\r\nvar parseDateArray = function(value) {\r\n  if (!value) { return null; }\r\n\r\n  var p = arrayParser.create(value, function(entry) {\r\n    if (entry !== null) {\r\n      entry = parseDate(entry);\r\n    }\r\n    return entry;\r\n  });\r\n\r\n  return p.parse();\r\n};\r\n\r\nvar parseIntervalArray = function(value) {\r\n  if (!value) { return null; }\r\n\r\n  var p = arrayParser.create(value, function(entry) {\r\n    if (entry !== null) {\r\n      entry = parseInterval(entry);\r\n    }\r\n    return entry;\r\n  });\r\n\r\n  return p.parse();\r\n};\r\n\r\nvar parseByteAArray = function(value) {\r\n  if (!value) { return null; }\r\n\r\n  return array.parse(value, allowNull(parseByteA));\r\n};\r\n\r\nvar parseInteger = function(value) {\r\n  return parseInt(value, 10);\r\n};\r\n\r\nvar parseBigInteger = function(value) {\r\n  var valStr = String(value);\r\n  if (/^\\d+$/.test(valStr)) { return valStr; }\r\n  return value;\r\n};\r\n\r\nvar parseJsonArray = function(value) {\r\n  if (!value) { return null; }\r\n\r\n  return array.parse(value, allowNull(JSON.parse));\r\n};\r\n\r\nvar parsePoint = function(value) {\r\n  if (value[0] !== '(') { return null; }\r\n\r\n  value = value.substring( 1, value.length - 1 ).split(',');\r\n\r\n  return {\r\n    x: parseFloat(value[0])\r\n  , y: parseFloat(value[1])\r\n  };\r\n};\r\n\r\nvar parseCircle = function(value) {\r\n  if (value[0] !== '<' && value[1] !== '(') { return null; }\r\n\r\n  var point = '(';\r\n  var radius = '';\r\n  var pointParsed = false;\r\n  for (var i = 2; i < value.length - 1; i++){\r\n    if (!pointParsed) {\r\n      point += value[i];\r\n    }\r\n\r\n    if (value[i] === ')') {\r\n      pointParsed = true;\r\n      continue;\r\n    } else if (!pointParsed) {\r\n      continue;\r\n    }\r\n\r\n    if (value[i] === ','){\r\n      continue;\r\n    }\r\n\r\n    radius += value[i];\r\n  }\r\n  var result = parsePoint(point);\r\n  result.radius = parseFloat(radius);\r\n\r\n  return result;\r\n};\r\n\r\nvar init = function(register) {\r\n  register(20, parseBigInteger); // int8\r\n  register(21, parseInteger); // int2\r\n  register(23, parseInteger); // int4\r\n  register(26, parseInteger); // oid\r\n  register(700, parseFloat); // float4/real\r\n  register(701, parseFloat); // float8/double\r\n  register(16, parseBool);\r\n  register(1082, parseDate); // date\r\n  register(1114, parseDate); // timestamp without timezone\r\n  register(1184, parseDate); // timestamp\r\n  register(600, parsePoint); // point\r\n  register(651, parseStringArray); // cidr[]\r\n  register(718, parseCircle); // circle\r\n  register(1000, parseBoolArray);\r\n  register(1001, parseByteAArray);\r\n  register(1005, parseIntegerArray); // _int2\r\n  register(1007, parseIntegerArray); // _int4\r\n  register(1028, parseIntegerArray); // oid[]\r\n  register(1016, parseBigIntegerArray); // _int8\r\n  register(1017, parsePointArray); // point[]\r\n  register(1021, parseFloatArray); // _float4\r\n  register(1022, parseFloatArray); // _float8\r\n  register(1231, parseFloatArray); // _numeric\r\n  register(1014, parseStringArray); //char\r\n  register(1015, parseStringArray); //varchar\r\n  register(1008, parseStringArray);\r\n  register(1009, parseStringArray);\r\n  register(1040, parseStringArray); // macaddr[]\r\n  register(1041, parseStringArray); // inet[]\r\n  register(1115, parseDateArray); // timestamp without time zone[]\r\n  register(1182, parseDateArray); // _date\r\n  register(1185, parseDateArray); // timestamp with time zone[]\r\n  register(1186, parseInterval);\r\n  register(1187, parseIntervalArray);\r\n  register(17, parseByteA);\r\n  register(114, JSON.parse.bind(JSON)); // json\r\n  register(3802, JSON.parse.bind(JSON)); // jsonb\r\n  register(199, parseJsonArray); // json[]\r\n  register(3807, parseJsonArray); // jsonb[]\r\n  register(3907, parseStringArray); // numrange[]\r\n  register(2951, parseStringArray); // uuid[]\r\n  register(791, parseStringArray); // money[]\r\n  register(1183, parseStringArray); // time[]\r\n  register(1270, parseStringArray); // timetz[]\r\n};\r\n\r\nmodule.exports = {\r\n  init: init\r\n};\r\n", "'use strict';\r\n\r\n// selected so (BASE - 1) * 0x100000000 + 0xffffffff is a safe integer\r\nvar BASE = 1000000;\r\n\r\nfunction readInt8(buffer) {\r\n\tvar high = buffer.readInt32BE(0);\r\n\tvar low = buffer.readUInt32BE(4);\r\n\tvar sign = '';\r\n\r\n\tif (high < 0) {\r\n\t\thigh = ~high + (low === 0);\r\n\t\tlow = (~low + 1) >>> 0;\r\n\t\tsign = '-';\r\n\t}\r\n\r\n\tvar result = '';\r\n\tvar carry;\r\n\tvar t;\r\n\tvar digits;\r\n\tvar pad;\r\n\tvar l;\r\n\tvar i;\r\n\r\n\t{\r\n\t\tcarry = high % BASE;\r\n\t\thigh = high / BASE >>> 0;\r\n\r\n\t\tt = 0x100000000 * carry + low;\r\n\t\tlow = t / BASE >>> 0;\r\n\t\tdigits = '' + (t - BASE * low);\r\n\r\n\t\tif (low === 0 && high === 0) {\r\n\t\t\treturn sign + digits + result;\r\n\t\t}\r\n\r\n\t\tpad = '';\r\n\t\tl = 6 - digits.length;\r\n\r\n\t\tfor (i = 0; i < l; i++) {\r\n\t\t\tpad += '0';\r\n\t\t}\r\n\r\n\t\tresult = pad + digits + result;\r\n\t}\r\n\r\n\t{\r\n\t\tcarry = high % BASE;\r\n\t\thigh = high / BASE >>> 0;\r\n\r\n\t\tt = 0x100000000 * carry + low;\r\n\t\tlow = t / BASE >>> 0;\r\n\t\tdigits = '' + (t - BASE * low);\r\n\r\n\t\tif (low === 0 && high === 0) {\r\n\t\t\treturn sign + digits + result;\r\n\t\t}\r\n\r\n\t\tpad = '';\r\n\t\tl = 6 - digits.length;\r\n\r\n\t\tfor (i = 0; i < l; i++) {\r\n\t\t\tpad += '0';\r\n\t\t}\r\n\r\n\t\tresult = pad + digits + result;\r\n\t}\r\n\r\n\t{\r\n\t\tcarry = high % BASE;\r\n\t\thigh = high / BASE >>> 0;\r\n\r\n\t\tt = 0x100000000 * carry + low;\r\n\t\tlow = t / BASE >>> 0;\r\n\t\tdigits = '' + (t - BASE * low);\r\n\r\n\t\tif (low === 0 && high === 0) {\r\n\t\t\treturn sign + digits + result;\r\n\t\t}\r\n\r\n\t\tpad = '';\r\n\t\tl = 6 - digits.length;\r\n\r\n\t\tfor (i = 0; i < l; i++) {\r\n\t\t\tpad += '0';\r\n\t\t}\r\n\r\n\t\tresult = pad + digits + result;\r\n\t}\r\n\r\n\t{\r\n\t\tcarry = high % BASE;\r\n\t\tt = 0x100000000 * carry + low;\r\n\t\tdigits = '' + t % BASE;\r\n\r\n\t\treturn sign + digits + result;\r\n\t}\r\n}\r\n\r\nmodule.exports = readInt8;\r\n", "var parseInt64 = require('pg-int8');\r\n\r\nvar parseBits = function(data, bits, offset, invert, callback) {\r\n  offset = offset || 0;\r\n  invert = invert || false;\r\n  callback = callback || function(lastValue, newValue, bits) { return (lastValue * Math.pow(2, bits)) + newValue; };\r\n  var offsetBytes = offset >> 3;\r\n\r\n  var inv = function(value) {\r\n    if (invert) {\r\n      return ~value & 0xff;\r\n    }\r\n\r\n    return value;\r\n  };\r\n\r\n  // read first (maybe partial) byte\r\n  var mask = 0xff;\r\n  var firstBits = 8 - (offset % 8);\r\n  if (bits < firstBits) {\r\n    mask = (0xff << (8 - bits)) & 0xff;\r\n    firstBits = bits;\r\n  }\r\n\r\n  if (offset) {\r\n    mask = mask >> (offset % 8);\r\n  }\r\n\r\n  var result = 0;\r\n  if ((offset % 8) + bits >= 8) {\r\n    result = callback(0, inv(data[offsetBytes]) & mask, firstBits);\r\n  }\r\n\r\n  // read bytes\r\n  var bytes = (bits + offset) >> 3;\r\n  for (var i = offsetBytes + 1; i < bytes; i++) {\r\n    result = callback(result, inv(data[i]), 8);\r\n  }\r\n\r\n  // bits to read, that are not a complete byte\r\n  var lastBits = (bits + offset) % 8;\r\n  if (lastBits > 0) {\r\n    result = callback(result, inv(data[bytes]) >> (8 - lastBits), lastBits);\r\n  }\r\n\r\n  return result;\r\n};\r\n\r\nvar parseFloatFromBits = function(data, precisionBits, exponentBits) {\r\n  var bias = Math.pow(2, exponentBits - 1) - 1;\r\n  var sign = parseBits(data, 1);\r\n  var exponent = parseBits(data, exponentBits, 1);\r\n\r\n  if (exponent === 0) {\r\n    return 0;\r\n  }\r\n\r\n  // parse mantissa\r\n  var precisionBitsCounter = 1;\r\n  var parsePrecisionBits = function(lastValue, newValue, bits) {\r\n    if (lastValue === 0) {\r\n      lastValue = 1;\r\n    }\r\n\r\n    for (var i = 1; i <= bits; i++) {\r\n      precisionBitsCounter /= 2;\r\n      if ((newValue & (0x1 << (bits - i))) > 0) {\r\n        lastValue += precisionBitsCounter;\r\n      }\r\n    }\r\n\r\n    return lastValue;\r\n  };\r\n\r\n  var mantissa = parseBits(data, precisionBits, exponentBits + 1, false, parsePrecisionBits);\r\n\r\n  // special cases\r\n  if (exponent == (Math.pow(2, exponentBits + 1) - 1)) {\r\n    if (mantissa === 0) {\r\n      return (sign === 0) ? Infinity : -Infinity;\r\n    }\r\n\r\n    return NaN;\r\n  }\r\n\r\n  // normale number\r\n  return ((sign === 0) ? 1 : -1) * Math.pow(2, exponent - bias) * mantissa;\r\n};\r\n\r\nvar parseInt16 = function(value) {\r\n  if (parseBits(value, 1) == 1) {\r\n    return -1 * (parseBits(value, 15, 1, true) + 1);\r\n  }\r\n\r\n  return parseBits(value, 15, 1);\r\n};\r\n\r\nvar parseInt32 = function(value) {\r\n  if (parseBits(value, 1) == 1) {\r\n    return -1 * (parseBits(value, 31, 1, true) + 1);\r\n  }\r\n\r\n  return parseBits(value, 31, 1);\r\n};\r\n\r\nvar parseFloat32 = function(value) {\r\n  return parseFloatFromBits(value, 23, 8);\r\n};\r\n\r\nvar parseFloat64 = function(value) {\r\n  return parseFloatFromBits(value, 52, 11);\r\n};\r\n\r\nvar parseNumeric = function(value) {\r\n  var sign = parseBits(value, 16, 32);\r\n  if (sign == 0xc000) {\r\n    return NaN;\r\n  }\r\n\r\n  var weight = Math.pow(10000, parseBits(value, 16, 16));\r\n  var result = 0;\r\n\r\n  var digits = [];\r\n  var ndigits = parseBits(value, 16);\r\n  for (var i = 0; i < ndigits; i++) {\r\n    result += parseBits(value, 16, 64 + (16 * i)) * weight;\r\n    weight /= 10000;\r\n  }\r\n\r\n  var scale = Math.pow(10, parseBits(value, 16, 48));\r\n  return ((sign === 0) ? 1 : -1) * Math.round(result * scale) / scale;\r\n};\r\n\r\nvar parseDate = function(isUTC, value) {\r\n  var sign = parseBits(value, 1);\r\n  var rawValue = parseBits(value, 63, 1);\r\n\r\n  // discard usecs and shift from 2000 to 1970\r\n  var result = new Date((((sign === 0) ? 1 : -1) * rawValue / 1000) + 946684800000);\r\n\r\n  if (!isUTC) {\r\n    result.setTime(result.getTime() + result.getTimezoneOffset() * 60000);\r\n  }\r\n\r\n  // add microseconds to the date\r\n  result.usec = rawValue % 1000;\r\n  result.getMicroSeconds = function() {\r\n    return this.usec;\r\n  };\r\n  result.setMicroSeconds = function(value) {\r\n    this.usec = value;\r\n  };\r\n  result.getUTCMicroSeconds = function() {\r\n    return this.usec;\r\n  };\r\n\r\n  return result;\r\n};\r\n\r\nvar parseArray = function(value) {\r\n  var dim = parseBits(value, 32);\r\n\r\n  var flags = parseBits(value, 32, 32);\r\n  var elementType = parseBits(value, 32, 64);\r\n\r\n  var offset = 96;\r\n  var dims = [];\r\n  for (var i = 0; i < dim; i++) {\r\n    // parse dimension\r\n    dims[i] = parseBits(value, 32, offset);\r\n    offset += 32;\r\n\r\n    // ignore lower bounds\r\n    offset += 32;\r\n  }\r\n\r\n  var parseElement = function(elementType) {\r\n    // parse content length\r\n    var length = parseBits(value, 32, offset);\r\n    offset += 32;\r\n\r\n    // parse null values\r\n    if (length == 0xffffffff) {\r\n      return null;\r\n    }\r\n\r\n    var result;\r\n    if ((elementType == 0x17) || (elementType == 0x14)) {\r\n      // int/bigint\r\n      result = parseBits(value, length * 8, offset);\r\n      offset += length * 8;\r\n      return result;\r\n    }\r\n    else if (elementType == 0x19) {\r\n      // string\r\n      result = value.toString(this.encoding, offset >> 3, (offset += (length << 3)) >> 3);\r\n      return result;\r\n    }\r\n    else {\r\n      console.log(\"ERROR: ElementType not implemented: \" + elementType);\r\n    }\r\n  };\r\n\r\n  var parse = function(dimension, elementType) {\r\n    var array = [];\r\n    var i;\r\n\r\n    if (dimension.length > 1) {\r\n      var count = dimension.shift();\r\n      for (i = 0; i < count; i++) {\r\n        array[i] = parse(dimension, elementType);\r\n      }\r\n      dimension.unshift(count);\r\n    }\r\n    else {\r\n      for (i = 0; i < dimension[0]; i++) {\r\n        array[i] = parseElement(elementType);\r\n      }\r\n    }\r\n\r\n    return array;\r\n  };\r\n\r\n  return parse(dims, elementType);\r\n};\r\n\r\nvar parseText = function(value) {\r\n  return value.toString('utf8');\r\n};\r\n\r\nvar parseBool = function(value) {\r\n  if(value === null) return null;\r\n  return (parseBits(value, 8) > 0);\r\n};\r\n\r\nvar init = function(register) {\r\n  register(20, parseInt64);\r\n  register(21, parseInt16);\r\n  register(23, parseInt32);\r\n  register(26, parseInt32);\r\n  register(1700, parseNumeric);\r\n  register(700, parseFloat32);\r\n  register(701, parseFloat64);\r\n  register(16, parseBool);\r\n  register(1114, parseDate.bind(null, false));\r\n  register(1184, parseDate.bind(null, true));\r\n  register(1000, parseArray);\r\n  register(1007, parseArray);\r\n  register(1016, parseArray);\r\n  register(1008, parseArray);\r\n  register(1009, parseArray);\r\n  register(25, parseText);\r\n};\r\n\r\nmodule.exports = {\r\n  init: init\r\n};\r\n", "/**\r\n * Following query was used to generate this file:\r\n\r\n SELECT json_object_agg(UPPER(PT.typname), PT.oid::int4 ORDER BY pt.oid)\r\n FROM pg_type PT\r\n WHERE typnamespace = (SELECT pgn.oid FROM pg_namespace pgn WHERE nspname = 'pg_catalog') -- Take only builting Postgres types with stable OID (extension types are not guaranted to be stable)\r\n AND typtype = 'b' -- Only basic types\r\n AND typelem = 0 -- Ignore aliases\r\n AND typisdefined -- Ignore undefined types\r\n */\r\n\r\nmodule.exports = {\r\n    BOOL: 16,\r\n    BYTEA: 17,\r\n    CHAR: 18,\r\n    INT8: 20,\r\n    INT2: 21,\r\n    INT4: 23,\r\n    REGPROC: 24,\r\n    TEXT: 25,\r\n    OID: 26,\r\n    TID: 27,\r\n    XID: 28,\r\n    CID: 29,\r\n    JSON: 114,\r\n    XML: 142,\r\n    PG_NODE_TREE: 194,\r\n    SMGR: 210,\r\n    PATH: 602,\r\n    POLYGON: 604,\r\n    CIDR: 650,\r\n    FLOAT4: 700,\r\n    FLOAT8: 701,\r\n    ABSTIME: 702,\r\n    RELTIME: 703,\r\n    TINTERVAL: 704,\r\n    CIRCLE: 718,\r\n    MACADDR8: 774,\r\n    MONEY: 790,\r\n    MACADDR: 829,\r\n    INET: 869,\r\n    ACLITEM: 1033,\r\n    BPCHAR: 1042,\r\n    VARCHAR: 1043,\r\n    DATE: 1082,\r\n    TIME: 1083,\r\n    TIMESTAMP: 1114,\r\n    TIMESTAMPTZ: 1184,\r\n    INTERVAL: 1186,\r\n    TIMETZ: 1266,\r\n    BIT: 1560,\r\n    VARBIT: 1562,\r\n    NUMERIC: 1700,\r\n    REFCURSOR: 1790,\r\n    REGPROCEDURE: 2202,\r\n    REGOPER: 2203,\r\n    REGOPERATOR: 2204,\r\n    REGCLASS: 2205,\r\n    REGTYPE: 2206,\r\n    UUID: 2950,\r\n    TXID_SNAPSHOT: 2970,\r\n    PG_LSN: 3220,\r\n    PG_NDISTINCT: 3361,\r\n    PG_DEPENDENCIES: 3402,\r\n    TSVECTOR: 3614,\r\n    TSQUERY: 3615,\r\n    GTSVECTOR: 3642,\r\n    REGCONFIG: 3734,\r\n    REGDICTIONARY: 3769,\r\n    JSONB: 3802,\r\n    REGNAMESPACE: 4089,\r\n    REGROLE: 4096\r\n};\r\n", "var textParsers = require('./lib/textParsers');\r\nvar binaryParsers = require('./lib/binaryParsers');\r\nvar arrayParser = require('./lib/arrayParser');\r\nvar builtinTypes = require('./lib/builtins');\r\n\r\nexports.getTypeParser = getTypeParser;\r\nexports.setTypeParser = setTypeParser;\r\nexports.arrayParser = arrayParser;\r\nexports.builtins = builtinTypes;\r\n\r\nvar typeParsers = {\r\n  text: {},\r\n  binary: {}\r\n};\r\n\r\n//the empty parse function\r\nfunction noParse (val) {\r\n  return String(val);\r\n};\r\n\r\n//returns a function used to convert a specific type (specified by\r\n//oid) into a result javascript type\r\n//note: the oid can be obtained via the following sql query:\r\n//SELECT oid FROM pg_type WHERE typname = 'TYPE_NAME_HERE';\r\nfunction getTypeParser (oid, format) {\r\n  format = format || 'text';\r\n  if (!typeParsers[format]) {\r\n    return noParse;\r\n  }\r\n  return typeParsers[format][oid] || noParse;\r\n};\r\n\r\nfunction setTypeParser (oid, format, parseFn) {\r\n  if(typeof format == 'function') {\r\n    parseFn = format;\r\n    format = 'text';\r\n  }\r\n  typeParsers[format][oid] = parseFn;\r\n};\r\n\r\ntextParsers.init(function(oid, converter) {\r\n  typeParsers.text[oid] = converter;\r\n});\r\n\r\nbinaryParsers.init(function(oid, converter) {\r\n  typeParsers.binary[oid] = converter;\r\n});\r\n", "'use strict'\r\n\r\nmodule.exports = {\r\n  // database host. defaults to localhost\r\n  host: 'localhost',\r\n\r\n  // database user's name\r\n  user: process.platform === 'win32' ? process.env.USERNAME : process.env.USER,\r\n\r\n  // name of database to connect\r\n  database: undefined,\r\n\r\n  // database user's password\r\n  password: null,\r\n\r\n  // a Postgres connection string to be used instead of setting individual connection items\r\n  // NOTE:  Setting this value will cause it to override any other value (such as database or user) defined\r\n  // in the defaults object.\r\n  connectionString: undefined,\r\n\r\n  // database port\r\n  port: 5432,\r\n\r\n  // number of rows to return at a time from a prepared statement's\r\n  // portal. 0 will return all rows at once\r\n  rows: 0,\r\n\r\n  // binary result mode\r\n  binary: false,\r\n\r\n  // Connection pool options - see https://github.com/brianc/node-pg-pool\r\n\r\n  // number of connections to use in connection pool\r\n  // 0 will disable connection pooling\r\n  max: 10,\r\n\r\n  // max milliseconds a client can go unused before it is removed\r\n  // from the pool and destroyed\r\n  idleTimeoutMillis: 30000,\r\n\r\n  client_encoding: '',\r\n\r\n  ssl: false,\r\n\r\n  application_name: undefined,\r\n\r\n  fallback_application_name: undefined,\r\n\r\n  options: undefined,\r\n\r\n  parseInputDatesAsUTC: false,\r\n\r\n  // max milliseconds any query using this connection will execute for before timing out in error.\r\n  // false=unlimited\r\n  statement_timeout: false,\r\n\r\n  // Abort any statement that waits longer than the specified duration in milliseconds while attempting to acquire a lock.\r\n  // false=unlimited\r\n  lock_timeout: false,\r\n\r\n  // Terminate any session with an open transaction that has been idle for longer than the specified duration in milliseconds\r\n  // false=unlimited\r\n  idle_in_transaction_session_timeout: false,\r\n\r\n  // max milliseconds to wait for query to complete (client side)\r\n  query_timeout: false,\r\n\r\n  connect_timeout: 0,\r\n\r\n  keepalives: 1,\r\n\r\n  keepalives_idle: 0,\r\n}\r\n\r\nconst pgTypes = require('pg-types')\r\n// save default parsers\r\nconst parseBigInteger = pgTypes.getTypeParser(20, 'text')\r\nconst parseBigIntegerArray = pgTypes.getTypeParser(1016, 'text')\r\n\r\n// parse int8 so you can get your count values as actual numbers\r\nmodule.exports.__defineSetter__('parseInt8', function (val) {\r\n  pgTypes.setTypeParser(20, 'text', val ? pgTypes.getTypeParser(23, 'text') : parseBigInteger)\r\n  pgTypes.setTypeParser(1016, 'text', val ? pgTypes.getTypeParser(1007, 'text') : parseBigIntegerArray)\r\n})\r\n", "'use strict'\r\n\r\nconst defaults = require('./defaults')\r\n\r\nconst util = require('util')\r\nconst { isDate } = util.types || util // Node 8 doesn't have `util.types`\r\n\r\nfunction escapeElement(elementRepresentation) {\r\n  const escaped = elementRepresentation.replace(/\\\\/g, '\\\\\\\\').replace(/\"/g, '\\\\\"')\r\n\r\n  return '\"' + escaped + '\"'\r\n}\r\n\r\n// convert a JS array to a postgres array literal\r\n// uses comma separator so won't work for types like box that use\r\n// a different array separator.\r\nfunction arrayString(val) {\r\n  let result = '{'\r\n  for (let i = 0; i < val.length; i++) {\r\n    if (i > 0) {\r\n      result = result + ','\r\n    }\r\n    if (val[i] === null || typeof val[i] === 'undefined') {\r\n      result = result + 'NULL'\r\n    } else if (Array.isArray(val[i])) {\r\n      result = result + arrayString(val[i])\r\n    } else if (ArrayBuffer.isView(val[i])) {\r\n      let item = val[i]\r\n      if (!(item instanceof Buffer)) {\r\n        const buf = Buffer.from(item.buffer, item.byteOffset, item.byteLength)\r\n        if (buf.length === item.byteLength) {\r\n          item = buf\r\n        } else {\r\n          item = buf.slice(item.byteOffset, item.byteOffset + item.byteLength)\r\n        }\r\n      }\r\n      result += '\\\\\\\\x' + item.toString('hex')\r\n    } else {\r\n      result += escapeElement(prepareValue(val[i]))\r\n    }\r\n  }\r\n  result = result + '}'\r\n  return result\r\n}\r\n\r\n// converts values from javascript types\r\n// to their 'raw' counterparts for use as a postgres parameter\r\n// note: you can override this function to provide your own conversion mechanism\r\n// for complex types, etc...\r\nconst prepareValue = function (val, seen) {\r\n  // null and undefined are both null for postgres\r\n  if (val == null) {\r\n    return null\r\n  }\r\n  if (typeof val === 'object') {\r\n    if (val instanceof Buffer) {\r\n      return val\r\n    }\r\n    if (ArrayBuffer.isView(val)) {\r\n      const buf = Buffer.from(val.buffer, val.byteOffset, val.byteLength)\r\n      if (buf.length === val.byteLength) {\r\n        return buf\r\n      }\r\n      return buf.slice(val.byteOffset, val.byteOffset + val.byteLength) // Node.js v4 does not support those Buffer.from params\r\n    }\r\n    if (isDate(val)) {\r\n      if (defaults.parseInputDatesAsUTC) {\r\n        return dateToStringUTC(val)\r\n      } else {\r\n        return dateToString(val)\r\n      }\r\n    }\r\n    if (Array.isArray(val)) {\r\n      return arrayString(val)\r\n    }\r\n\r\n    return prepareObject(val, seen)\r\n  }\r\n  return val.toString()\r\n}\r\n\r\nfunction prepareObject(val, seen) {\r\n  if (val && typeof val.toPostgres === 'function') {\r\n    seen = seen || []\r\n    if (seen.indexOf(val) !== -1) {\r\n      throw new Error('circular reference detected while preparing \"' + val + '\" for query')\r\n    }\r\n    seen.push(val)\r\n\r\n    return prepareValue(val.toPostgres(prepareValue), seen)\r\n  }\r\n  return JSON.stringify(val)\r\n}\r\n\r\nfunction dateToString(date) {\r\n  let offset = -date.getTimezoneOffset()\r\n\r\n  let year = date.getFullYear()\r\n  const isBCYear = year < 1\r\n  if (isBCYear) year = Math.abs(year) + 1 // negative years are 1 off their BC representation\r\n\r\n  let ret =\r\n    String(year).padStart(4, '0') +\r\n    '-' +\r\n    String(date.getMonth() + 1).padStart(2, '0') +\r\n    '-' +\r\n    String(date.getDate()).padStart(2, '0') +\r\n    'T' +\r\n    String(date.getHours()).padStart(2, '0') +\r\n    ':' +\r\n    String(date.getMinutes()).padStart(2, '0') +\r\n    ':' +\r\n    String(date.getSeconds()).padStart(2, '0') +\r\n    '.' +\r\n    String(date.getMilliseconds()).padStart(3, '0')\r\n\r\n  if (offset < 0) {\r\n    ret += '-'\r\n    offset *= -1\r\n  } else {\r\n    ret += '+'\r\n  }\r\n\r\n  ret += String(Math.floor(offset / 60)).padStart(2, '0') + ':' + String(offset % 60).padStart(2, '0')\r\n  if (isBCYear) ret += ' BC'\r\n  return ret\r\n}\r\n\r\nfunction dateToStringUTC(date) {\r\n  let year = date.getUTCFullYear()\r\n  const isBCYear = year < 1\r\n  if (isBCYear) year = Math.abs(year) + 1 // negative years are 1 off their BC representation\r\n\r\n  let ret =\r\n    String(year).padStart(4, '0') +\r\n    '-' +\r\n    String(date.getUTCMonth() + 1).padStart(2, '0') +\r\n    '-' +\r\n    String(date.getUTCDate()).padStart(2, '0') +\r\n    'T' +\r\n    String(date.getUTCHours()).padStart(2, '0') +\r\n    ':' +\r\n    String(date.getUTCMinutes()).padStart(2, '0') +\r\n    ':' +\r\n    String(date.getUTCSeconds()).padStart(2, '0') +\r\n    '.' +\r\n    String(date.getUTCMilliseconds()).padStart(3, '0')\r\n\r\n  ret += '+00:00'\r\n  if (isBCYear) ret += ' BC'\r\n  return ret\r\n}\r\n\r\nfunction normalizeQueryConfig(config, values, callback) {\r\n  // can take in strings or config objects\r\n  config = typeof config === 'string' ? { text: config } : config\r\n  if (values) {\r\n    if (typeof values === 'function') {\r\n      config.callback = values\r\n    } else {\r\n      config.values = values\r\n    }\r\n  }\r\n  if (callback) {\r\n    config.callback = callback\r\n  }\r\n  return config\r\n}\r\n\r\n// Ported from PostgreSQL 9.2.4 source code in src/interfaces/libpq/fe-exec.c\r\nconst escapeIdentifier = function (str) {\r\n  return '\"' + str.replace(/\"/g, '\"\"') + '\"'\r\n}\r\n\r\nconst escapeLiteral = function (str) {\r\n  let hasBackslash = false\r\n  let escaped = \"'\"\r\n\r\n  if (str == null) {\r\n    return \"''\"\r\n  }\r\n\r\n  if (typeof str !== 'string') {\r\n    return \"''\"\r\n  }\r\n\r\n  for (let i = 0; i < str.length; i++) {\r\n    const c = str[i]\r\n    if (c === \"'\") {\r\n      escaped += c + c\r\n    } else if (c === '\\\\') {\r\n      escaped += c + c\r\n      hasBackslash = true\r\n    } else {\r\n      escaped += c\r\n    }\r\n  }\r\n\r\n  escaped += \"'\"\r\n\r\n  if (hasBackslash === true) {\r\n    escaped = ' E' + escaped\r\n  }\r\n\r\n  return escaped\r\n}\r\n\r\nmodule.exports = {\r\n  prepareValue: function prepareValueWrapper(value) {\r\n    // this ensures that extra arguments do not get passed into prepareValue\r\n    // by accident, eg: from calling values.map(utils.prepareValue)\r\n    return prepareValue(value)\r\n  },\r\n  normalizeQueryConfig,\r\n  escapeIdentifier,\r\n  escapeLiteral,\r\n}\r\n", "'use strict'\r\n// This file contains crypto utility functions for versions of Node.js < 15.0.0,\r\n// which does not support the WebCrypto.subtle API.\r\n\r\nconst nodeCrypto = require('crypto')\r\n\r\nfunction md5(string) {\r\n  return nodeCrypto.createHash('md5').update(string, 'utf-8').digest('hex')\r\n}\r\n\r\n// See AuthenticationMD5Password at https://www.postgresql.org/docs/current/static/protocol-flow.html\r\nfunction postgresMd5PasswordHash(user, password, salt) {\r\n  const inner = md5(password + user)\r\n  const outer = md5(Buffer.concat([Buffer.from(inner), salt]))\r\n  return 'md5' + outer\r\n}\r\n\r\nfunction sha256(text) {\r\n  return nodeCrypto.createHash('sha256').update(text).digest()\r\n}\r\n\r\nfunction hashByName(hashName, text) {\r\n  hashName = hashName.replace(/(\\D)-/, '$1') // e.g. SHA-256 -> SHA256\r\n  return nodeCrypto.createHash(hashName).update(text).digest()\r\n}\r\n\r\nfunction hmacSha256(key, msg) {\r\n  return nodeCrypto.createHmac('sha256', key).update(msg).digest()\r\n}\r\n\r\nasync function deriveKey(password, salt, iterations) {\r\n  return nodeCrypto.pbkdf2Sync(password, salt, iterations, 32, 'sha256')\r\n}\r\n\r\nmodule.exports = {\r\n  postgresMd5PasswordHash,\r\n  randomBytes: nodeCrypto.randomBytes,\r\n  deriveKey,\r\n  sha256,\r\n  hashByName,\r\n  hmacSha256,\r\n  md5,\r\n}\r\n", "const nodeCrypto = require('crypto')\r\n\r\nmodule.exports = {\r\n  postgresMd5PasswordHash,\r\n  randomBytes,\r\n  deriveKey,\r\n  sha256,\r\n  hashByName,\r\n  hmacSha256,\r\n  md5,\r\n}\r\n\r\n/**\r\n * The Web Crypto API - grabbed from the Node.js library or the global\r\n * @type Crypto\r\n */\r\n// eslint-disable-next-line no-undef\r\nconst webCrypto = nodeCrypto.webcrypto || globalThis.crypto\r\n/**\r\n * The SubtleCrypto API for low level crypto operations.\r\n * @type SubtleCrypto\r\n */\r\nconst subtleCrypto = webCrypto.subtle\r\nconst textEncoder = new TextEncoder()\r\n\r\n/**\r\n *\r\n * @param {*} length\r\n * @returns\r\n */\r\nfunction randomBytes(length) {\r\n  return webCrypto.getRandomValues(Buffer.alloc(length))\r\n}\r\n\r\nasync function md5(string) {\r\n  try {\r\n    return nodeCrypto.createHash('md5').update(string, 'utf-8').digest('hex')\r\n  } catch (e) {\r\n    // `createHash()` failed so we are probably not in Node.js, use the WebCrypto API instead.\r\n    // Note that the MD5 algorithm on WebCrypto is not available in Node.js.\r\n    // This is why we cannot just use WebCrypto in all environments.\r\n    const data = typeof string === 'string' ? textEncoder.encode(string) : string\r\n    const hash = await subtleCrypto.digest('MD5', data)\r\n    return Array.from(new Uint8Array(hash))\r\n      .map((b) => b.toString(16).padStart(2, '0'))\r\n      .join('')\r\n  }\r\n}\r\n\r\n// See AuthenticationMD5Password at https://www.postgresql.org/docs/current/static/protocol-flow.html\r\nasync function postgresMd5PasswordHash(user, password, salt) {\r\n  const inner = await md5(password + user)\r\n  const outer = await md5(Buffer.concat([Buffer.from(inner), salt]))\r\n  return 'md5' + outer\r\n}\r\n\r\n/**\r\n * Create a SHA-256 digest of the given data\r\n * @param {Buffer} data\r\n */\r\nasync function sha256(text) {\r\n  return await subtleCrypto.digest('SHA-256', text)\r\n}\r\n\r\nasync function hashByName(hashName, text) {\r\n  return await subtleCrypto.digest(hashName, text)\r\n}\r\n\r\n/**\r\n * Sign the message with the given key\r\n * @param {ArrayBuffer} keyBuffer\r\n * @param {string} msg\r\n */\r\nasync function hmacSha256(keyBuffer, msg) {\r\n  const key = await subtleCrypto.importKey('raw', keyBuffer, { name: 'HMAC', hash: 'SHA-256' }, false, ['sign'])\r\n  return await subtleCrypto.sign('HMAC', key, textEncoder.encode(msg))\r\n}\r\n\r\n/**\r\n * Derive a key from the password and salt\r\n * @param {string} password\r\n * @param {Uint8Array} salt\r\n * @param {number} iterations\r\n */\r\nasync function deriveKey(password, salt, iterations) {\r\n  const key = await subtleCrypto.importKey('raw', textEncoder.encode(password), 'PBKDF2', false, ['deriveBits'])\r\n  const params = { name: 'PBKDF2', hash: 'SHA-256', salt: salt, iterations: iterations }\r\n  return await subtleCrypto.deriveBits(params, key, 32 * 8, ['deriveBits'])\r\n}\r\n", "'use strict'\r\n\r\nconst useLegacyCrypto = parseInt(process.versions && process.versions.node && process.versions.node.split('.')[0]) < 15\r\nif (useLegacyCrypto) {\r\n  // We are on an old version of Node.js that requires legacy crypto utilities.\r\n  module.exports = require('./utils-legacy')\r\n} else {\r\n  module.exports = require('./utils-webcrypto')\r\n}\r\n", "function x509Error(msg, cert) {\r\n  return new Error('SASL channel binding: ' + msg + ' when parsing public certificate ' + cert.toString('base64'))\r\n}\r\n\r\nfunction readASN1Length(data, index) {\r\n  let length = data[index++]\r\n  if (length < 0x80) return { length, index }\r\n\r\n  const lengthBytes = length & 0x7f\r\n  if (lengthBytes > 4) throw x509Error('bad length', data)\r\n\r\n  length = 0\r\n  for (let i = 0; i < lengthBytes; i++) {\r\n    length = (length << 8) | data[index++]\r\n  }\r\n\r\n  return { length, index }\r\n}\r\n\r\nfunction readASN1OID(data, index) {\r\n  if (data[index++] !== 0x6) throw x509Error('non-OID data', data) // 6 = OID\r\n\r\n  const { length: OIDLength, index: indexAfterOIDLength } = readASN1Length(data, index)\r\n  index = indexAfterOIDLength\r\n  const lastIndex = index + OIDLength\r\n\r\n  const byte1 = data[index++]\r\n  let oid = ((byte1 / 40) >> 0) + '.' + (byte1 % 40)\r\n\r\n  while (index < lastIndex) {\r\n    // loop over numbers in OID\r\n    let value = 0\r\n    while (index < lastIndex) {\r\n      // loop over bytes in number\r\n      const nextByte = data[index++]\r\n      value = (value << 7) | (nextByte & 0x7f)\r\n      if (nextByte < 0x80) break\r\n    }\r\n    oid += '.' + value\r\n  }\r\n\r\n  return { oid, index }\r\n}\r\n\r\nfunction expectASN1Seq(data, index) {\r\n  if (data[index++] !== 0x30) throw x509Error('non-sequence data', data) // 30 = Sequence\r\n  return readASN1Length(data, index)\r\n}\r\n\r\nfunction signatureAlgorithmHashFromCertificate(data, index) {\r\n  // read this thread: https://www.postgresql.org/message-id/17760-b6c61e752ec07060%40postgresql.org\r\n  if (index === undefined) index = 0\r\n  index = expectASN1Seq(data, index).index\r\n  const { length: certInfoLength, index: indexAfterCertInfoLength } = expectASN1Seq(data, index)\r\n  index = indexAfterCertInfoLength + certInfoLength // skip over certificate info\r\n  index = expectASN1Seq(data, index).index // skip over signature length field\r\n  const { oid, index: indexAfterOID } = readASN1OID(data, index)\r\n  switch (oid) {\r\n    // RSA\r\n    case '1.2.840.113549.1.1.4':\r\n      return 'MD5'\r\n    case '1.2.840.113549.1.1.5':\r\n      return 'SHA-1'\r\n    case '1.2.840.113549.1.1.11':\r\n      return 'SHA-256'\r\n    case '1.2.840.113549.1.1.12':\r\n      return 'SHA-384'\r\n    case '1.2.840.113549.1.1.13':\r\n      return 'SHA-512'\r\n    case '1.2.840.113549.1.1.14':\r\n      return 'SHA-224'\r\n    case '1.2.840.113549.1.1.15':\r\n      return 'SHA512-224'\r\n    case '1.2.840.113549.1.1.16':\r\n      return 'SHA512-256'\r\n    // ECDSA\r\n    case '1.2.840.10045.4.1':\r\n      return 'SHA-1'\r\n    case '1.2.840.10045.4.3.1':\r\n      return 'SHA-224'\r\n    case '1.2.840.10045.4.3.2':\r\n      return 'SHA-256'\r\n    case '1.2.840.10045.4.3.3':\r\n      return 'SHA-384'\r\n    case '1.2.840.10045.4.3.4':\r\n      return 'SHA-512'\r\n    // RSASSA-PSS: hash is indicated separately\r\n    case '1.2.840.113549.1.1.10': {\r\n      index = indexAfterOID\r\n      index = expectASN1Seq(data, index).index\r\n      if (data[index++] !== 0xa0) throw x509Error('non-tag data', data) // a0 = constructed tag 0\r\n      index = readASN1Length(data, index).index // skip over tag length field\r\n      index = expectASN1Seq(data, index).index // skip over sequence length field\r\n      const { oid: hashOID } = readASN1OID(data, index)\r\n      switch (hashOID) {\r\n        // standalone hash OIDs\r\n        case '1.2.840.113549.2.5':\r\n          return 'MD5'\r\n        case '********.2.26':\r\n          return 'SHA-1'\r\n        case '2.16.840.*********.2.1':\r\n          return 'SHA-256'\r\n        case '2.16.840.*********.2.2':\r\n          return 'SHA-384'\r\n        case '2.16.840.*********.2.3':\r\n          return 'SHA-512'\r\n      }\r\n      throw x509Error('unknown hash OID ' + hashOID, data)\r\n    }\r\n    // Ed25519 -- see https: return//github.com/openssl/openssl/issues/15477\r\n    case '***********':\r\n    case '***********': // ph\r\n      return 'SHA-512'\r\n    // Ed448 -- still not in pg 17.2 (if supported, digest would be SHAKE256 x 64 bytes)\r\n    case '***********':\r\n    case '***********': // ph\r\n      throw x509Error('Ed448 certificate channel binding is not currently supported by Postgres')\r\n  }\r\n  throw x509Error('unknown OID ' + oid, data)\r\n}\r\n\r\nmodule.exports = { signatureAlgorithmHashFromCertificate }\r\n", "'use strict'\r\nconst crypto = require('./utils')\r\nconst { signatureAlgorithmHashFromCertificate } = require('./cert-signatures')\r\n\r\nfunction startSession(mechanisms, stream) {\r\n  const candidates = ['SCRAM-SHA-256']\r\n  if (stream) candidates.unshift('SCRAM-SHA-256-PLUS') // higher-priority, so placed first\r\n\r\n  const mechanism = candidates.find((candidate) => mechanisms.includes(candidate))\r\n\r\n  if (!mechanism) {\r\n    throw new Error('SASL: Only mechanism(s) ' + candidates.join(' and ') + ' are supported')\r\n  }\r\n\r\n  if (mechanism === 'SCRAM-SHA-256-PLUS' && typeof stream.getPeerCertificate !== 'function') {\r\n    // this should never happen if we are really talking to a Postgres server\r\n    throw new Error('SASL: Mechanism SCRAM-SHA-256-PLUS requires a certificate')\r\n  }\r\n\r\n  const clientNonce = crypto.randomBytes(18).toString('base64')\r\n  const gs2Header = mechanism === 'SCRAM-SHA-256-PLUS' ? 'p=tls-server-end-point' : stream ? 'y' : 'n'\r\n\r\n  return {\r\n    mechanism,\r\n    clientNonce,\r\n    response: gs2Header + ',,n=*,r=' + clientNonce,\r\n    message: 'SASLInitialResponse',\r\n  }\r\n}\r\n\r\nasync function continueSession(session, password, serverData, stream) {\r\n  if (session.message !== 'SASLInitialResponse') {\r\n    throw new Error('SASL: Last message was not SASLInitialResponse')\r\n  }\r\n  if (typeof password !== 'string') {\r\n    throw new Error('SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string')\r\n  }\r\n  if (password === '') {\r\n    throw new Error('SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a non-empty string')\r\n  }\r\n  if (typeof serverData !== 'string') {\r\n    throw new Error('SASL: SCRAM-SERVER-FIRST-MESSAGE: serverData must be a string')\r\n  }\r\n\r\n  const sv = parseServerFirstMessage(serverData)\r\n\r\n  if (!sv.nonce.startsWith(session.clientNonce)) {\r\n    throw new Error('SASL: SCRAM-SERVER-FIRST-MESSAGE: server nonce does not start with client nonce')\r\n  } else if (sv.nonce.length === session.clientNonce.length) {\r\n    throw new Error('SASL: SCRAM-SERVER-FIRST-MESSAGE: server nonce is too short')\r\n  }\r\n\r\n  const clientFirstMessageBare = 'n=*,r=' + session.clientNonce\r\n  const serverFirstMessage = 'r=' + sv.nonce + ',s=' + sv.salt + ',i=' + sv.iteration\r\n\r\n  // without channel binding:\r\n  let channelBinding = stream ? 'eSws' : 'biws' // 'y,,' or 'n,,', base64-encoded\r\n\r\n  // override if channel binding is in use:\r\n  if (session.mechanism === 'SCRAM-SHA-256-PLUS') {\r\n    const peerCert = stream.getPeerCertificate().raw\r\n    let hashName = signatureAlgorithmHashFromCertificate(peerCert)\r\n    if (hashName === 'MD5' || hashName === 'SHA-1') hashName = 'SHA-256'\r\n    const certHash = await crypto.hashByName(hashName, peerCert)\r\n    const bindingData = Buffer.concat([Buffer.from('p=tls-server-end-point,,'), Buffer.from(certHash)])\r\n    channelBinding = bindingData.toString('base64')\r\n  }\r\n\r\n  const clientFinalMessageWithoutProof = 'c=' + channelBinding + ',r=' + sv.nonce\r\n  const authMessage = clientFirstMessageBare + ',' + serverFirstMessage + ',' + clientFinalMessageWithoutProof\r\n\r\n  const saltBytes = Buffer.from(sv.salt, 'base64')\r\n  const saltedPassword = await crypto.deriveKey(password, saltBytes, sv.iteration)\r\n  const clientKey = await crypto.hmacSha256(saltedPassword, 'Client Key')\r\n  const storedKey = await crypto.sha256(clientKey)\r\n  const clientSignature = await crypto.hmacSha256(storedKey, authMessage)\r\n  const clientProof = xorBuffers(Buffer.from(clientKey), Buffer.from(clientSignature)).toString('base64')\r\n  const serverKey = await crypto.hmacSha256(saltedPassword, 'Server Key')\r\n  const serverSignatureBytes = await crypto.hmacSha256(serverKey, authMessage)\r\n\r\n  session.message = 'SASLResponse'\r\n  session.serverSignature = Buffer.from(serverSignatureBytes).toString('base64')\r\n  session.response = clientFinalMessageWithoutProof + ',p=' + clientProof\r\n}\r\n\r\nfunction finalizeSession(session, serverData) {\r\n  if (session.message !== 'SASLResponse') {\r\n    throw new Error('SASL: Last message was not SASLResponse')\r\n  }\r\n  if (typeof serverData !== 'string') {\r\n    throw new Error('SASL: SCRAM-SERVER-FINAL-MESSAGE: serverData must be a string')\r\n  }\r\n\r\n  const { serverSignature } = parseServerFinalMessage(serverData)\r\n\r\n  if (serverSignature !== session.serverSignature) {\r\n    throw new Error('SASL: SCRAM-SERVER-FINAL-MESSAGE: server signature does not match')\r\n  }\r\n}\r\n\r\n/**\r\n * printable       = %x21-2B / %x2D-7E\r\n *                   ;; Printable ASCII except \",\".\r\n *                   ;; Note that any \"printable\" is also\r\n *                   ;; a valid \"value\".\r\n */\r\nfunction isPrintableChars(text) {\r\n  if (typeof text !== 'string') {\r\n    throw new TypeError('SASL: text must be a string')\r\n  }\r\n  return text\r\n    .split('')\r\n    .map((_, i) => text.charCodeAt(i))\r\n    .every((c) => (c >= 0x21 && c <= 0x2b) || (c >= 0x2d && c <= 0x7e))\r\n}\r\n\r\n/**\r\n * base64-char     = ALPHA / DIGIT / \"/\" / \"+\"\r\n *\r\n * base64-4        = 4base64-char\r\n *\r\n * base64-3        = 3base64-char \"=\"\r\n *\r\n * base64-2        = 2base64-char \"==\"\r\n *\r\n * base64          = *base64-4 [base64-3 / base64-2]\r\n */\r\nfunction isBase64(text) {\r\n  return /^(?:[a-zA-Z0-9+/]{4})*(?:[a-zA-Z0-9+/]{2}==|[a-zA-Z0-9+/]{3}=)?$/.test(text)\r\n}\r\n\r\nfunction parseAttributePairs(text) {\r\n  if (typeof text !== 'string') {\r\n    throw new TypeError('SASL: attribute pairs text must be a string')\r\n  }\r\n\r\n  return new Map(\r\n    text.split(',').map((attrValue) => {\r\n      if (!/^.=/.test(attrValue)) {\r\n        throw new Error('SASL: Invalid attribute pair entry')\r\n      }\r\n      const name = attrValue[0]\r\n      const value = attrValue.substring(2)\r\n      return [name, value]\r\n    })\r\n  )\r\n}\r\n\r\nfunction parseServerFirstMessage(data) {\r\n  const attrPairs = parseAttributePairs(data)\r\n\r\n  const nonce = attrPairs.get('r')\r\n  if (!nonce) {\r\n    throw new Error('SASL: SCRAM-SERVER-FIRST-MESSAGE: nonce missing')\r\n  } else if (!isPrintableChars(nonce)) {\r\n    throw new Error('SASL: SCRAM-SERVER-FIRST-MESSAGE: nonce must only contain printable characters')\r\n  }\r\n  const salt = attrPairs.get('s')\r\n  if (!salt) {\r\n    throw new Error('SASL: SCRAM-SERVER-FIRST-MESSAGE: salt missing')\r\n  } else if (!isBase64(salt)) {\r\n    throw new Error('SASL: SCRAM-SERVER-FIRST-MESSAGE: salt must be base64')\r\n  }\r\n  const iterationText = attrPairs.get('i')\r\n  if (!iterationText) {\r\n    throw new Error('SASL: SCRAM-SERVER-FIRST-MESSAGE: iteration missing')\r\n  } else if (!/^[1-9][0-9]*$/.test(iterationText)) {\r\n    throw new Error('SASL: SCRAM-SERVER-FIRST-MESSAGE: invalid iteration count')\r\n  }\r\n  const iteration = parseInt(iterationText, 10)\r\n\r\n  return {\r\n    nonce,\r\n    salt,\r\n    iteration,\r\n  }\r\n}\r\n\r\nfunction parseServerFinalMessage(serverData) {\r\n  const attrPairs = parseAttributePairs(serverData)\r\n  const serverSignature = attrPairs.get('v')\r\n  if (!serverSignature) {\r\n    throw new Error('SASL: SCRAM-SERVER-FINAL-MESSAGE: server signature is missing')\r\n  } else if (!isBase64(serverSignature)) {\r\n    throw new Error('SASL: SCRAM-SERVER-FINAL-MESSAGE: server signature must be base64')\r\n  }\r\n  return {\r\n    serverSignature,\r\n  }\r\n}\r\n\r\nfunction xorBuffers(a, b) {\r\n  if (!Buffer.isBuffer(a)) {\r\n    throw new TypeError('first argument must be a Buffer')\r\n  }\r\n  if (!Buffer.isBuffer(b)) {\r\n    throw new TypeError('second argument must be a Buffer')\r\n  }\r\n  if (a.length !== b.length) {\r\n    throw new Error('Buffer lengths must match')\r\n  }\r\n  if (a.length === 0) {\r\n    throw new Error('Buffers cannot be empty')\r\n  }\r\n  return Buffer.from(a.map((_, i) => a[i] ^ b[i]))\r\n}\r\n\r\nmodule.exports = {\r\n  startSession,\r\n  continueSession,\r\n  finalizeSession,\r\n}\r\n", "'use strict'\r\n\r\nconst types = require('pg-types')\r\n\r\nfunction TypeOverrides(userTypes) {\r\n  this._types = userTypes || types\r\n  this.text = {}\r\n  this.binary = {}\r\n}\r\n\r\nTypeOverrides.prototype.getOverrides = function (format) {\r\n  switch (format) {\r\n    case 'text':\r\n      return this.text\r\n    case 'binary':\r\n      return this.binary\r\n    default:\r\n      return {}\r\n  }\r\n}\r\n\r\nTypeOverrides.prototype.setTypeParser = function (oid, format, parseFn) {\r\n  if (typeof format === 'function') {\r\n    parseFn = format\r\n    format = 'text'\r\n  }\r\n  this.getOverrides(format)[oid] = parseFn\r\n}\r\n\r\nTypeOverrides.prototype.getTypeParser = function (oid, format) {\r\n  format = format || 'text'\r\n  return this.getOverrides(format)[oid] || this._types.getTypeParser(oid, format)\r\n}\r\n\r\nmodule.exports = TypeOverrides\r\n", "module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"dns\" has been externalized for browser compatibility. Cannot access \"dns.${key}\" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "'use strict'\r\n\r\n//Parse method copied from https://github.com/brianc/node-postgres\r\n//Copyright (c) 2010-2014 <PERSON> (<EMAIL>)\r\n//MIT License\r\n\r\n//parses a connection string\r\nfunction parse(str, options = {}) {\r\n  //unix socket\r\n  if (str.charAt(0) === '/') {\r\n    const config = str.split(' ')\r\n    return { host: config[0], database: config[1] }\r\n  }\r\n\r\n  // Check for empty host in URL\r\n\r\n  const config = {}\r\n  let result\r\n  let dummyHost = false\r\n  if (/ |%[^a-f0-9]|%[a-f0-9][^a-f0-9]/i.test(str)) {\r\n    // Ensure spaces are encoded as %20\r\n    str = encodeURI(str).replace(/%25(\\d\\d)/g, '%$1')\r\n  }\r\n\r\n  try {\r\n    try {\r\n      result = new URL(str, 'postgres://base')\r\n    } catch (e) {\r\n      // The URL is invalid so try again with a dummy host\r\n      result = new URL(str.replace('@/', '@___DUMMY___/'), 'postgres://base')\r\n      dummyHost = true\r\n    }\r\n  } catch (err) {\r\n    // Remove the input from the error message to avoid leaking sensitive information\r\n    err.input && (err.input = '*****REDACTED*****')\r\n  }\r\n\r\n  // We'd like to use Object.fromEntries() here but Node.js 10 does not support it\r\n  for (const entry of result.searchParams.entries()) {\r\n    config[entry[0]] = entry[1]\r\n  }\r\n\r\n  config.user = config.user || decodeURIComponent(result.username)\r\n  config.password = config.password || decodeURIComponent(result.password)\r\n\r\n  if (result.protocol == 'socket:') {\r\n    config.host = decodeURI(result.pathname)\r\n    config.database = result.searchParams.get('db')\r\n    config.client_encoding = result.searchParams.get('encoding')\r\n    return config\r\n  }\r\n  const hostname = dummyHost ? '' : result.hostname\r\n  if (!config.host) {\r\n    // Only set the host if there is no equivalent query param.\r\n    config.host = decodeURIComponent(hostname)\r\n  } else if (hostname && /^%2f/i.test(hostname)) {\r\n    // Only prepend the hostname to the pathname if it is not a URL encoded Unix socket host.\r\n    result.pathname = hostname + result.pathname\r\n  }\r\n  if (!config.port) {\r\n    // Only set the port if there is no equivalent query param.\r\n    config.port = result.port\r\n  }\r\n\r\n  const pathname = result.pathname.slice(1) || null\r\n  config.database = pathname ? decodeURI(pathname) : null\r\n\r\n  if (config.ssl === 'true' || config.ssl === '1') {\r\n    config.ssl = true\r\n  }\r\n\r\n  if (config.ssl === '0') {\r\n    config.ssl = false\r\n  }\r\n\r\n  if (config.sslcert || config.sslkey || config.sslrootcert || config.sslmode) {\r\n    config.ssl = {}\r\n  }\r\n\r\n  // Only try to load fs if we expect to read from the disk\r\n  const fs = config.sslcert || config.sslkey || config.sslrootcert ? require('fs') : null\r\n\r\n  if (config.sslcert) {\r\n    config.ssl.cert = fs.readFileSync(config.sslcert).toString()\r\n  }\r\n\r\n  if (config.sslkey) {\r\n    config.ssl.key = fs.readFileSync(config.sslkey).toString()\r\n  }\r\n\r\n  if (config.sslrootcert) {\r\n    config.ssl.ca = fs.readFileSync(config.sslrootcert).toString()\r\n  }\r\n\r\n  if (options.useLibpqCompat && config.uselibpqcompat) {\r\n    throw new Error('Both useLibpqCompat and uselibpqcompat are set. Please use only one of them.')\r\n  }\r\n\r\n  if (config.uselibpqcompat === 'true' || options.useLibpqCompat) {\r\n    switch (config.sslmode) {\r\n      case 'disable': {\r\n        config.ssl = false\r\n        break\r\n      }\r\n      case 'prefer': {\r\n        config.ssl.rejectUnauthorized = false\r\n        break\r\n      }\r\n      case 'require': {\r\n        if (config.sslrootcert) {\r\n          // If a root CA is specified, behavior of `sslmode=require` will be the same as that of `verify-ca`\r\n          config.ssl.checkServerIdentity = function () {}\r\n        } else {\r\n          config.ssl.rejectUnauthorized = false\r\n        }\r\n        break\r\n      }\r\n      case 'verify-ca': {\r\n        if (!config.ssl.ca) {\r\n          throw new Error(\r\n            'SECURITY WARNING: Using sslmode=verify-ca requires specifying a CA with sslrootcert. If a public CA is used, verify-ca allows connections to a server that somebody else may have registered with the CA, making you vulnerable to Man-in-the-Middle attacks. Either specify a custom CA certificate with sslrootcert parameter or use sslmode=verify-full for proper security.'\r\n          )\r\n        }\r\n        config.ssl.checkServerIdentity = function () {}\r\n        break\r\n      }\r\n      case 'verify-full': {\r\n        break\r\n      }\r\n    }\r\n  } else {\r\n    switch (config.sslmode) {\r\n      case 'disable': {\r\n        config.ssl = false\r\n        break\r\n      }\r\n      case 'prefer':\r\n      case 'require':\r\n      case 'verify-ca':\r\n      case 'verify-full': {\r\n        break\r\n      }\r\n      case 'no-verify': {\r\n        config.ssl.rejectUnauthorized = false\r\n        break\r\n      }\r\n    }\r\n  }\r\n\r\n  return config\r\n}\r\n\r\n// convert pg-connection-string ssl config to a ClientConfig.ConnectionOptions\r\nfunction toConnectionOptions(sslConfig) {\r\n  const connectionOptions = Object.entries(sslConfig).reduce((c, [key, value]) => {\r\n    // we explicitly check for undefined and null instead of `if (value)` because some\r\n    // options accept falsy values. Example: `ssl.rejectUnauthorized = false`\r\n    if (value !== undefined && value !== null) {\r\n      c[key] = value\r\n    }\r\n\r\n    return c\r\n  }, {})\r\n\r\n  return connectionOptions\r\n}\r\n\r\n// convert pg-connection-string config to a ClientConfig\r\nfunction toClientConfig(config) {\r\n  const poolConfig = Object.entries(config).reduce((c, [key, value]) => {\r\n    if (key === 'ssl') {\r\n      const sslConfig = value\r\n\r\n      if (typeof sslConfig === 'boolean') {\r\n        c[key] = sslConfig\r\n      }\r\n\r\n      if (typeof sslConfig === 'object') {\r\n        c[key] = toConnectionOptions(sslConfig)\r\n      }\r\n    } else if (value !== undefined && value !== null) {\r\n      if (key === 'port') {\r\n        // when port is not specified, it is converted into an empty string\r\n        // we want to avoid NaN or empty string as a values in ClientConfig\r\n        if (value !== '') {\r\n          const v = parseInt(value, 10)\r\n          if (isNaN(v)) {\r\n            throw new Error(`Invalid ${key}: ${value}`)\r\n          }\r\n\r\n          c[key] = v\r\n        }\r\n      } else {\r\n        c[key] = value\r\n      }\r\n    }\r\n\r\n    return c\r\n  }, {})\r\n\r\n  return poolConfig\r\n}\r\n\r\n// parses a connection string into ClientConfig\r\nfunction parseIntoClientConfig(str) {\r\n  return toClientConfig(parse(str))\r\n}\r\n\r\nmodule.exports = parse\r\n\r\nparse.parse = parse\r\nparse.toClientConfig = toClientConfig\r\nparse.parseIntoClientConfig = parseIntoClientConfig\r\n", "'use strict'\r\n\r\nconst dns = require('dns')\r\n\r\nconst defaults = require('./defaults')\r\n\r\nconst parse = require('pg-connection-string').parse // parses a connection string\r\n\r\nconst val = function (key, config, envVar) {\r\n  if (envVar === undefined) {\r\n    envVar = process.env['PG' + key.toUpperCase()]\r\n  } else if (envVar === false) {\r\n    // do nothing ... use false\r\n  } else {\r\n    envVar = process.env[envVar]\r\n  }\r\n\r\n  return config[key] || envVar || defaults[key]\r\n}\r\n\r\nconst readSSLConfigFromEnvironment = function () {\r\n  switch (process.env.PGSSLMODE) {\r\n    case 'disable':\r\n      return false\r\n    case 'prefer':\r\n    case 'require':\r\n    case 'verify-ca':\r\n    case 'verify-full':\r\n      return true\r\n    case 'no-verify':\r\n      return { rejectUnauthorized: false }\r\n  }\r\n  return defaults.ssl\r\n}\r\n\r\n// Convert arg to a string, surround in single quotes, and escape single quotes and backslashes\r\nconst quoteParamValue = function (value) {\r\n  return \"'\" + ('' + value).replace(/\\\\/g, '\\\\\\\\').replace(/'/g, \"\\\\'\") + \"'\"\r\n}\r\n\r\nconst add = function (params, config, paramName) {\r\n  const value = config[paramName]\r\n  if (value !== undefined && value !== null) {\r\n    params.push(paramName + '=' + quoteParamValue(value))\r\n  }\r\n}\r\n\r\nclass ConnectionParameters {\r\n  constructor(config) {\r\n    // if a string is passed, it is a raw connection string so we parse it into a config\r\n    config = typeof config === 'string' ? parse(config) : config || {}\r\n\r\n    // if the config has a connectionString defined, parse IT into the config we use\r\n    // this will override other default values with what is stored in connectionString\r\n    if (config.connectionString) {\r\n      config = Object.assign({}, config, parse(config.connectionString))\r\n    }\r\n\r\n    this.user = val('user', config)\r\n    this.database = val('database', config)\r\n\r\n    if (this.database === undefined) {\r\n      this.database = this.user\r\n    }\r\n\r\n    this.port = parseInt(val('port', config), 10)\r\n    this.host = val('host', config)\r\n\r\n    // \"hiding\" the password so it doesn't show up in stack traces\r\n    // or if the client is console.logged\r\n    Object.defineProperty(this, 'password', {\r\n      configurable: true,\r\n      enumerable: false,\r\n      writable: true,\r\n      value: val('password', config),\r\n    })\r\n\r\n    this.binary = val('binary', config)\r\n    this.options = val('options', config)\r\n\r\n    this.ssl = typeof config.ssl === 'undefined' ? readSSLConfigFromEnvironment() : config.ssl\r\n\r\n    if (typeof this.ssl === 'string') {\r\n      if (this.ssl === 'true') {\r\n        this.ssl = true\r\n      }\r\n    }\r\n    // support passing in ssl=no-verify via connection string\r\n    if (this.ssl === 'no-verify') {\r\n      this.ssl = { rejectUnauthorized: false }\r\n    }\r\n    if (this.ssl && this.ssl.key) {\r\n      Object.defineProperty(this.ssl, 'key', {\r\n        enumerable: false,\r\n      })\r\n    }\r\n\r\n    this.client_encoding = val('client_encoding', config)\r\n    this.replication = val('replication', config)\r\n    // a domain socket begins with '/'\r\n    this.isDomainSocket = !(this.host || '').indexOf('/')\r\n\r\n    this.application_name = val('application_name', config, 'PGAPPNAME')\r\n    this.fallback_application_name = val('fallback_application_name', config, false)\r\n    this.statement_timeout = val('statement_timeout', config, false)\r\n    this.lock_timeout = val('lock_timeout', config, false)\r\n    this.idle_in_transaction_session_timeout = val('idle_in_transaction_session_timeout', config, false)\r\n    this.query_timeout = val('query_timeout', config, false)\r\n\r\n    if (config.connectionTimeoutMillis === undefined) {\r\n      this.connect_timeout = process.env.PGCONNECT_TIMEOUT || 0\r\n    } else {\r\n      this.connect_timeout = Math.floor(config.connectionTimeoutMillis / 1000)\r\n    }\r\n\r\n    if (config.keepAlive === false) {\r\n      this.keepalives = 0\r\n    } else if (config.keepAlive === true) {\r\n      this.keepalives = 1\r\n    }\r\n\r\n    if (typeof config.keepAliveInitialDelayMillis === 'number') {\r\n      this.keepalives_idle = Math.floor(config.keepAliveInitialDelayMillis / 1000)\r\n    }\r\n  }\r\n\r\n  getLibpqConnectionString(cb) {\r\n    const params = []\r\n    add(params, this, 'user')\r\n    add(params, this, 'password')\r\n    add(params, this, 'port')\r\n    add(params, this, 'application_name')\r\n    add(params, this, 'fallback_application_name')\r\n    add(params, this, 'connect_timeout')\r\n    add(params, this, 'options')\r\n\r\n    const ssl = typeof this.ssl === 'object' ? this.ssl : this.ssl ? { sslmode: this.ssl } : {}\r\n    add(params, ssl, 'sslmode')\r\n    add(params, ssl, 'sslca')\r\n    add(params, ssl, 'sslkey')\r\n    add(params, ssl, 'sslcert')\r\n    add(params, ssl, 'sslrootcert')\r\n\r\n    if (this.database) {\r\n      params.push('dbname=' + quoteParamValue(this.database))\r\n    }\r\n    if (this.replication) {\r\n      params.push('replication=' + quoteParamValue(this.replication))\r\n    }\r\n    if (this.host) {\r\n      params.push('host=' + quoteParamValue(this.host))\r\n    }\r\n    if (this.isDomainSocket) {\r\n      return cb(null, params.join(' '))\r\n    }\r\n    if (this.client_encoding) {\r\n      params.push('client_encoding=' + quoteParamValue(this.client_encoding))\r\n    }\r\n    dns.lookup(this.host, function (err, address) {\r\n      if (err) return cb(err, null)\r\n      params.push('hostaddr=' + quoteParamValue(address))\r\n      return cb(null, params.join(' '))\r\n    })\r\n  }\r\n}\r\n\r\nmodule.exports = ConnectionParameters\r\n", "'use strict'\r\n\r\nconst types = require('pg-types')\r\n\r\nconst matchRegexp = /^([A-Za-z]+)(?: (\\d+))?(?: (\\d+))?/\r\n\r\n// result object returned from query\r\n// in the 'end' event and also\r\n// passed as second argument to provided callback\r\nclass Result {\r\n  constructor(rowMode, types) {\r\n    this.command = null\r\n    this.rowCount = null\r\n    this.oid = null\r\n    this.rows = []\r\n    this.fields = []\r\n    this._parsers = undefined\r\n    this._types = types\r\n    this.RowCtor = null\r\n    this.rowAsArray = rowMode === 'array'\r\n    if (this.rowAsArray) {\r\n      this.parseRow = this._parseRowAsArray\r\n    }\r\n    this._prebuiltEmptyResultObject = null\r\n  }\r\n\r\n  // adds a command complete message\r\n  addCommandComplete(msg) {\r\n    let match\r\n    if (msg.text) {\r\n      // pure javascript\r\n      match = matchRegexp.exec(msg.text)\r\n    } else {\r\n      // native bindings\r\n      match = matchRegexp.exec(msg.command)\r\n    }\r\n    if (match) {\r\n      this.command = match[1]\r\n      if (match[3]) {\r\n        // COMMAND OID ROWS\r\n        this.oid = parseInt(match[2], 10)\r\n        this.rowCount = parseInt(match[3], 10)\r\n      } else if (match[2]) {\r\n        // COMMAND ROWS\r\n        this.rowCount = parseInt(match[2], 10)\r\n      }\r\n    }\r\n  }\r\n\r\n  _parseRowAsArray(rowData) {\r\n    const row = new Array(rowData.length)\r\n    for (let i = 0, len = rowData.length; i < len; i++) {\r\n      const rawValue = rowData[i]\r\n      if (rawValue !== null) {\r\n        row[i] = this._parsers[i](rawValue)\r\n      } else {\r\n        row[i] = null\r\n      }\r\n    }\r\n    return row\r\n  }\r\n\r\n  parseRow(rowData) {\r\n    const row = { ...this._prebuiltEmptyResultObject }\r\n    for (let i = 0, len = rowData.length; i < len; i++) {\r\n      const rawValue = rowData[i]\r\n      const field = this.fields[i].name\r\n      if (rawValue !== null) {\r\n        row[field] = this._parsers[i](rawValue)\r\n      } else {\r\n        row[field] = null\r\n      }\r\n    }\r\n    return row\r\n  }\r\n\r\n  addRow(row) {\r\n    this.rows.push(row)\r\n  }\r\n\r\n  addFields(fieldDescriptions) {\r\n    // clears field definitions\r\n    // multiple query statements in 1 action can result in multiple sets\r\n    // of rowDescriptions...eg: 'select NOW(); select 1::int;'\r\n    // you need to reset the fields\r\n    this.fields = fieldDescriptions\r\n    if (this.fields.length) {\r\n      this._parsers = new Array(fieldDescriptions.length)\r\n    }\r\n\r\n    const row = {}\r\n\r\n    for (let i = 0; i < fieldDescriptions.length; i++) {\r\n      const desc = fieldDescriptions[i]\r\n      row[desc.name] = null\r\n\r\n      if (this._types) {\r\n        this._parsers[i] = this._types.getTypeParser(desc.dataTypeID, desc.format || 'text')\r\n      } else {\r\n        this._parsers[i] = types.getTypeParser(desc.dataTypeID, desc.format || 'text')\r\n      }\r\n    }\r\n\r\n    this._prebuiltEmptyResultObject = { ...row }\r\n  }\r\n}\r\n\r\nmodule.exports = Result\r\n", "'use strict'\r\n\r\nconst { EventEmitter } = require('events')\r\n\r\nconst Result = require('./result')\r\nconst utils = require('./utils')\r\n\r\nclass Query extends EventEmitter {\r\n  constructor(config, values, callback) {\r\n    super()\r\n\r\n    config = utils.normalizeQueryConfig(config, values, callback)\r\n\r\n    this.text = config.text\r\n    this.values = config.values\r\n    this.rows = config.rows\r\n    this.types = config.types\r\n    this.name = config.name\r\n    this.queryMode = config.queryMode\r\n    this.binary = config.binary\r\n    // use unique portal name each time\r\n    this.portal = config.portal || ''\r\n    this.callback = config.callback\r\n    this._rowMode = config.rowMode\r\n    if (process.domain && config.callback) {\r\n      this.callback = process.domain.bind(config.callback)\r\n    }\r\n    this._result = new Result(this._rowMode, this.types)\r\n\r\n    // potential for multiple results\r\n    this._results = this._result\r\n    this._canceledDueToError = false\r\n  }\r\n\r\n  requiresPreparation() {\r\n    if (this.queryMode === 'extended') {\r\n      return true\r\n    }\r\n\r\n    // named queries must always be prepared\r\n    if (this.name) {\r\n      return true\r\n    }\r\n    // always prepare if there are max number of rows expected per\r\n    // portal execution\r\n    if (this.rows) {\r\n      return true\r\n    }\r\n    // don't prepare empty text queries\r\n    if (!this.text) {\r\n      return false\r\n    }\r\n    // prepare if there are values\r\n    if (!this.values) {\r\n      return false\r\n    }\r\n    return this.values.length > 0\r\n  }\r\n\r\n  _checkForMultirow() {\r\n    // if we already have a result with a command property\r\n    // then we've already executed one query in a multi-statement simple query\r\n    // turn our results into an array of results\r\n    if (this._result.command) {\r\n      if (!Array.isArray(this._results)) {\r\n        this._results = [this._result]\r\n      }\r\n      this._result = new Result(this._rowMode, this._result._types)\r\n      this._results.push(this._result)\r\n    }\r\n  }\r\n\r\n  // associates row metadata from the supplied\r\n  // message with this query object\r\n  // metadata used when parsing row results\r\n  handleRowDescription(msg) {\r\n    this._checkForMultirow()\r\n    this._result.addFields(msg.fields)\r\n    this._accumulateRows = this.callback || !this.listeners('row').length\r\n  }\r\n\r\n  handleDataRow(msg) {\r\n    let row\r\n\r\n    if (this._canceledDueToError) {\r\n      return\r\n    }\r\n\r\n    try {\r\n      row = this._result.parseRow(msg.fields)\r\n    } catch (err) {\r\n      this._canceledDueToError = err\r\n      return\r\n    }\r\n\r\n    this.emit('row', row, this._result)\r\n    if (this._accumulateRows) {\r\n      this._result.addRow(row)\r\n    }\r\n  }\r\n\r\n  handleCommandComplete(msg, connection) {\r\n    this._checkForMultirow()\r\n    this._result.addCommandComplete(msg)\r\n    // need to sync after each command complete of a prepared statement\r\n    // if we were using a row count which results in multiple calls to _getRows\r\n    if (this.rows) {\r\n      connection.sync()\r\n    }\r\n  }\r\n\r\n  // if a named prepared statement is created with empty query text\r\n  // the backend will send an emptyQuery message but *not* a command complete message\r\n  // since we pipeline sync immediately after execute we don't need to do anything here\r\n  // unless we have rows specified, in which case we did not pipeline the intial sync call\r\n  handleEmptyQuery(connection) {\r\n    if (this.rows) {\r\n      connection.sync()\r\n    }\r\n  }\r\n\r\n  handleError(err, connection) {\r\n    // need to sync after error during a prepared statement\r\n    if (this._canceledDueToError) {\r\n      err = this._canceledDueToError\r\n      this._canceledDueToError = false\r\n    }\r\n    // if callback supplied do not emit error event as uncaught error\r\n    // events will bubble up to node process\r\n    if (this.callback) {\r\n      return this.callback(err)\r\n    }\r\n    this.emit('error', err)\r\n  }\r\n\r\n  handleReadyForQuery(con) {\r\n    if (this._canceledDueToError) {\r\n      return this.handleError(this._canceledDueToError, con)\r\n    }\r\n    if (this.callback) {\r\n      try {\r\n        this.callback(null, this._results)\r\n      } catch (err) {\r\n        process.nextTick(() => {\r\n          throw err\r\n        })\r\n      }\r\n    }\r\n    this.emit('end', this._results)\r\n  }\r\n\r\n  submit(connection) {\r\n    if (typeof this.text !== 'string' && typeof this.name !== 'string') {\r\n      return new Error('A query must have either text or a name. Supplying neither is unsupported.')\r\n    }\r\n    const previous = connection.parsedStatements[this.name]\r\n    if (this.text && previous && this.text !== previous) {\r\n      return new Error(`Prepared statements must be unique - '${this.name}' was used for a different statement`)\r\n    }\r\n    if (this.values && !Array.isArray(this.values)) {\r\n      return new Error('Query values must be an array')\r\n    }\r\n    if (this.requiresPreparation()) {\r\n      // If we're using the extended query protocol we fire off several separate commands\r\n      // to the backend. On some versions of node & some operating system versions\r\n      // the network stack writes each message separately instead of buffering them together\r\n      // causing the client & network to send more slowly. Corking & uncorking the stream\r\n      // allows node to buffer up the messages internally before sending them all off at once.\r\n      // note: we're checking for existence of cork/uncork because some versions of streams\r\n      // might not have this (cloudflare?)\r\n      connection.stream.cork && connection.stream.cork()\r\n      try {\r\n        this.prepare(connection)\r\n      } finally {\r\n        // while unlikely for this.prepare to throw, if it does & we don't uncork this stream\r\n        // this client becomes unresponsive, so put in finally block \"just in case\"\r\n        connection.stream.uncork && connection.stream.uncork()\r\n      }\r\n    } else {\r\n      connection.query(this.text)\r\n    }\r\n    return null\r\n  }\r\n\r\n  hasBeenParsed(connection) {\r\n    return this.name && connection.parsedStatements[this.name]\r\n  }\r\n\r\n  handlePortalSuspended(connection) {\r\n    this._getRows(connection, this.rows)\r\n  }\r\n\r\n  _getRows(connection, rows) {\r\n    connection.execute({\r\n      portal: this.portal,\r\n      rows: rows,\r\n    })\r\n    // if we're not reading pages of rows send the sync command\r\n    // to indicate the pipeline is finished\r\n    if (!rows) {\r\n      connection.sync()\r\n    } else {\r\n      // otherwise flush the call out to read more rows\r\n      connection.flush()\r\n    }\r\n  }\r\n\r\n  // http://developer.postgresql.org/pgdocs/postgres/protocol-flow.html#PROTOCOL-FLOW-EXT-QUERY\r\n  prepare(connection) {\r\n    // TODO refactor this poor encapsulation\r\n    if (!this.hasBeenParsed(connection)) {\r\n      connection.parse({\r\n        text: this.text,\r\n        name: this.name,\r\n        types: this.types,\r\n      })\r\n    }\r\n\r\n    // because we're mapping user supplied values to\r\n    // postgres wire protocol compatible values it could\r\n    // throw an exception, so try/catch this section\r\n    try {\r\n      connection.bind({\r\n        portal: this.portal,\r\n        statement: this.name,\r\n        values: this.values,\r\n        binary: this.binary,\r\n        valueMapper: utils.prepareValue,\r\n      })\r\n    } catch (err) {\r\n      this.handleError(err, connection)\r\n      return\r\n    }\r\n\r\n    connection.describe({\r\n      type: 'P',\r\n      name: this.portal || '',\r\n    })\r\n\r\n    this._getRows(connection, this.rows)\r\n  }\r\n\r\n  handleCopyInResponse(connection) {\r\n    connection.sendCopyFail('No source stream defined')\r\n  }\r\n\r\n  handleCopyData(msg, connection) {\r\n    // noop\r\n  }\r\n}\r\n\r\nmodule.exports = Query\r\n", "export type Mode = 'text' | 'binary'\r\n\r\nexport type MessageName =\r\n  | 'parseComplete'\r\n  | 'bindComplete'\r\n  | 'closeComplete'\r\n  | 'noData'\r\n  | 'portalSuspended'\r\n  | 'replicationStart'\r\n  | 'emptyQuery'\r\n  | 'copyDone'\r\n  | 'copyData'\r\n  | 'rowDescription'\r\n  | 'parameterDescription'\r\n  | 'parameterStatus'\r\n  | 'backendKeyData'\r\n  | 'notification'\r\n  | 'readyForQuery'\r\n  | 'commandComplete'\r\n  | 'dataRow'\r\n  | 'copyInResponse'\r\n  | 'copyOutResponse'\r\n  | 'authenticationOk'\r\n  | 'authenticationMD5Password'\r\n  | 'authenticationCleartextPassword'\r\n  | 'authenticationSASL'\r\n  | 'authenticationSASLContinue'\r\n  | 'authenticationSASLFinal'\r\n  | 'error'\r\n  | 'notice'\r\n\r\nexport interface BackendMessage {\r\n  name: MessageName\r\n  length: number\r\n}\r\n\r\nexport const parseComplete: BackendMessage = {\r\n  name: 'parseComplete',\r\n  length: 5,\r\n}\r\n\r\nexport const bindComplete: BackendMessage = {\r\n  name: 'bindComplete',\r\n  length: 5,\r\n}\r\n\r\nexport const closeComplete: BackendMessage = {\r\n  name: 'closeComplete',\r\n  length: 5,\r\n}\r\n\r\nexport const noData: BackendMessage = {\r\n  name: 'noData',\r\n  length: 5,\r\n}\r\n\r\nexport const portalSuspended: BackendMessage = {\r\n  name: 'portalSuspended',\r\n  length: 5,\r\n}\r\n\r\nexport const replicationStart: BackendMessage = {\r\n  name: 'replicationStart',\r\n  length: 4,\r\n}\r\n\r\nexport const emptyQuery: BackendMessage = {\r\n  name: 'emptyQuery',\r\n  length: 4,\r\n}\r\n\r\nexport const copyDone: BackendMessage = {\r\n  name: 'copyDone',\r\n  length: 4,\r\n}\r\n\r\ninterface NoticeOrError {\r\n  message: string | undefined\r\n  severity: string | undefined\r\n  code: string | undefined\r\n  detail: string | undefined\r\n  hint: string | undefined\r\n  position: string | undefined\r\n  internalPosition: string | undefined\r\n  internalQuery: string | undefined\r\n  where: string | undefined\r\n  schema: string | undefined\r\n  table: string | undefined\r\n  column: string | undefined\r\n  dataType: string | undefined\r\n  constraint: string | undefined\r\n  file: string | undefined\r\n  line: string | undefined\r\n  routine: string | undefined\r\n}\r\n\r\nexport class DatabaseError extends Error implements NoticeOrError {\r\n  public severity: string | undefined\r\n  public code: string | undefined\r\n  public detail: string | undefined\r\n  public hint: string | undefined\r\n  public position: string | undefined\r\n  public internalPosition: string | undefined\r\n  public internalQuery: string | undefined\r\n  public where: string | undefined\r\n  public schema: string | undefined\r\n  public table: string | undefined\r\n  public column: string | undefined\r\n  public dataType: string | undefined\r\n  public constraint: string | undefined\r\n  public file: string | undefined\r\n  public line: string | undefined\r\n  public routine: string | undefined\r\n  constructor(\r\n    message: string,\r\n    public readonly length: number,\r\n    public readonly name: MessageName\r\n  ) {\r\n    super(message)\r\n  }\r\n}\r\n\r\nexport class CopyDataMessage {\r\n  public readonly name = 'copyData'\r\n  constructor(\r\n    public readonly length: number,\r\n    public readonly chunk: Buffer\r\n  ) {}\r\n}\r\n\r\nexport class CopyResponse {\r\n  public readonly columnTypes: number[]\r\n  constructor(\r\n    public readonly length: number,\r\n    public readonly name: MessageName,\r\n    public readonly binary: boolean,\r\n    columnCount: number\r\n  ) {\r\n    this.columnTypes = new Array(columnCount)\r\n  }\r\n}\r\n\r\nexport class Field {\r\n  constructor(\r\n    public readonly name: string,\r\n    public readonly tableID: number,\r\n    public readonly columnID: number,\r\n    public readonly dataTypeID: number,\r\n    public readonly dataTypeSize: number,\r\n    public readonly dataTypeModifier: number,\r\n    public readonly format: Mode\r\n  ) {}\r\n}\r\n\r\nexport class RowDescriptionMessage {\r\n  public readonly name: MessageName = 'rowDescription'\r\n  public readonly fields: Field[]\r\n  constructor(\r\n    public readonly length: number,\r\n    public readonly fieldCount: number\r\n  ) {\r\n    this.fields = new Array(this.fieldCount)\r\n  }\r\n}\r\n\r\nexport class ParameterDescriptionMessage {\r\n  public readonly name: MessageName = 'parameterDescription'\r\n  public readonly dataTypeIDs: number[]\r\n  constructor(\r\n    public readonly length: number,\r\n    public readonly parameterCount: number\r\n  ) {\r\n    this.dataTypeIDs = new Array(this.parameterCount)\r\n  }\r\n}\r\n\r\nexport class ParameterStatusMessage {\r\n  public readonly name: MessageName = 'parameterStatus'\r\n  constructor(\r\n    public readonly length: number,\r\n    public readonly parameterName: string,\r\n    public readonly parameterValue: string\r\n  ) {}\r\n}\r\n\r\nexport class AuthenticationMD5Password implements BackendMessage {\r\n  public readonly name: MessageName = 'authenticationMD5Password'\r\n  constructor(\r\n    public readonly length: number,\r\n    public readonly salt: Buffer\r\n  ) {}\r\n}\r\n\r\nexport class BackendKeyDataMessage {\r\n  public readonly name: MessageName = 'backendKeyData'\r\n  constructor(\r\n    public readonly length: number,\r\n    public readonly processID: number,\r\n    public readonly secretKey: number\r\n  ) {}\r\n}\r\n\r\nexport class NotificationResponseMessage {\r\n  public readonly name: MessageName = 'notification'\r\n  constructor(\r\n    public readonly length: number,\r\n    public readonly processId: number,\r\n    public readonly channel: string,\r\n    public readonly payload: string\r\n  ) {}\r\n}\r\n\r\nexport class ReadyForQueryMessage {\r\n  public readonly name: MessageName = 'readyForQuery'\r\n  constructor(\r\n    public readonly length: number,\r\n    public readonly status: string\r\n  ) {}\r\n}\r\n\r\nexport class CommandCompleteMessage {\r\n  public readonly name: MessageName = 'commandComplete'\r\n  constructor(\r\n    public readonly length: number,\r\n    public readonly text: string\r\n  ) {}\r\n}\r\n\r\nexport class DataRowMessage {\r\n  public readonly fieldCount: number\r\n  public readonly name: MessageName = 'dataRow'\r\n  constructor(\r\n    public length: number,\r\n    public fields: any[]\r\n  ) {\r\n    this.fieldCount = fields.length\r\n  }\r\n}\r\n\r\nexport class NoticeMessage implements BackendMessage, NoticeOrError {\r\n  constructor(\r\n    public readonly length: number,\r\n    public readonly message: string | undefined\r\n  ) {}\r\n  public readonly name = 'notice'\r\n  public severity: string | undefined\r\n  public code: string | undefined\r\n  public detail: string | undefined\r\n  public hint: string | undefined\r\n  public position: string | undefined\r\n  public internalPosition: string | undefined\r\n  public internalQuery: string | undefined\r\n  public where: string | undefined\r\n  public schema: string | undefined\r\n  public table: string | undefined\r\n  public column: string | undefined\r\n  public dataType: string | undefined\r\n  public constraint: string | undefined\r\n  public file: string | undefined\r\n  public line: string | undefined\r\n  public routine: string | undefined\r\n}\r\n", "//binary data writer tuned for encoding binary specific to the postgres binary protocol\r\n\r\nexport class Writer {\r\n  private buffer: <PERSON>uffer\r\n  private offset: number = 5\r\n  private headerPosition: number = 0\r\n  constructor(private size = 256) {\r\n    this.buffer = Buffer.allocUnsafe(size)\r\n  }\r\n\r\n  private ensure(size: number): void {\r\n    const remaining = this.buffer.length - this.offset\r\n    if (remaining < size) {\r\n      const oldBuffer = this.buffer\r\n      // exponential growth factor of around ~ 1.5\r\n      // https://stackoverflow.com/questions/2269063/buffer-growth-strategy\r\n      const newSize = oldBuffer.length + (oldBuffer.length >> 1) + size\r\n      this.buffer = Buffer.allocUnsafe(newSize)\r\n      oldBuffer.copy(this.buffer)\r\n    }\r\n  }\r\n\r\n  public addInt32(num: number): Writer {\r\n    this.ensure(4)\r\n    this.buffer[this.offset++] = (num >>> 24) & 0xff\r\n    this.buffer[this.offset++] = (num >>> 16) & 0xff\r\n    this.buffer[this.offset++] = (num >>> 8) & 0xff\r\n    this.buffer[this.offset++] = (num >>> 0) & 0xff\r\n    return this\r\n  }\r\n\r\n  public addInt16(num: number): Writer {\r\n    this.ensure(2)\r\n    this.buffer[this.offset++] = (num >>> 8) & 0xff\r\n    this.buffer[this.offset++] = (num >>> 0) & 0xff\r\n    return this\r\n  }\r\n\r\n  public addCString(string: string): Writer {\r\n    if (!string) {\r\n      this.ensure(1)\r\n    } else {\r\n      const len = Buffer.byteLength(string)\r\n      this.ensure(len + 1) // +1 for null terminator\r\n      this.buffer.write(string, this.offset, 'utf-8')\r\n      this.offset += len\r\n    }\r\n\r\n    this.buffer[this.offset++] = 0 // null terminator\r\n    return this\r\n  }\r\n\r\n  public addString(string: string = ''): Writer {\r\n    const len = Buffer.byteLength(string)\r\n    this.ensure(len)\r\n    this.buffer.write(string, this.offset)\r\n    this.offset += len\r\n    return this\r\n  }\r\n\r\n  public add(otherBuffer: Buffer): Writer {\r\n    this.ensure(otherBuffer.length)\r\n    otherBuffer.copy(this.buffer, this.offset)\r\n    this.offset += otherBuffer.length\r\n    return this\r\n  }\r\n\r\n  private join(code?: number): Buffer {\r\n    if (code) {\r\n      this.buffer[this.headerPosition] = code\r\n      //length is everything in this packet minus the code\r\n      const length = this.offset - (this.headerPosition + 1)\r\n      this.buffer.writeInt32BE(length, this.headerPosition + 1)\r\n    }\r\n    return this.buffer.slice(code ? 0 : 5, this.offset)\r\n  }\r\n\r\n  public flush(code?: number): Buffer {\r\n    const result = this.join(code)\r\n    this.offset = 5\r\n    this.headerPosition = 0\r\n    this.buffer = Buffer.allocUnsafe(this.size)\r\n    return result\r\n  }\r\n}\r\n", "import { Writer } from './buffer-writer'\r\n\r\nconst enum code {\r\n  startup = 0x70,\r\n  query = 0x51,\r\n  parse = 0x50,\r\n  bind = 0x42,\r\n  execute = 0x45,\r\n  flush = 0x48,\r\n  sync = 0x53,\r\n  end = 0x58,\r\n  close = 0x43,\r\n  describe = 0x44,\r\n  copyFromChunk = 0x64,\r\n  copyDone = 0x63,\r\n  copyFail = 0x66,\r\n}\r\n\r\nconst writer = new Writer()\r\n\r\nconst startup = (opts: Record<string, string>): Buffer => {\r\n  // protocol version\r\n  writer.addInt16(3).addInt16(0)\r\n  for (const key of Object.keys(opts)) {\r\n    writer.addCString(key).addCString(opts[key])\r\n  }\r\n\r\n  writer.addCString('client_encoding').addCString('UTF8')\r\n\r\n  const bodyBuffer = writer.addCString('').flush()\r\n  // this message is sent without a code\r\n\r\n  const length = bodyBuffer.length + 4\r\n\r\n  return new Writer().addInt32(length).add(bodyBuffer).flush()\r\n}\r\n\r\nconst requestSsl = (): Buffer => {\r\n  const response = Buffer.allocUnsafe(8)\r\n  response.writeInt32BE(8, 0)\r\n  response.writeInt32BE(80877103, 4)\r\n  return response\r\n}\r\n\r\nconst password = (password: string): Buffer => {\r\n  return writer.addCString(password).flush(code.startup)\r\n}\r\n\r\nconst sendSASLInitialResponseMessage = function (mechanism: string, initialResponse: string): Buffer {\r\n  // 0x70 = 'p'\r\n  writer.addCString(mechanism).addInt32(Buffer.byteLength(initialResponse)).addString(initialResponse)\r\n\r\n  return writer.flush(code.startup)\r\n}\r\n\r\nconst sendSCRAMClientFinalMessage = function (additionalData: string): Buffer {\r\n  return writer.addString(additionalData).flush(code.startup)\r\n}\r\n\r\nconst query = (text: string): Buffer => {\r\n  return writer.addCString(text).flush(code.query)\r\n}\r\n\r\ntype ParseOpts = {\r\n  name?: string\r\n  types?: number[]\r\n  text: string\r\n}\r\n\r\nconst emptyArray: any[] = []\r\n\r\nconst parse = (query: ParseOpts): Buffer => {\r\n  // expect something like this:\r\n  // { name: 'queryName',\r\n  //   text: 'select * from blah',\r\n  //   types: ['int8', 'bool'] }\r\n\r\n  // normalize missing query names to allow for null\r\n  const name = query.name || ''\r\n  if (name.length > 63) {\r\n    console.error('Warning! Postgres only supports 63 characters for query names.')\r\n    console.error('You supplied %s (%s)', name, name.length)\r\n    console.error('This can cause conflicts and silent errors executing queries')\r\n  }\r\n\r\n  const types = query.types || emptyArray\r\n\r\n  const len = types.length\r\n\r\n  const buffer = writer\r\n    .addCString(name) // name of query\r\n    .addCString(query.text) // actual query text\r\n    .addInt16(len)\r\n\r\n  for (let i = 0; i < len; i++) {\r\n    buffer.addInt32(types[i])\r\n  }\r\n\r\n  return writer.flush(code.parse)\r\n}\r\n\r\ntype ValueMapper = (param: any, index: number) => any\r\n\r\ntype BindOpts = {\r\n  portal?: string\r\n  binary?: boolean\r\n  statement?: string\r\n  values?: any[]\r\n  // optional map from JS value to postgres value per parameter\r\n  valueMapper?: ValueMapper\r\n}\r\n\r\nconst paramWriter = new Writer()\r\n\r\n// make this a const enum so typescript will inline the value\r\nconst enum ParamType {\r\n  STRING = 0,\r\n  BINARY = 1,\r\n}\r\n\r\nconst writeValues = function (values: any[], valueMapper?: ValueMapper): void {\r\n  for (let i = 0; i < values.length; i++) {\r\n    const mappedVal = valueMapper ? valueMapper(values[i], i) : values[i]\r\n    if (mappedVal == null) {\r\n      // add the param type (string) to the writer\r\n      writer.addInt16(ParamType.STRING)\r\n      // write -1 to the param writer to indicate null\r\n      paramWriter.addInt32(-1)\r\n    } else if (mappedVal instanceof Buffer) {\r\n      // add the param type (binary) to the writer\r\n      writer.addInt16(ParamType.BINARY)\r\n      // add the buffer to the param writer\r\n      paramWriter.addInt32(mappedVal.length)\r\n      paramWriter.add(mappedVal)\r\n    } else {\r\n      // add the param type (string) to the writer\r\n      writer.addInt16(ParamType.STRING)\r\n      paramWriter.addInt32(Buffer.byteLength(mappedVal))\r\n      paramWriter.addString(mappedVal)\r\n    }\r\n  }\r\n}\r\n\r\nconst bind = (config: BindOpts = {}): Buffer => {\r\n  // normalize config\r\n  const portal = config.portal || ''\r\n  const statement = config.statement || ''\r\n  const binary = config.binary || false\r\n  const values = config.values || emptyArray\r\n  const len = values.length\r\n\r\n  writer.addCString(portal).addCString(statement)\r\n  writer.addInt16(len)\r\n\r\n  writeValues(values, config.valueMapper)\r\n\r\n  writer.addInt16(len)\r\n  writer.add(paramWriter.flush())\r\n\r\n  // format code\r\n  writer.addInt16(binary ? ParamType.BINARY : ParamType.STRING)\r\n  return writer.flush(code.bind)\r\n}\r\n\r\ntype ExecOpts = {\r\n  portal?: string\r\n  rows?: number\r\n}\r\n\r\nconst emptyExecute = Buffer.from([code.execute, 0x00, 0x00, 0x00, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00])\r\n\r\nconst execute = (config?: ExecOpts): Buffer => {\r\n  // this is the happy path for most queries\r\n  if (!config || (!config.portal && !config.rows)) {\r\n    return emptyExecute\r\n  }\r\n\r\n  const portal = config.portal || ''\r\n  const rows = config.rows || 0\r\n\r\n  const portalLength = Buffer.byteLength(portal)\r\n  const len = 4 + portalLength + 1 + 4\r\n  // one extra bit for code\r\n  const buff = Buffer.allocUnsafe(1 + len)\r\n  buff[0] = code.execute\r\n  buff.writeInt32BE(len, 1)\r\n  buff.write(portal, 5, 'utf-8')\r\n  buff[portalLength + 5] = 0 // null terminate portal cString\r\n  buff.writeUInt32BE(rows, buff.length - 4)\r\n  return buff\r\n}\r\n\r\nconst cancel = (processID: number, secretKey: number): Buffer => {\r\n  const buffer = Buffer.allocUnsafe(16)\r\n  buffer.writeInt32BE(16, 0)\r\n  buffer.writeInt16BE(1234, 4)\r\n  buffer.writeInt16BE(5678, 6)\r\n  buffer.writeInt32BE(processID, 8)\r\n  buffer.writeInt32BE(secretKey, 12)\r\n  return buffer\r\n}\r\n\r\ntype PortalOpts = {\r\n  type: 'S' | 'P'\r\n  name?: string\r\n}\r\n\r\nconst cstringMessage = (code: code, string: string): Buffer => {\r\n  const stringLen = Buffer.byteLength(string)\r\n  const len = 4 + stringLen + 1\r\n  // one extra bit for code\r\n  const buffer = Buffer.allocUnsafe(1 + len)\r\n  buffer[0] = code\r\n  buffer.writeInt32BE(len, 1)\r\n  buffer.write(string, 5, 'utf-8')\r\n  buffer[len] = 0 // null terminate cString\r\n  return buffer\r\n}\r\n\r\nconst emptyDescribePortal = writer.addCString('P').flush(code.describe)\r\nconst emptyDescribeStatement = writer.addCString('S').flush(code.describe)\r\n\r\nconst describe = (msg: PortalOpts): Buffer => {\r\n  return msg.name\r\n    ? cstringMessage(code.describe, `${msg.type}${msg.name || ''}`)\r\n    : msg.type === 'P'\r\n    ? emptyDescribePortal\r\n    : emptyDescribeStatement\r\n}\r\n\r\nconst close = (msg: PortalOpts): Buffer => {\r\n  const text = `${msg.type}${msg.name || ''}`\r\n  return cstringMessage(code.close, text)\r\n}\r\n\r\nconst copyData = (chunk: Buffer): Buffer => {\r\n  return writer.add(chunk).flush(code.copyFromChunk)\r\n}\r\n\r\nconst copyFail = (message: string): Buffer => {\r\n  return cstringMessage(code.copyFail, message)\r\n}\r\n\r\nconst codeOnlyBuffer = (code: code): Buffer => Buffer.from([code, 0x00, 0x00, 0x00, 0x04])\r\n\r\nconst flushBuffer = codeOnlyBuffer(code.flush)\r\nconst syncBuffer = codeOnlyBuffer(code.sync)\r\nconst endBuffer = codeOnlyBuffer(code.end)\r\nconst copyDoneBuffer = codeOnlyBuffer(code.copyDone)\r\n\r\nconst serialize = {\r\n  startup,\r\n  password,\r\n  requestSsl,\r\n  sendSASLInitialResponseMessage,\r\n  sendSCRAMClientFinalMessage,\r\n  query,\r\n  parse,\r\n  bind,\r\n  execute,\r\n  describe,\r\n  close,\r\n  flush: () => flushBuffer,\r\n  sync: () => syncBuffer,\r\n  end: () => endBuffer,\r\n  copyData,\r\n  copyDone: () => copyDoneBuffer,\r\n  copyFail,\r\n  cancel,\r\n}\r\n\r\nexport { serialize }\r\n", "const emptyBuffer = Buffer.allocUnsafe(0)\r\n\r\nexport class Buffer<PERSON>eader {\r\n  private buffer: Buffer = emptyBuffer\r\n\r\n  // TODO(bmc): support non-utf8 encoding?\r\n  private encoding: string = 'utf-8'\r\n\r\n  constructor(private offset: number = 0) {}\r\n\r\n  public setBuffer(offset: number, buffer: Buffer): void {\r\n    this.offset = offset\r\n    this.buffer = buffer\r\n  }\r\n\r\n  public int16(): number {\r\n    const result = this.buffer.readInt16BE(this.offset)\r\n    this.offset += 2\r\n    return result\r\n  }\r\n\r\n  public byte(): number {\r\n    const result = this.buffer[this.offset]\r\n    this.offset++\r\n    return result\r\n  }\r\n\r\n  public int32(): number {\r\n    const result = this.buffer.readInt32BE(this.offset)\r\n    this.offset += 4\r\n    return result\r\n  }\r\n\r\n  public uint32(): number {\r\n    const result = this.buffer.readUInt32BE(this.offset)\r\n    this.offset += 4\r\n    return result\r\n  }\r\n\r\n  public string(length: number): string {\r\n    const result = this.buffer.toString(this.encoding, this.offset, this.offset + length)\r\n    this.offset += length\r\n    return result\r\n  }\r\n\r\n  public cstring(): string {\r\n    const start = this.offset\r\n    let end = start\r\n    // eslint-disable-next-line no-empty\r\n    while (this.buffer[end++] !== 0) {}\r\n    this.offset = end\r\n    return this.buffer.toString(this.encoding, start, end - 1)\r\n  }\r\n\r\n  public bytes(length: number): Buffer {\r\n    const result = this.buffer.slice(this.offset, this.offset + length)\r\n    this.offset += length\r\n    return result\r\n  }\r\n}\r\n", "import { TransformOptions } from 'stream'\r\nimport {\r\n  Mode,\r\n  bindComplete,\r\n  parseComplete,\r\n  closeComplete,\r\n  noData,\r\n  portalSuspended,\r\n  copyDone,\r\n  replicationStart,\r\n  emptyQuery,\r\n  ReadyForQueryMessage,\r\n  CommandCompleteMessage,\r\n  CopyDataMessage,\r\n  CopyResponse,\r\n  NotificationResponseMessage,\r\n  RowDescriptionMessage,\r\n  ParameterDescriptionMessage,\r\n  Field,\r\n  DataRowMessage,\r\n  ParameterStatusMessage,\r\n  BackendKeyDataMessage,\r\n  DatabaseError,\r\n  BackendMessage,\r\n  MessageName,\r\n  AuthenticationMD5Password,\r\n  NoticeMessage,\r\n} from './messages'\r\nimport { BufferReader } from './buffer-reader'\r\n\r\n// every message is prefixed with a single bye\r\nconst CODE_LENGTH = 1\r\n// every message has an int32 length which includes itself but does\r\n// NOT include the code in the length\r\nconst LEN_LENGTH = 4\r\n\r\nconst HEADER_LENGTH = CODE_LENGTH + LEN_LENGTH\r\n\r\nexport type Packet = {\r\n  code: number\r\n  packet: Buffer\r\n}\r\n\r\nconst emptyBuffer = Buffer.allocUnsafe(0)\r\n\r\ntype StreamOptions = TransformOptions & {\r\n  mode: Mode\r\n}\r\n\r\nconst enum MessageCodes {\r\n  DataRow = 0x44, // D\r\n  ParseComplete = 0x31, // 1\r\n  BindComplete = 0x32, // 2\r\n  CloseComplete = 0x33, // 3\r\n  CommandComplete = 0x43, // C\r\n  ReadyForQuery = 0x5a, // Z\r\n  NoData = 0x6e, // n\r\n  NotificationResponse = 0x41, // A\r\n  AuthenticationResponse = 0x52, // R\r\n  ParameterStatus = 0x53, // S\r\n  BackendKeyData = 0x4b, // K\r\n  ErrorMessage = 0x45, // E\r\n  NoticeMessage = 0x4e, // N\r\n  RowDescriptionMessage = 0x54, // T\r\n  ParameterDescriptionMessage = 0x74, // t\r\n  PortalSuspended = 0x73, // s\r\n  ReplicationStart = 0x57, // W\r\n  EmptyQuery = 0x49, // I\r\n  CopyIn = 0x47, // G\r\n  CopyOut = 0x48, // H\r\n  CopyDone = 0x63, // c\r\n  CopyData = 0x64, // d\r\n}\r\n\r\nexport type MessageCallback = (msg: BackendMessage) => void\r\n\r\nexport class Parser {\r\n  private buffer: Buffer = emptyBuffer\r\n  private bufferLength: number = 0\r\n  private bufferOffset: number = 0\r\n  private reader = new BufferReader()\r\n  private mode: Mode\r\n\r\n  constructor(opts?: StreamOptions) {\r\n    if (opts?.mode === 'binary') {\r\n      throw new Error('Binary mode not supported yet')\r\n    }\r\n    this.mode = opts?.mode || 'text'\r\n  }\r\n\r\n  public parse(buffer: Buffer, callback: MessageCallback) {\r\n    this.mergeBuffer(buffer)\r\n    const bufferFullLength = this.bufferOffset + this.bufferLength\r\n    let offset = this.bufferOffset\r\n    while (offset + HEADER_LENGTH <= bufferFullLength) {\r\n      // code is 1 byte long - it identifies the message type\r\n      const code = this.buffer[offset]\r\n      // length is 1 Uint32BE - it is the length of the message EXCLUDING the code\r\n      const length = this.buffer.readUInt32BE(offset + CODE_LENGTH)\r\n      const fullMessageLength = CODE_LENGTH + length\r\n      if (fullMessageLength + offset <= bufferFullLength) {\r\n        const message = this.handlePacket(offset + HEADER_LENGTH, code, length, this.buffer)\r\n        callback(message)\r\n        offset += fullMessageLength\r\n      } else {\r\n        break\r\n      }\r\n    }\r\n    if (offset === bufferFullLength) {\r\n      // No more use for the buffer\r\n      this.buffer = emptyBuffer\r\n      this.bufferLength = 0\r\n      this.bufferOffset = 0\r\n    } else {\r\n      // Adjust the cursors of remainingBuffer\r\n      this.bufferLength = bufferFullLength - offset\r\n      this.bufferOffset = offset\r\n    }\r\n  }\r\n\r\n  private mergeBuffer(buffer: Buffer): void {\r\n    if (this.bufferLength > 0) {\r\n      const newLength = this.bufferLength + buffer.byteLength\r\n      const newFullLength = newLength + this.bufferOffset\r\n      if (newFullLength > this.buffer.byteLength) {\r\n        // We can't concat the new buffer with the remaining one\r\n        let newBuffer: Buffer\r\n        if (newLength <= this.buffer.byteLength && this.bufferOffset >= this.bufferLength) {\r\n          // We can move the relevant part to the beginning of the buffer instead of allocating a new buffer\r\n          newBuffer = this.buffer\r\n        } else {\r\n          // Allocate a new larger buffer\r\n          let newBufferLength = this.buffer.byteLength * 2\r\n          while (newLength >= newBufferLength) {\r\n            newBufferLength *= 2\r\n          }\r\n          newBuffer = Buffer.allocUnsafe(newBufferLength)\r\n        }\r\n        // Move the remaining buffer to the new one\r\n        this.buffer.copy(newBuffer, 0, this.bufferOffset, this.bufferOffset + this.bufferLength)\r\n        this.buffer = newBuffer\r\n        this.bufferOffset = 0\r\n      }\r\n      // Concat the new buffer with the remaining one\r\n      buffer.copy(this.buffer, this.bufferOffset + this.bufferLength)\r\n      this.bufferLength = newLength\r\n    } else {\r\n      this.buffer = buffer\r\n      this.bufferOffset = 0\r\n      this.bufferLength = buffer.byteLength\r\n    }\r\n  }\r\n\r\n  private handlePacket(offset: number, code: number, length: number, bytes: Buffer): BackendMessage {\r\n    switch (code) {\r\n      case MessageCodes.BindComplete:\r\n        return bindComplete\r\n      case MessageCodes.ParseComplete:\r\n        return parseComplete\r\n      case MessageCodes.CloseComplete:\r\n        return closeComplete\r\n      case MessageCodes.NoData:\r\n        return noData\r\n      case MessageCodes.PortalSuspended:\r\n        return portalSuspended\r\n      case MessageCodes.CopyDone:\r\n        return copyDone\r\n      case MessageCodes.ReplicationStart:\r\n        return replicationStart\r\n      case MessageCodes.EmptyQuery:\r\n        return emptyQuery\r\n      case MessageCodes.DataRow:\r\n        return this.parseDataRowMessage(offset, length, bytes)\r\n      case MessageCodes.CommandComplete:\r\n        return this.parseCommandCompleteMessage(offset, length, bytes)\r\n      case MessageCodes.ReadyForQuery:\r\n        return this.parseReadyForQueryMessage(offset, length, bytes)\r\n      case MessageCodes.NotificationResponse:\r\n        return this.parseNotificationMessage(offset, length, bytes)\r\n      case MessageCodes.AuthenticationResponse:\r\n        return this.parseAuthenticationResponse(offset, length, bytes)\r\n      case MessageCodes.ParameterStatus:\r\n        return this.parseParameterStatusMessage(offset, length, bytes)\r\n      case MessageCodes.BackendKeyData:\r\n        return this.parseBackendKeyData(offset, length, bytes)\r\n      case MessageCodes.ErrorMessage:\r\n        return this.parseErrorMessage(offset, length, bytes, 'error')\r\n      case MessageCodes.NoticeMessage:\r\n        return this.parseErrorMessage(offset, length, bytes, 'notice')\r\n      case MessageCodes.RowDescriptionMessage:\r\n        return this.parseRowDescriptionMessage(offset, length, bytes)\r\n      case MessageCodes.ParameterDescriptionMessage:\r\n        return this.parseParameterDescriptionMessage(offset, length, bytes)\r\n      case MessageCodes.CopyIn:\r\n        return this.parseCopyInMessage(offset, length, bytes)\r\n      case MessageCodes.CopyOut:\r\n        return this.parseCopyOutMessage(offset, length, bytes)\r\n      case MessageCodes.CopyData:\r\n        return this.parseCopyData(offset, length, bytes)\r\n      default:\r\n        return new DatabaseError('received invalid response: ' + code.toString(16), length, 'error')\r\n    }\r\n  }\r\n\r\n  private parseReadyForQueryMessage(offset: number, length: number, bytes: Buffer) {\r\n    this.reader.setBuffer(offset, bytes)\r\n    const status = this.reader.string(1)\r\n    return new ReadyForQueryMessage(length, status)\r\n  }\r\n\r\n  private parseCommandCompleteMessage(offset: number, length: number, bytes: Buffer) {\r\n    this.reader.setBuffer(offset, bytes)\r\n    const text = this.reader.cstring()\r\n    return new CommandCompleteMessage(length, text)\r\n  }\r\n\r\n  private parseCopyData(offset: number, length: number, bytes: Buffer) {\r\n    const chunk = bytes.slice(offset, offset + (length - 4))\r\n    return new CopyDataMessage(length, chunk)\r\n  }\r\n\r\n  private parseCopyInMessage(offset: number, length: number, bytes: Buffer) {\r\n    return this.parseCopyMessage(offset, length, bytes, 'copyInResponse')\r\n  }\r\n\r\n  private parseCopyOutMessage(offset: number, length: number, bytes: Buffer) {\r\n    return this.parseCopyMessage(offset, length, bytes, 'copyOutResponse')\r\n  }\r\n\r\n  private parseCopyMessage(offset: number, length: number, bytes: Buffer, messageName: MessageName) {\r\n    this.reader.setBuffer(offset, bytes)\r\n    const isBinary = this.reader.byte() !== 0\r\n    const columnCount = this.reader.int16()\r\n    const message = new CopyResponse(length, messageName, isBinary, columnCount)\r\n    for (let i = 0; i < columnCount; i++) {\r\n      message.columnTypes[i] = this.reader.int16()\r\n    }\r\n    return message\r\n  }\r\n\r\n  private parseNotificationMessage(offset: number, length: number, bytes: Buffer) {\r\n    this.reader.setBuffer(offset, bytes)\r\n    const processId = this.reader.int32()\r\n    const channel = this.reader.cstring()\r\n    const payload = this.reader.cstring()\r\n    return new NotificationResponseMessage(length, processId, channel, payload)\r\n  }\r\n\r\n  private parseRowDescriptionMessage(offset: number, length: number, bytes: Buffer) {\r\n    this.reader.setBuffer(offset, bytes)\r\n    const fieldCount = this.reader.int16()\r\n    const message = new RowDescriptionMessage(length, fieldCount)\r\n    for (let i = 0; i < fieldCount; i++) {\r\n      message.fields[i] = this.parseField()\r\n    }\r\n    return message\r\n  }\r\n\r\n  private parseField(): Field {\r\n    const name = this.reader.cstring()\r\n    const tableID = this.reader.uint32()\r\n    const columnID = this.reader.int16()\r\n    const dataTypeID = this.reader.uint32()\r\n    const dataTypeSize = this.reader.int16()\r\n    const dataTypeModifier = this.reader.int32()\r\n    const mode = this.reader.int16() === 0 ? 'text' : 'binary'\r\n    return new Field(name, tableID, columnID, dataTypeID, dataTypeSize, dataTypeModifier, mode)\r\n  }\r\n\r\n  private parseParameterDescriptionMessage(offset: number, length: number, bytes: Buffer) {\r\n    this.reader.setBuffer(offset, bytes)\r\n    const parameterCount = this.reader.int16()\r\n    const message = new ParameterDescriptionMessage(length, parameterCount)\r\n    for (let i = 0; i < parameterCount; i++) {\r\n      message.dataTypeIDs[i] = this.reader.int32()\r\n    }\r\n    return message\r\n  }\r\n\r\n  private parseDataRowMessage(offset: number, length: number, bytes: Buffer) {\r\n    this.reader.setBuffer(offset, bytes)\r\n    const fieldCount = this.reader.int16()\r\n    const fields: any[] = new Array(fieldCount)\r\n    for (let i = 0; i < fieldCount; i++) {\r\n      const len = this.reader.int32()\r\n      // a -1 for length means the value of the field is null\r\n      fields[i] = len === -1 ? null : this.reader.string(len)\r\n    }\r\n    return new DataRowMessage(length, fields)\r\n  }\r\n\r\n  private parseParameterStatusMessage(offset: number, length: number, bytes: Buffer) {\r\n    this.reader.setBuffer(offset, bytes)\r\n    const name = this.reader.cstring()\r\n    const value = this.reader.cstring()\r\n    return new ParameterStatusMessage(length, name, value)\r\n  }\r\n\r\n  private parseBackendKeyData(offset: number, length: number, bytes: Buffer) {\r\n    this.reader.setBuffer(offset, bytes)\r\n    const processID = this.reader.int32()\r\n    const secretKey = this.reader.int32()\r\n    return new BackendKeyDataMessage(length, processID, secretKey)\r\n  }\r\n\r\n  public parseAuthenticationResponse(offset: number, length: number, bytes: Buffer) {\r\n    this.reader.setBuffer(offset, bytes)\r\n    const code = this.reader.int32()\r\n    // TODO(bmc): maybe better types here\r\n    const message: BackendMessage & any = {\r\n      name: 'authenticationOk',\r\n      length,\r\n    }\r\n\r\n    switch (code) {\r\n      case 0: // AuthenticationOk\r\n        break\r\n      case 3: // AuthenticationCleartextPassword\r\n        if (message.length === 8) {\r\n          message.name = 'authenticationCleartextPassword'\r\n        }\r\n        break\r\n      case 5: // AuthenticationMD5Password\r\n        if (message.length === 12) {\r\n          message.name = 'authenticationMD5Password'\r\n          const salt = this.reader.bytes(4)\r\n          return new AuthenticationMD5Password(length, salt)\r\n        }\r\n        break\r\n      case 10: // AuthenticationSASL\r\n        {\r\n          message.name = 'authenticationSASL'\r\n          message.mechanisms = []\r\n          let mechanism: string\r\n          do {\r\n            mechanism = this.reader.cstring()\r\n            if (mechanism) {\r\n              message.mechanisms.push(mechanism)\r\n            }\r\n          } while (mechanism)\r\n        }\r\n        break\r\n      case 11: // AuthenticationSASLContinue\r\n        message.name = 'authenticationSASLContinue'\r\n        message.data = this.reader.string(length - 8)\r\n        break\r\n      case 12: // AuthenticationSASLFinal\r\n        message.name = 'authenticationSASLFinal'\r\n        message.data = this.reader.string(length - 8)\r\n        break\r\n      default:\r\n        throw new Error('Unknown authenticationOk message type ' + code)\r\n    }\r\n    return message\r\n  }\r\n\r\n  private parseErrorMessage(offset: number, length: number, bytes: Buffer, name: MessageName) {\r\n    this.reader.setBuffer(offset, bytes)\r\n    const fields: Record<string, string> = {}\r\n    let fieldType = this.reader.string(1)\r\n    while (fieldType !== '\\0') {\r\n      fields[fieldType] = this.reader.cstring()\r\n      fieldType = this.reader.string(1)\r\n    }\r\n\r\n    const messageValue = fields.M\r\n\r\n    const message =\r\n      name === 'notice' ? new NoticeMessage(length, messageValue) : new DatabaseError(messageValue, length, name)\r\n\r\n    message.severity = fields.S\r\n    message.code = fields.C\r\n    message.detail = fields.D\r\n    message.hint = fields.H\r\n    message.position = fields.P\r\n    message.internalPosition = fields.p\r\n    message.internalQuery = fields.q\r\n    message.where = fields.W\r\n    message.schema = fields.s\r\n    message.table = fields.t\r\n    message.column = fields.c\r\n    message.dataType = fields.d\r\n    message.constraint = fields.n\r\n    message.file = fields.F\r\n    message.line = fields.L\r\n    message.routine = fields.R\r\n    return message\r\n  }\r\n}\r\n", "import { DatabaseError } from './messages'\r\nimport { serialize } from './serializer'\r\nimport { Parser, MessageCallback } from './parser'\r\n\r\nexport function parse(stream: NodeJS.ReadableStream, callback: MessageCallback): Promise<void> {\r\n  const parser = new Parser()\r\n  stream.on('data', (buffer: Buffer) => parser.parse(buffer, callback))\r\n  return new Promise((resolve) => stream.on('end', () => resolve()))\r\n}\r\n\r\nexport { serialize, DatabaseError }\r\n", "// This is an empty module that is served up when outside of a workerd environment\r\n// See the `exports` field in package.json\r\nexport default {}\r\n", "const { getStream, getSecureStream } = getStreamFuncs()\r\n\r\nmodule.exports = {\r\n  /**\r\n   * Get a socket stream compatible with the current runtime environment.\r\n   * @returns {Duplex}\r\n   */\r\n  getStream,\r\n  /**\r\n   * Get a TLS secured socket, compatible with the current environment,\r\n   * using the socket and other settings given in `options`.\r\n   * @returns {Duplex}\r\n   */\r\n  getSecureStream,\r\n}\r\n\r\n/**\r\n * The stream functions that work in Node.js\r\n */\r\nfunction getNodejsStreamFuncs() {\r\n  function getStream(ssl) {\r\n    const net = require('net')\r\n    return new net.Socket()\r\n  }\r\n\r\n  function getSecureStream(options) {\r\n    const tls = require('tls')\r\n    return tls.connect(options)\r\n  }\r\n  return {\r\n    getStream,\r\n    getSecureStream,\r\n  }\r\n}\r\n\r\n/**\r\n * The stream functions that work in Cloudflare Workers\r\n */\r\nfunction getCloudflareStreamFuncs() {\r\n  function getStream(ssl) {\r\n    const { CloudflareSocket } = require('pg-cloudflare')\r\n    return new CloudflareSocket(ssl)\r\n  }\r\n\r\n  function getSecureStream(options) {\r\n    options.socket.startTls(options)\r\n    return options.socket\r\n  }\r\n  return {\r\n    getStream,\r\n    getSecureStream,\r\n  }\r\n}\r\n\r\n/**\r\n * Are we running in a Cloudflare Worker?\r\n *\r\n * @returns true if the code is currently running inside a Cloudflare Worker.\r\n */\r\nfunction isCloudflareRuntime() {\r\n  // Since 2022-03-21 the `global_navigator` compatibility flag is on for Cloudflare Workers\r\n  // which means that `navigator.userAgent` will be defined.\r\n  // eslint-disable-next-line no-undef\r\n  if (typeof navigator === 'object' && navigator !== null && typeof navigator.userAgent === 'string') {\r\n    // eslint-disable-next-line no-undef\r\n    return navigator.userAgent === 'Cloudflare-Workers'\r\n  }\r\n  // In case `navigator` or `navigator.userAgent` is not defined then try a more sneaky approach\r\n  if (typeof Response === 'function') {\r\n    const resp = new Response(null, { cf: { thing: true } })\r\n    if (typeof resp.cf === 'object' && resp.cf !== null && resp.cf.thing) {\r\n      return true\r\n    }\r\n  }\r\n  return false\r\n}\r\n\r\nfunction getStreamFuncs() {\r\n  if (isCloudflareRuntime()) {\r\n    return getCloudflareStreamFuncs()\r\n  }\r\n  return getNodejsStreamFuncs()\r\n}\r\n", "'use strict'\r\n\r\nconst EventEmitter = require('events').EventEmitter\r\n\r\nconst { parse, serialize } = require('pg-protocol')\r\nconst { getStream, getSecureStream } = require('./stream')\r\n\r\nconst flushBuffer = serialize.flush()\r\nconst syncBuffer = serialize.sync()\r\nconst endBuffer = serialize.end()\r\n\r\n// TODO(bmc) support binary mode at some point\r\nclass Connection extends EventEmitter {\r\n  constructor(config) {\r\n    super()\r\n    config = config || {}\r\n\r\n    this.stream = config.stream || getStream(config.ssl)\r\n    if (typeof this.stream === 'function') {\r\n      this.stream = this.stream(config)\r\n    }\r\n\r\n    this._keepAlive = config.keepAlive\r\n    this._keepAliveInitialDelayMillis = config.keepAliveInitialDelayMillis\r\n    this.lastBuffer = false\r\n    this.parsedStatements = {}\r\n    this.ssl = config.ssl || false\r\n    this._ending = false\r\n    this._emitMessage = false\r\n    const self = this\r\n    this.on('newListener', function (eventName) {\r\n      if (eventName === 'message') {\r\n        self._emitMessage = true\r\n      }\r\n    })\r\n  }\r\n\r\n  connect(port, host) {\r\n    const self = this\r\n\r\n    this._connecting = true\r\n    this.stream.setNoDelay(true)\r\n    this.stream.connect(port, host)\r\n\r\n    this.stream.once('connect', function () {\r\n      if (self._keepAlive) {\r\n        self.stream.setKeepAlive(true, self._keepAliveInitialDelayMillis)\r\n      }\r\n      self.emit('connect')\r\n    })\r\n\r\n    const reportStreamError = function (error) {\r\n      // errors about disconnections should be ignored during disconnect\r\n      if (self._ending && (error.code === 'ECONNRESET' || error.code === 'EPIPE')) {\r\n        return\r\n      }\r\n      self.emit('error', error)\r\n    }\r\n    this.stream.on('error', reportStreamError)\r\n\r\n    this.stream.on('close', function () {\r\n      self.emit('end')\r\n    })\r\n\r\n    if (!this.ssl) {\r\n      return this.attachListeners(this.stream)\r\n    }\r\n\r\n    this.stream.once('data', function (buffer) {\r\n      const responseCode = buffer.toString('utf8')\r\n      switch (responseCode) {\r\n        case 'S': // Server supports SSL connections, continue with a secure connection\r\n          break\r\n        case 'N': // Server does not support SSL connections\r\n          self.stream.end()\r\n          return self.emit('error', new Error('The server does not support SSL connections'))\r\n        default:\r\n          // Any other response byte, including 'E' (ErrorResponse) indicating a server error\r\n          self.stream.end()\r\n          return self.emit('error', new Error('There was an error establishing an SSL connection'))\r\n      }\r\n      const options = {\r\n        socket: self.stream,\r\n      }\r\n\r\n      if (self.ssl !== true) {\r\n        Object.assign(options, self.ssl)\r\n\r\n        if ('key' in self.ssl) {\r\n          options.key = self.ssl.key\r\n        }\r\n      }\r\n\r\n      const net = require('net')\r\n      if (net.isIP && net.isIP(host) === 0) {\r\n        options.servername = host\r\n      }\r\n      try {\r\n        self.stream = getSecureStream(options)\r\n      } catch (err) {\r\n        return self.emit('error', err)\r\n      }\r\n      self.attachListeners(self.stream)\r\n      self.stream.on('error', reportStreamError)\r\n\r\n      self.emit('sslconnect')\r\n    })\r\n  }\r\n\r\n  attachListeners(stream) {\r\n    parse(stream, (msg) => {\r\n      const eventName = msg.name === 'error' ? 'errorMessage' : msg.name\r\n      if (this._emitMessage) {\r\n        this.emit('message', msg)\r\n      }\r\n      this.emit(eventName, msg)\r\n    })\r\n  }\r\n\r\n  requestSsl() {\r\n    this.stream.write(serialize.requestSsl())\r\n  }\r\n\r\n  startup(config) {\r\n    this.stream.write(serialize.startup(config))\r\n  }\r\n\r\n  cancel(processID, secretKey) {\r\n    this._send(serialize.cancel(processID, secretKey))\r\n  }\r\n\r\n  password(password) {\r\n    this._send(serialize.password(password))\r\n  }\r\n\r\n  sendSASLInitialResponseMessage(mechanism, initialResponse) {\r\n    this._send(serialize.sendSASLInitialResponseMessage(mechanism, initialResponse))\r\n  }\r\n\r\n  sendSCRAMClientFinalMessage(additionalData) {\r\n    this._send(serialize.sendSCRAMClientFinalMessage(additionalData))\r\n  }\r\n\r\n  _send(buffer) {\r\n    if (!this.stream.writable) {\r\n      return false\r\n    }\r\n    return this.stream.write(buffer)\r\n  }\r\n\r\n  query(text) {\r\n    this._send(serialize.query(text))\r\n  }\r\n\r\n  // send parse message\r\n  parse(query) {\r\n    this._send(serialize.parse(query))\r\n  }\r\n\r\n  // send bind message\r\n  bind(config) {\r\n    this._send(serialize.bind(config))\r\n  }\r\n\r\n  // send execute message\r\n  execute(config) {\r\n    this._send(serialize.execute(config))\r\n  }\r\n\r\n  flush() {\r\n    if (this.stream.writable) {\r\n      this.stream.write(flushBuffer)\r\n    }\r\n  }\r\n\r\n  sync() {\r\n    this._ending = true\r\n    this._send(syncBuffer)\r\n  }\r\n\r\n  ref() {\r\n    this.stream.ref()\r\n  }\r\n\r\n  unref() {\r\n    this.stream.unref()\r\n  }\r\n\r\n  end() {\r\n    // 0x58 = 'X'\r\n    this._ending = true\r\n    if (!this._connecting || !this.stream.writable) {\r\n      this.stream.end()\r\n      return\r\n    }\r\n    return this.stream.write(endBuffer, () => {\r\n      this.stream.end()\r\n    })\r\n  }\r\n\r\n  close(msg) {\r\n    this._send(serialize.close(msg))\r\n  }\r\n\r\n  describe(msg) {\r\n    this._send(serialize.describe(msg))\r\n  }\r\n\r\n  sendCopyFromChunk(chunk) {\r\n    this._send(serialize.copyData(chunk))\r\n  }\r\n\r\n  endCopyFrom() {\r\n    this._send(serialize.copyDone())\r\n  }\r\n\r\n  sendCopyFail(msg) {\r\n    this._send(serialize.copyFail(msg))\r\n  }\r\n}\r\n\r\nmodule.exports = Connection\r\n", "/*\r\nCopyright (c) 2014-2021, <PERSON> <<EMAIL>>\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted, provided that the above\r\ncopyright notice and this permission notice appear in all copies.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\r\nWITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\r\nMERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\r\nANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\r\nWHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\r\nACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR\r\nIN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\r\n*/\r\n\r\n'use strict'\r\n\r\nconst { Transform } = require('stream')\r\nconst { StringDecoder } = require('string_decoder')\r\nconst kLast = Symbol('last')\r\nconst kDecoder = Symbol('decoder')\r\n\r\nfunction transform (chunk, enc, cb) {\r\n  let list\r\n  if (this.overflow) { // Line buffer is full. Skip to start of next line.\r\n    const buf = this[kDecoder].write(chunk)\r\n    list = buf.split(this.matcher)\r\n\r\n    if (list.length === 1) return cb() // Line ending not found. Discard entire chunk.\r\n\r\n    // Line ending found. Discard trailing fragment of previous line and reset overflow state.\r\n    list.shift()\r\n    this.overflow = false\r\n  } else {\r\n    this[kLast] += this[kDecoder].write(chunk)\r\n    list = this[kLast].split(this.matcher)\r\n  }\r\n\r\n  this[kLast] = list.pop()\r\n\r\n  for (let i = 0; i < list.length; i++) {\r\n    try {\r\n      push(this, this.mapper(list[i]))\r\n    } catch (error) {\r\n      return cb(error)\r\n    }\r\n  }\r\n\r\n  this.overflow = this[kLast].length > this.maxLength\r\n  if (this.overflow && !this.skipOverflow) {\r\n    cb(new Error('maximum buffer reached'))\r\n    return\r\n  }\r\n\r\n  cb()\r\n}\r\n\r\nfunction flush (cb) {\r\n  // forward any gibberish left in there\r\n  this[kLast] += this[kDecoder].end()\r\n\r\n  if (this[kLast]) {\r\n    try {\r\n      push(this, this.mapper(this[kLast]))\r\n    } catch (error) {\r\n      return cb(error)\r\n    }\r\n  }\r\n\r\n  cb()\r\n}\r\n\r\nfunction push (self, val) {\r\n  if (val !== undefined) {\r\n    self.push(val)\r\n  }\r\n}\r\n\r\nfunction noop (incoming) {\r\n  return incoming\r\n}\r\n\r\nfunction split (matcher, mapper, options) {\r\n  // Set defaults for any arguments not supplied.\r\n  matcher = matcher || /\\r?\\n/\r\n  mapper = mapper || noop\r\n  options = options || {}\r\n\r\n  // Test arguments explicitly.\r\n  switch (arguments.length) {\r\n    case 1:\r\n      // If mapper is only argument.\r\n      if (typeof matcher === 'function') {\r\n        mapper = matcher\r\n        matcher = /\\r?\\n/\r\n      // If options is only argument.\r\n      } else if (typeof matcher === 'object' && !(matcher instanceof RegExp) && !matcher[Symbol.split]) {\r\n        options = matcher\r\n        matcher = /\\r?\\n/\r\n      }\r\n      break\r\n\r\n    case 2:\r\n      // If mapper and options are arguments.\r\n      if (typeof matcher === 'function') {\r\n        options = mapper\r\n        mapper = matcher\r\n        matcher = /\\r?\\n/\r\n      // If matcher and options are arguments.\r\n      } else if (typeof mapper === 'object') {\r\n        options = mapper\r\n        mapper = noop\r\n      }\r\n  }\r\n\r\n  options = Object.assign({}, options)\r\n  options.autoDestroy = true\r\n  options.transform = transform\r\n  options.flush = flush\r\n  options.readableObjectMode = true\r\n\r\n  const stream = new Transform(options)\r\n\r\n  stream[kLast] = ''\r\n  stream[kDecoder] = new StringDecoder('utf8')\r\n  stream.matcher = matcher\r\n  stream.mapper = mapper\r\n  stream.maxLength = options.maxLength\r\n  stream.skipOverflow = options.skipOverflow || false\r\n  stream.overflow = false\r\n  stream._destroy = function (err, cb) {\r\n    // Weird Node v12 bug that we need to work around\r\n    this._writableState.errorEmitted = false\r\n    cb(err)\r\n  }\r\n\r\n  return stream\r\n}\r\n\r\nmodule.exports = split\r\n", "'use strict';\r\n\r\nvar path = require('path')\r\n  , Stream = require('stream').Stream\r\n  , split = require('split2')\r\n  , util = require('util')\r\n  , defaultPort = 5432\r\n  , isWin = (process.platform === 'win32')\r\n  , warnStream = process.stderr\r\n;\r\n\r\n\r\nvar S_IRWXG = 56     //    00070(8)\r\n  , S_IRWXO = 7      //    00007(8)\r\n  , S_IFMT  = 61440  // 00170000(8)\r\n  , S_IFREG = 32768  //  0100000(8)\r\n;\r\nfunction isRegFile(mode) {\r\n    return ((mode & S_IFMT) == S_IFREG);\r\n}\r\n\r\nvar fieldNames = [ 'host', 'port', 'database', 'user', 'password' ];\r\nvar nrOfFields = fieldNames.length;\r\nvar passKey = fieldNames[ nrOfFields -1 ];\r\n\r\n\r\nfunction warn() {\r\n    var isWritable = (\r\n        warnStream instanceof Stream &&\r\n          true === warnStream.writable\r\n    );\r\n\r\n    if (isWritable) {\r\n        var args = Array.prototype.slice.call(arguments).concat(\"\\n\");\r\n        warnStream.write( util.format.apply(util, args) );\r\n    }\r\n}\r\n\r\n\r\nObject.defineProperty(module.exports, 'isWin', {\r\n    get : function() {\r\n        return isWin;\r\n    } ,\r\n    set : function(val) {\r\n        isWin = val;\r\n    }\r\n});\r\n\r\n\r\nmodule.exports.warnTo = function(stream) {\r\n    var old = warnStream;\r\n    warnStream = stream;\r\n    return old;\r\n};\r\n\r\nmodule.exports.getFileName = function(rawEnv){\r\n    var env = rawEnv || process.env;\r\n    var file = env.PGPASSFILE || (\r\n        isWin ?\r\n          path.join( env.APPDATA || './' , 'postgresql', 'pgpass.conf' ) :\r\n          path.join( env.HOME || './', '.pgpass' )\r\n    );\r\n    return file;\r\n};\r\n\r\nmodule.exports.usePgPass = function(stats, fname) {\r\n    if (Object.prototype.hasOwnProperty.call(process.env, 'PGPASSWORD')) {\r\n        return false;\r\n    }\r\n\r\n    if (isWin) {\r\n        return true;\r\n    }\r\n\r\n    fname = fname || '<unkn>';\r\n\r\n    if (! isRegFile(stats.mode)) {\r\n        warn('WARNING: password file \"%s\" is not a plain file', fname);\r\n        return false;\r\n    }\r\n\r\n    if (stats.mode & (S_IRWXG | S_IRWXO)) {\r\n        /* If password file is insecure, alert the user and ignore it. */\r\n        warn('WARNING: password file \"%s\" has group or world access; permissions should be u=rw (0600) or less', fname);\r\n        return false;\r\n    }\r\n\r\n    return true;\r\n};\r\n\r\n\r\nvar matcher = module.exports.match = function(connInfo, entry) {\r\n    return fieldNames.slice(0, -1).reduce(function(prev, field, idx){\r\n        if (idx == 1) {\r\n            // the port\r\n            if ( Number( connInfo[field] || defaultPort ) === Number( entry[field] ) ) {\r\n                return prev && true;\r\n            }\r\n        }\r\n        return prev && (\r\n            entry[field] === '*' ||\r\n              entry[field] === connInfo[field]\r\n        );\r\n    }, true);\r\n};\r\n\r\n\r\nmodule.exports.getPassword = function(connInfo, stream, cb) {\r\n    var pass;\r\n    var lineStream = stream.pipe(split());\r\n\r\n    function onLine(line) {\r\n        var entry = parseLine(line);\r\n        if (entry && isValidEntry(entry) && matcher(connInfo, entry)) {\r\n            pass = entry[passKey];\r\n            lineStream.end(); // -> calls onEnd(), but pass is set now\r\n        }\r\n    }\r\n\r\n    var onEnd = function() {\r\n        stream.destroy();\r\n        cb(pass);\r\n    };\r\n\r\n    var onErr = function(err) {\r\n        stream.destroy();\r\n        warn('WARNING: error on reading file: %s', err);\r\n        cb(undefined);\r\n    };\r\n\r\n    stream.on('error', onErr);\r\n    lineStream\r\n        .on('data', onLine)\r\n        .on('end', onEnd)\r\n        .on('error', onErr)\r\n    ;\r\n\r\n};\r\n\r\n\r\nvar parseLine = module.exports.parseLine = function(line) {\r\n    if (line.length < 11 || line.match(/^\\s+#/)) {\r\n        return null;\r\n    }\r\n\r\n    var curChar = '';\r\n    var prevChar = '';\r\n    var fieldIdx = 0;\r\n    var startIdx = 0;\r\n    var endIdx = 0;\r\n    var obj = {};\r\n    var isLastField = false;\r\n    var addToObj = function(idx, i0, i1) {\r\n        var field = line.substring(i0, i1);\r\n\r\n        if (! Object.hasOwnProperty.call(process.env, 'PGPASS_NO_DEESCAPE')) {\r\n            field = field.replace(/\\\\([:\\\\])/g, '$1');\r\n        }\r\n\r\n        obj[ fieldNames[idx] ] = field;\r\n    };\r\n\r\n    for (var i = 0 ; i < line.length-1 ; i += 1) {\r\n        curChar = line.charAt(i+1);\r\n        prevChar = line.charAt(i);\r\n\r\n        isLastField = (fieldIdx == nrOfFields-1);\r\n\r\n        if (isLastField) {\r\n            addToObj(fieldIdx, startIdx);\r\n            break;\r\n        }\r\n\r\n        if (i >= 0 && curChar == ':' && prevChar !== '\\\\') {\r\n            addToObj(fieldIdx, startIdx, i+1);\r\n\r\n            startIdx = i+2;\r\n            fieldIdx += 1;\r\n        }\r\n    }\r\n\r\n    obj = ( Object.keys(obj).length === nrOfFields ) ? obj : null;\r\n\r\n    return obj;\r\n};\r\n\r\n\r\nvar isValidEntry = module.exports.isValidEntry = function(entry){\r\n    var rules = {\r\n        // host\r\n        0 : function(x){\r\n            return x.length > 0;\r\n        } ,\r\n        // port\r\n        1 : function(x){\r\n            if (x === '*') {\r\n                return true;\r\n            }\r\n            x = Number(x);\r\n            return (\r\n                isFinite(x) &&\r\n                  x > 0 &&\r\n                  x < 9007199254740992 &&\r\n                  Math.floor(x) === x\r\n            );\r\n        } ,\r\n        // database\r\n        2 : function(x){\r\n            return x.length > 0;\r\n        } ,\r\n        // username\r\n        3 : function(x){\r\n            return x.length > 0;\r\n        } ,\r\n        // password\r\n        4 : function(x){\r\n            return x.length > 0;\r\n        }\r\n    };\r\n\r\n    for (var idx = 0 ; idx < fieldNames.length ; idx += 1) {\r\n        var rule = rules[idx];\r\n        var value = entry[ fieldNames[idx] ] || '';\r\n\r\n        var res = rule(value);\r\n        if (!res) {\r\n            return false;\r\n        }\r\n    }\r\n\r\n    return true;\r\n};\r\n\r\n", "'use strict';\r\n\r\nvar path = require('path')\r\n  , fs = require('fs')\r\n  , helper = require('./helper.js')\r\n;\r\n\r\n\r\nmodule.exports = function(connInfo, cb) {\r\n    var file = helper.getFileName();\r\n    \r\n    fs.stat(file, function(err, stat){\r\n        if (err || !helper.usePgPass(stat, file)) {\r\n            return cb(undefined);\r\n        }\r\n\r\n        var st = fs.createReadStream(file);\r\n\r\n        helper.getPassword(connInfo, st, cb);\r\n    });\r\n};\r\n\r\nmodule.exports.warnTo = helper.warnTo;\r\n", "'use strict'\r\n\r\nconst EventEmitter = require('events').EventEmitter\r\nconst utils = require('./utils')\r\nconst sasl = require('./crypto/sasl')\r\nconst TypeOverrides = require('./type-overrides')\r\n\r\nconst ConnectionParameters = require('./connection-parameters')\r\nconst Query = require('./query')\r\nconst defaults = require('./defaults')\r\nconst Connection = require('./connection')\r\nconst crypto = require('./crypto/utils')\r\n\r\nclass Client extends EventEmitter {\r\n  constructor(config) {\r\n    super()\r\n\r\n    this.connectionParameters = new ConnectionParameters(config)\r\n    this.user = this.connectionParameters.user\r\n    this.database = this.connectionParameters.database\r\n    this.port = this.connectionParameters.port\r\n    this.host = this.connectionParameters.host\r\n\r\n    // \"hiding\" the password so it doesn't show up in stack traces\r\n    // or if the client is console.logged\r\n    Object.defineProperty(this, 'password', {\r\n      configurable: true,\r\n      enumerable: false,\r\n      writable: true,\r\n      value: this.connectionParameters.password,\r\n    })\r\n\r\n    this.replication = this.connectionParameters.replication\r\n\r\n    const c = config || {}\r\n\r\n    this._Promise = c.Promise || global.Promise\r\n    this._types = new TypeOverrides(c.types)\r\n    this._ending = false\r\n    this._ended = false\r\n    this._connecting = false\r\n    this._connected = false\r\n    this._connectionError = false\r\n    this._queryable = true\r\n\r\n    this.enableChannelBinding = Boolean(c.enableChannelBinding) // set true to use SCRAM-SHA-256-PLUS when offered\r\n    this.connection =\r\n      c.connection ||\r\n      new Connection({\r\n        stream: c.stream,\r\n        ssl: this.connectionParameters.ssl,\r\n        keepAlive: c.keepAlive || false,\r\n        keepAliveInitialDelayMillis: c.keepAliveInitialDelayMillis || 0,\r\n        encoding: this.connectionParameters.client_encoding || 'utf8',\r\n      })\r\n    this.queryQueue = []\r\n    this.binary = c.binary || defaults.binary\r\n    this.processID = null\r\n    this.secretKey = null\r\n    this.ssl = this.connectionParameters.ssl || false\r\n    // As with Password, make SSL->Key (the private key) non-enumerable.\r\n    // It won't show up in stack traces\r\n    // or if the client is console.logged\r\n    if (this.ssl && this.ssl.key) {\r\n      Object.defineProperty(this.ssl, 'key', {\r\n        enumerable: false,\r\n      })\r\n    }\r\n\r\n    this._connectionTimeoutMillis = c.connectionTimeoutMillis || 0\r\n  }\r\n\r\n  _errorAllQueries(err) {\r\n    const enqueueError = (query) => {\r\n      process.nextTick(() => {\r\n        query.handleError(err, this.connection)\r\n      })\r\n    }\r\n\r\n    if (this.activeQuery) {\r\n      enqueueError(this.activeQuery)\r\n      this.activeQuery = null\r\n    }\r\n\r\n    this.queryQueue.forEach(enqueueError)\r\n    this.queryQueue.length = 0\r\n  }\r\n\r\n  _connect(callback) {\r\n    const self = this\r\n    const con = this.connection\r\n    this._connectionCallback = callback\r\n\r\n    if (this._connecting || this._connected) {\r\n      const err = new Error('Client has already been connected. You cannot reuse a client.')\r\n      process.nextTick(() => {\r\n        callback(err)\r\n      })\r\n      return\r\n    }\r\n    this._connecting = true\r\n\r\n    if (this._connectionTimeoutMillis > 0) {\r\n      this.connectionTimeoutHandle = setTimeout(() => {\r\n        con._ending = true\r\n        con.stream.destroy(new Error('timeout expired'))\r\n      }, this._connectionTimeoutMillis)\r\n\r\n      if (this.connectionTimeoutHandle.unref) {\r\n        this.connectionTimeoutHandle.unref()\r\n      }\r\n    }\r\n\r\n    if (this.host && this.host.indexOf('/') === 0) {\r\n      con.connect(this.host + '/.s.PGSQL.' + this.port)\r\n    } else {\r\n      con.connect(this.port, this.host)\r\n    }\r\n\r\n    // once connection is established send startup message\r\n    con.on('connect', function () {\r\n      if (self.ssl) {\r\n        con.requestSsl()\r\n      } else {\r\n        con.startup(self.getStartupConf())\r\n      }\r\n    })\r\n\r\n    con.on('sslconnect', function () {\r\n      con.startup(self.getStartupConf())\r\n    })\r\n\r\n    this._attachListeners(con)\r\n\r\n    con.once('end', () => {\r\n      const error = this._ending ? new Error('Connection terminated') : new Error('Connection terminated unexpectedly')\r\n\r\n      clearTimeout(this.connectionTimeoutHandle)\r\n      this._errorAllQueries(error)\r\n      this._ended = true\r\n\r\n      if (!this._ending) {\r\n        // if the connection is ended without us calling .end()\r\n        // on this client then we have an unexpected disconnection\r\n        // treat this as an error unless we've already emitted an error\r\n        // during connection.\r\n        if (this._connecting && !this._connectionError) {\r\n          if (this._connectionCallback) {\r\n            this._connectionCallback(error)\r\n          } else {\r\n            this._handleErrorEvent(error)\r\n          }\r\n        } else if (!this._connectionError) {\r\n          this._handleErrorEvent(error)\r\n        }\r\n      }\r\n\r\n      process.nextTick(() => {\r\n        this.emit('end')\r\n      })\r\n    })\r\n  }\r\n\r\n  connect(callback) {\r\n    if (callback) {\r\n      this._connect(callback)\r\n      return\r\n    }\r\n\r\n    return new this._Promise((resolve, reject) => {\r\n      this._connect((error) => {\r\n        if (error) {\r\n          reject(error)\r\n        } else {\r\n          resolve()\r\n        }\r\n      })\r\n    })\r\n  }\r\n\r\n  _attachListeners(con) {\r\n    // password request handling\r\n    con.on('authenticationCleartextPassword', this._handleAuthCleartextPassword.bind(this))\r\n    // password request handling\r\n    con.on('authenticationMD5Password', this._handleAuthMD5Password.bind(this))\r\n    // password request handling (SASL)\r\n    con.on('authenticationSASL', this._handleAuthSASL.bind(this))\r\n    con.on('authenticationSASLContinue', this._handleAuthSASLContinue.bind(this))\r\n    con.on('authenticationSASLFinal', this._handleAuthSASLFinal.bind(this))\r\n    con.on('backendKeyData', this._handleBackendKeyData.bind(this))\r\n    con.on('error', this._handleErrorEvent.bind(this))\r\n    con.on('errorMessage', this._handleErrorMessage.bind(this))\r\n    con.on('readyForQuery', this._handleReadyForQuery.bind(this))\r\n    con.on('notice', this._handleNotice.bind(this))\r\n    con.on('rowDescription', this._handleRowDescription.bind(this))\r\n    con.on('dataRow', this._handleDataRow.bind(this))\r\n    con.on('portalSuspended', this._handlePortalSuspended.bind(this))\r\n    con.on('emptyQuery', this._handleEmptyQuery.bind(this))\r\n    con.on('commandComplete', this._handleCommandComplete.bind(this))\r\n    con.on('parseComplete', this._handleParseComplete.bind(this))\r\n    con.on('copyInResponse', this._handleCopyInResponse.bind(this))\r\n    con.on('copyData', this._handleCopyData.bind(this))\r\n    con.on('notification', this._handleNotification.bind(this))\r\n  }\r\n\r\n  // TODO(bmc): deprecate pgpass \"built in\" integration since this.password can be a function\r\n  // it can be supplied by the user if required - this is a breaking change!\r\n  _checkPgPass(cb) {\r\n    const con = this.connection\r\n    if (typeof this.password === 'function') {\r\n      this._Promise\r\n        .resolve()\r\n        .then(() => this.password())\r\n        .then((pass) => {\r\n          if (pass !== undefined) {\r\n            if (typeof pass !== 'string') {\r\n              con.emit('error', new TypeError('Password must be a string'))\r\n              return\r\n            }\r\n            this.connectionParameters.password = this.password = pass\r\n          } else {\r\n            this.connectionParameters.password = this.password = null\r\n          }\r\n          cb()\r\n        })\r\n        .catch((err) => {\r\n          con.emit('error', err)\r\n        })\r\n    } else if (this.password !== null) {\r\n      cb()\r\n    } else {\r\n      try {\r\n        const pgPass = require('pgpass')\r\n        pgPass(this.connectionParameters, (pass) => {\r\n          if (undefined !== pass) {\r\n            this.connectionParameters.password = this.password = pass\r\n          }\r\n          cb()\r\n        })\r\n      } catch (e) {\r\n        this.emit('error', e)\r\n      }\r\n    }\r\n  }\r\n\r\n  _handleAuthCleartextPassword(msg) {\r\n    this._checkPgPass(() => {\r\n      this.connection.password(this.password)\r\n    })\r\n  }\r\n\r\n  _handleAuthMD5Password(msg) {\r\n    this._checkPgPass(async () => {\r\n      try {\r\n        const hashedPassword = await crypto.postgresMd5PasswordHash(this.user, this.password, msg.salt)\r\n        this.connection.password(hashedPassword)\r\n      } catch (e) {\r\n        this.emit('error', e)\r\n      }\r\n    })\r\n  }\r\n\r\n  _handleAuthSASL(msg) {\r\n    this._checkPgPass(() => {\r\n      try {\r\n        this.saslSession = sasl.startSession(msg.mechanisms, this.enableChannelBinding && this.connection.stream)\r\n        this.connection.sendSASLInitialResponseMessage(this.saslSession.mechanism, this.saslSession.response)\r\n      } catch (err) {\r\n        this.connection.emit('error', err)\r\n      }\r\n    })\r\n  }\r\n\r\n  async _handleAuthSASLContinue(msg) {\r\n    try {\r\n      await sasl.continueSession(\r\n        this.saslSession,\r\n        this.password,\r\n        msg.data,\r\n        this.enableChannelBinding && this.connection.stream\r\n      )\r\n      this.connection.sendSCRAMClientFinalMessage(this.saslSession.response)\r\n    } catch (err) {\r\n      this.connection.emit('error', err)\r\n    }\r\n  }\r\n\r\n  _handleAuthSASLFinal(msg) {\r\n    try {\r\n      sasl.finalizeSession(this.saslSession, msg.data)\r\n      this.saslSession = null\r\n    } catch (err) {\r\n      this.connection.emit('error', err)\r\n    }\r\n  }\r\n\r\n  _handleBackendKeyData(msg) {\r\n    this.processID = msg.processID\r\n    this.secretKey = msg.secretKey\r\n  }\r\n\r\n  _handleReadyForQuery(msg) {\r\n    if (this._connecting) {\r\n      this._connecting = false\r\n      this._connected = true\r\n      clearTimeout(this.connectionTimeoutHandle)\r\n\r\n      // process possible callback argument to Client#connect\r\n      if (this._connectionCallback) {\r\n        this._connectionCallback(null, this)\r\n        // remove callback for proper error handling\r\n        // after the connect event\r\n        this._connectionCallback = null\r\n      }\r\n      this.emit('connect')\r\n    }\r\n    const { activeQuery } = this\r\n    this.activeQuery = null\r\n    this.readyForQuery = true\r\n    if (activeQuery) {\r\n      activeQuery.handleReadyForQuery(this.connection)\r\n    }\r\n    this._pulseQueryQueue()\r\n  }\r\n\r\n  // if we receieve an error event or error message\r\n  // during the connection process we handle it here\r\n  _handleErrorWhileConnecting(err) {\r\n    if (this._connectionError) {\r\n      // TODO(bmc): this is swallowing errors - we shouldn't do this\r\n      return\r\n    }\r\n    this._connectionError = true\r\n    clearTimeout(this.connectionTimeoutHandle)\r\n    if (this._connectionCallback) {\r\n      return this._connectionCallback(err)\r\n    }\r\n    this.emit('error', err)\r\n  }\r\n\r\n  // if we're connected and we receive an error event from the connection\r\n  // this means the socket is dead - do a hard abort of all queries and emit\r\n  // the socket error on the client as well\r\n  _handleErrorEvent(err) {\r\n    if (this._connecting) {\r\n      return this._handleErrorWhileConnecting(err)\r\n    }\r\n    this._queryable = false\r\n    this._errorAllQueries(err)\r\n    this.emit('error', err)\r\n  }\r\n\r\n  // handle error messages from the postgres backend\r\n  _handleErrorMessage(msg) {\r\n    if (this._connecting) {\r\n      return this._handleErrorWhileConnecting(msg)\r\n    }\r\n    const activeQuery = this.activeQuery\r\n\r\n    if (!activeQuery) {\r\n      this._handleErrorEvent(msg)\r\n      return\r\n    }\r\n\r\n    this.activeQuery = null\r\n    activeQuery.handleError(msg, this.connection)\r\n  }\r\n\r\n  _handleRowDescription(msg) {\r\n    // delegate rowDescription to active query\r\n    this.activeQuery.handleRowDescription(msg)\r\n  }\r\n\r\n  _handleDataRow(msg) {\r\n    // delegate dataRow to active query\r\n    this.activeQuery.handleDataRow(msg)\r\n  }\r\n\r\n  _handlePortalSuspended(msg) {\r\n    // delegate portalSuspended to active query\r\n    this.activeQuery.handlePortalSuspended(this.connection)\r\n  }\r\n\r\n  _handleEmptyQuery(msg) {\r\n    // delegate emptyQuery to active query\r\n    this.activeQuery.handleEmptyQuery(this.connection)\r\n  }\r\n\r\n  _handleCommandComplete(msg) {\r\n    if (this.activeQuery == null) {\r\n      const error = new Error('Received unexpected commandComplete message from backend.')\r\n      this._handleErrorEvent(error)\r\n      return\r\n    }\r\n    // delegate commandComplete to active query\r\n    this.activeQuery.handleCommandComplete(msg, this.connection)\r\n  }\r\n\r\n  _handleParseComplete() {\r\n    if (this.activeQuery == null) {\r\n      const error = new Error('Received unexpected parseComplete message from backend.')\r\n      this._handleErrorEvent(error)\r\n      return\r\n    }\r\n    // if a prepared statement has a name and properly parses\r\n    // we track that its already been executed so we don't parse\r\n    // it again on the same client\r\n    if (this.activeQuery.name) {\r\n      this.connection.parsedStatements[this.activeQuery.name] = this.activeQuery.text\r\n    }\r\n  }\r\n\r\n  _handleCopyInResponse(msg) {\r\n    this.activeQuery.handleCopyInResponse(this.connection)\r\n  }\r\n\r\n  _handleCopyData(msg) {\r\n    this.activeQuery.handleCopyData(msg, this.connection)\r\n  }\r\n\r\n  _handleNotification(msg) {\r\n    this.emit('notification', msg)\r\n  }\r\n\r\n  _handleNotice(msg) {\r\n    this.emit('notice', msg)\r\n  }\r\n\r\n  getStartupConf() {\r\n    const params = this.connectionParameters\r\n\r\n    const data = {\r\n      user: params.user,\r\n      database: params.database,\r\n    }\r\n\r\n    const appName = params.application_name || params.fallback_application_name\r\n    if (appName) {\r\n      data.application_name = appName\r\n    }\r\n    if (params.replication) {\r\n      data.replication = '' + params.replication\r\n    }\r\n    if (params.statement_timeout) {\r\n      data.statement_timeout = String(parseInt(params.statement_timeout, 10))\r\n    }\r\n    if (params.lock_timeout) {\r\n      data.lock_timeout = String(parseInt(params.lock_timeout, 10))\r\n    }\r\n    if (params.idle_in_transaction_session_timeout) {\r\n      data.idle_in_transaction_session_timeout = String(parseInt(params.idle_in_transaction_session_timeout, 10))\r\n    }\r\n    if (params.options) {\r\n      data.options = params.options\r\n    }\r\n\r\n    return data\r\n  }\r\n\r\n  cancel(client, query) {\r\n    if (client.activeQuery === query) {\r\n      const con = this.connection\r\n\r\n      if (this.host && this.host.indexOf('/') === 0) {\r\n        con.connect(this.host + '/.s.PGSQL.' + this.port)\r\n      } else {\r\n        con.connect(this.port, this.host)\r\n      }\r\n\r\n      // once connection is established send cancel message\r\n      con.on('connect', function () {\r\n        con.cancel(client.processID, client.secretKey)\r\n      })\r\n    } else if (client.queryQueue.indexOf(query) !== -1) {\r\n      client.queryQueue.splice(client.queryQueue.indexOf(query), 1)\r\n    }\r\n  }\r\n\r\n  setTypeParser(oid, format, parseFn) {\r\n    return this._types.setTypeParser(oid, format, parseFn)\r\n  }\r\n\r\n  getTypeParser(oid, format) {\r\n    return this._types.getTypeParser(oid, format)\r\n  }\r\n\r\n  // escapeIdentifier and escapeLiteral moved to utility functions & exported\r\n  // on PG\r\n  // re-exported here for backwards compatibility\r\n  escapeIdentifier(str) {\r\n    return utils.escapeIdentifier(str)\r\n  }\r\n\r\n  escapeLiteral(str) {\r\n    return utils.escapeLiteral(str)\r\n  }\r\n\r\n  _pulseQueryQueue() {\r\n    if (this.readyForQuery === true) {\r\n      this.activeQuery = this.queryQueue.shift()\r\n      if (this.activeQuery) {\r\n        this.readyForQuery = false\r\n        this.hasExecuted = true\r\n\r\n        const queryError = this.activeQuery.submit(this.connection)\r\n        if (queryError) {\r\n          process.nextTick(() => {\r\n            this.activeQuery.handleError(queryError, this.connection)\r\n            this.readyForQuery = true\r\n            this._pulseQueryQueue()\r\n          })\r\n        }\r\n      } else if (this.hasExecuted) {\r\n        this.activeQuery = null\r\n        this.emit('drain')\r\n      }\r\n    }\r\n  }\r\n\r\n  query(config, values, callback) {\r\n    // can take in strings, config object or query object\r\n    let query\r\n    let result\r\n    let readTimeout\r\n    let readTimeoutTimer\r\n    let queryCallback\r\n\r\n    if (config === null || config === undefined) {\r\n      throw new TypeError('Client was passed a null or undefined query')\r\n    } else if (typeof config.submit === 'function') {\r\n      readTimeout = config.query_timeout || this.connectionParameters.query_timeout\r\n      result = query = config\r\n      if (typeof values === 'function') {\r\n        query.callback = query.callback || values\r\n      }\r\n    } else {\r\n      readTimeout = config.query_timeout || this.connectionParameters.query_timeout\r\n      query = new Query(config, values, callback)\r\n      if (!query.callback) {\r\n        result = new this._Promise((resolve, reject) => {\r\n          query.callback = (err, res) => (err ? reject(err) : resolve(res))\r\n        }).catch((err) => {\r\n          // replace the stack trace that leads to `TCP.onStreamRead` with one that leads back to the\r\n          // application that created the query\r\n          Error.captureStackTrace(err)\r\n          throw err\r\n        })\r\n      }\r\n    }\r\n\r\n    if (readTimeout) {\r\n      queryCallback = query.callback\r\n\r\n      readTimeoutTimer = setTimeout(() => {\r\n        const error = new Error('Query read timeout')\r\n\r\n        process.nextTick(() => {\r\n          query.handleError(error, this.connection)\r\n        })\r\n\r\n        queryCallback(error)\r\n\r\n        // we already returned an error,\r\n        // just do nothing if query completes\r\n        query.callback = () => {}\r\n\r\n        // Remove from queue\r\n        const index = this.queryQueue.indexOf(query)\r\n        if (index > -1) {\r\n          this.queryQueue.splice(index, 1)\r\n        }\r\n\r\n        this._pulseQueryQueue()\r\n      }, readTimeout)\r\n\r\n      query.callback = (err, res) => {\r\n        clearTimeout(readTimeoutTimer)\r\n        queryCallback(err, res)\r\n      }\r\n    }\r\n\r\n    if (this.binary && !query.binary) {\r\n      query.binary = true\r\n    }\r\n\r\n    if (query._result && !query._result._types) {\r\n      query._result._types = this._types\r\n    }\r\n\r\n    if (!this._queryable) {\r\n      process.nextTick(() => {\r\n        query.handleError(new Error('Client has encountered a connection error and is not queryable'), this.connection)\r\n      })\r\n      return result\r\n    }\r\n\r\n    if (this._ending) {\r\n      process.nextTick(() => {\r\n        query.handleError(new Error('Client was closed and is not queryable'), this.connection)\r\n      })\r\n      return result\r\n    }\r\n\r\n    this.queryQueue.push(query)\r\n    this._pulseQueryQueue()\r\n    return result\r\n  }\r\n\r\n  ref() {\r\n    this.connection.ref()\r\n  }\r\n\r\n  unref() {\r\n    this.connection.unref()\r\n  }\r\n\r\n  end(cb) {\r\n    this._ending = true\r\n\r\n    // if we have never connected, then end is a noop, callback immediately\r\n    if (!this.connection._connecting || this._ended) {\r\n      if (cb) {\r\n        cb()\r\n      } else {\r\n        return this._Promise.resolve()\r\n      }\r\n    }\r\n\r\n    if (this.activeQuery || !this._queryable) {\r\n      // if we have an active query we need to force a disconnect\r\n      // on the socket - otherwise a hung query could block end forever\r\n      this.connection.stream.destroy()\r\n    } else {\r\n      this.connection.end()\r\n    }\r\n\r\n    if (cb) {\r\n      this.connection.once('end', cb)\r\n    } else {\r\n      return new this._Promise((resolve) => {\r\n        this.connection.once('end', resolve)\r\n      })\r\n    }\r\n  }\r\n}\r\n\r\n// expose a Query constructor\r\nClient.Query = Query\r\n\r\nmodule.exports = Client\r\n", "'use strict'\r\nconst EventEmitter = require('events').EventEmitter\r\n\r\nconst NOOP = function () {}\r\n\r\nconst removeWhere = (list, predicate) => {\r\n  const i = list.findIndex(predicate)\r\n\r\n  return i === -1 ? undefined : list.splice(i, 1)[0]\r\n}\r\n\r\nclass IdleItem {\r\n  constructor(client, idleListener, timeoutId) {\r\n    this.client = client\r\n    this.idleListener = idleListener\r\n    this.timeoutId = timeoutId\r\n  }\r\n}\r\n\r\nclass PendingItem {\r\n  constructor(callback) {\r\n    this.callback = callback\r\n  }\r\n}\r\n\r\nfunction throwOnDoubleRelease() {\r\n  throw new Error('Release called on client which has already been released to the pool.')\r\n}\r\n\r\nfunction promisify(Promise, callback) {\r\n  if (callback) {\r\n    return { callback: callback, result: undefined }\r\n  }\r\n  let rej\r\n  let res\r\n  const cb = function (err, client) {\r\n    err ? rej(err) : res(client)\r\n  }\r\n  const result = new Promise(function (resolve, reject) {\r\n    res = resolve\r\n    rej = reject\r\n  }).catch((err) => {\r\n    // replace the stack trace that leads to `TCP.onStreamRead` with one that leads back to the\r\n    // application that created the query\r\n    Error.captureStackTrace(err)\r\n    throw err\r\n  })\r\n  return { callback: cb, result: result }\r\n}\r\n\r\nfunction makeIdleListener(pool, client) {\r\n  return function idleListener(err) {\r\n    err.client = client\r\n\r\n    client.removeListener('error', idleListener)\r\n    client.on('error', () => {\r\n      pool.log('additional client error after disconnection due to error', err)\r\n    })\r\n    pool._remove(client)\r\n    // TODO - document that once the pool emits an error\r\n    // the client has already been closed & purged and is unusable\r\n    pool.emit('error', err, client)\r\n  }\r\n}\r\n\r\nclass Pool extends EventEmitter {\r\n  constructor(options, Client) {\r\n    super()\r\n    this.options = Object.assign({}, options)\r\n\r\n    if (options != null && 'password' in options) {\r\n      // \"hiding\" the password so it doesn't show up in stack traces\r\n      // or if the client is console.logged\r\n      Object.defineProperty(this.options, 'password', {\r\n        configurable: true,\r\n        enumerable: false,\r\n        writable: true,\r\n        value: options.password,\r\n      })\r\n    }\r\n    if (options != null && options.ssl && options.ssl.key) {\r\n      // \"hiding\" the ssl->key so it doesn't show up in stack traces\r\n      // or if the client is console.logged\r\n      Object.defineProperty(this.options.ssl, 'key', {\r\n        enumerable: false,\r\n      })\r\n    }\r\n\r\n    this.options.max = this.options.max || this.options.poolSize || 10\r\n    this.options.min = this.options.min || 0\r\n    this.options.maxUses = this.options.maxUses || Infinity\r\n    this.options.allowExitOnIdle = this.options.allowExitOnIdle || false\r\n    this.options.maxLifetimeSeconds = this.options.maxLifetimeSeconds || 0\r\n    this.log = this.options.log || function () {}\r\n    this.Client = this.options.Client || Client || require('pg').Client\r\n    this.Promise = this.options.Promise || global.Promise\r\n\r\n    if (typeof this.options.idleTimeoutMillis === 'undefined') {\r\n      this.options.idleTimeoutMillis = 10000\r\n    }\r\n\r\n    this._clients = []\r\n    this._idle = []\r\n    this._expired = new WeakSet()\r\n    this._pendingQueue = []\r\n    this._endCallback = undefined\r\n    this.ending = false\r\n    this.ended = false\r\n  }\r\n\r\n  _isFull() {\r\n    return this._clients.length >= this.options.max\r\n  }\r\n\r\n  _isAboveMin() {\r\n    return this._clients.length > this.options.min\r\n  }\r\n\r\n  _pulseQueue() {\r\n    this.log('pulse queue')\r\n    if (this.ended) {\r\n      this.log('pulse queue ended')\r\n      return\r\n    }\r\n    if (this.ending) {\r\n      this.log('pulse queue on ending')\r\n      if (this._idle.length) {\r\n        this._idle.slice().map((item) => {\r\n          this._remove(item.client)\r\n        })\r\n      }\r\n      if (!this._clients.length) {\r\n        this.ended = true\r\n        this._endCallback()\r\n      }\r\n      return\r\n    }\r\n\r\n    // if we don't have any waiting, do nothing\r\n    if (!this._pendingQueue.length) {\r\n      this.log('no queued requests')\r\n      return\r\n    }\r\n    // if we don't have any idle clients and we have no more room do nothing\r\n    if (!this._idle.length && this._isFull()) {\r\n      return\r\n    }\r\n    const pendingItem = this._pendingQueue.shift()\r\n    if (this._idle.length) {\r\n      const idleItem = this._idle.pop()\r\n      clearTimeout(idleItem.timeoutId)\r\n      const client = idleItem.client\r\n      client.ref && client.ref()\r\n      const idleListener = idleItem.idleListener\r\n\r\n      return this._acquireClient(client, pendingItem, idleListener, false)\r\n    }\r\n    if (!this._isFull()) {\r\n      return this.newClient(pendingItem)\r\n    }\r\n    throw new Error('unexpected condition')\r\n  }\r\n\r\n  _remove(client, callback) {\r\n    const removed = removeWhere(this._idle, (item) => item.client === client)\r\n\r\n    if (removed !== undefined) {\r\n      clearTimeout(removed.timeoutId)\r\n    }\r\n\r\n    this._clients = this._clients.filter((c) => c !== client)\r\n    const context = this\r\n    client.end(() => {\r\n      context.emit('remove', client)\r\n\r\n      if (typeof callback === 'function') {\r\n        callback()\r\n      }\r\n    })\r\n  }\r\n\r\n  connect(cb) {\r\n    if (this.ending) {\r\n      const err = new Error('Cannot use a pool after calling end on the pool')\r\n      return cb ? cb(err) : this.Promise.reject(err)\r\n    }\r\n\r\n    const response = promisify(this.Promise, cb)\r\n    const result = response.result\r\n\r\n    // if we don't have to connect a new client, don't do so\r\n    if (this._isFull() || this._idle.length) {\r\n      // if we have idle clients schedule a pulse immediately\r\n      if (this._idle.length) {\r\n        process.nextTick(() => this._pulseQueue())\r\n      }\r\n\r\n      if (!this.options.connectionTimeoutMillis) {\r\n        this._pendingQueue.push(new PendingItem(response.callback))\r\n        return result\r\n      }\r\n\r\n      const queueCallback = (err, res, done) => {\r\n        clearTimeout(tid)\r\n        response.callback(err, res, done)\r\n      }\r\n\r\n      const pendingItem = new PendingItem(queueCallback)\r\n\r\n      // set connection timeout on checking out an existing client\r\n      const tid = setTimeout(() => {\r\n        // remove the callback from pending waiters because\r\n        // we're going to call it with a timeout error\r\n        removeWhere(this._pendingQueue, (i) => i.callback === queueCallback)\r\n        pendingItem.timedOut = true\r\n        response.callback(new Error('timeout exceeded when trying to connect'))\r\n      }, this.options.connectionTimeoutMillis)\r\n\r\n      if (tid.unref) {\r\n        tid.unref()\r\n      }\r\n\r\n      this._pendingQueue.push(pendingItem)\r\n      return result\r\n    }\r\n\r\n    this.newClient(new PendingItem(response.callback))\r\n\r\n    return result\r\n  }\r\n\r\n  newClient(pendingItem) {\r\n    const client = new this.Client(this.options)\r\n    this._clients.push(client)\r\n    const idleListener = makeIdleListener(this, client)\r\n\r\n    this.log('checking client timeout')\r\n\r\n    // connection timeout logic\r\n    let tid\r\n    let timeoutHit = false\r\n    if (this.options.connectionTimeoutMillis) {\r\n      tid = setTimeout(() => {\r\n        this.log('ending client due to timeout')\r\n        timeoutHit = true\r\n        // force kill the node driver, and let libpq do its teardown\r\n        client.connection ? client.connection.stream.destroy() : client.end()\r\n      }, this.options.connectionTimeoutMillis)\r\n    }\r\n\r\n    this.log('connecting new client')\r\n    client.connect((err) => {\r\n      if (tid) {\r\n        clearTimeout(tid)\r\n      }\r\n      client.on('error', idleListener)\r\n      if (err) {\r\n        this.log('client failed to connect', err)\r\n        // remove the dead client from our list of clients\r\n        this._clients = this._clients.filter((c) => c !== client)\r\n        if (timeoutHit) {\r\n          err = new Error('Connection terminated due to connection timeout', { cause: err })\r\n        }\r\n\r\n        // this client won’t be released, so move on immediately\r\n        this._pulseQueue()\r\n\r\n        if (!pendingItem.timedOut) {\r\n          pendingItem.callback(err, undefined, NOOP)\r\n        }\r\n      } else {\r\n        this.log('new client connected')\r\n\r\n        if (this.options.maxLifetimeSeconds !== 0) {\r\n          const maxLifetimeTimeout = setTimeout(() => {\r\n            this.log('ending client due to expired lifetime')\r\n            this._expired.add(client)\r\n            const idleIndex = this._idle.findIndex((idleItem) => idleItem.client === client)\r\n            if (idleIndex !== -1) {\r\n              this._acquireClient(\r\n                client,\r\n                new PendingItem((err, client, clientRelease) => clientRelease()),\r\n                idleListener,\r\n                false\r\n              )\r\n            }\r\n          }, this.options.maxLifetimeSeconds * 1000)\r\n\r\n          maxLifetimeTimeout.unref()\r\n          client.once('end', () => clearTimeout(maxLifetimeTimeout))\r\n        }\r\n\r\n        return this._acquireClient(client, pendingItem, idleListener, true)\r\n      }\r\n    })\r\n  }\r\n\r\n  // acquire a client for a pending work item\r\n  _acquireClient(client, pendingItem, idleListener, isNew) {\r\n    if (isNew) {\r\n      this.emit('connect', client)\r\n    }\r\n\r\n    this.emit('acquire', client)\r\n\r\n    client.release = this._releaseOnce(client, idleListener)\r\n\r\n    client.removeListener('error', idleListener)\r\n\r\n    if (!pendingItem.timedOut) {\r\n      if (isNew && this.options.verify) {\r\n        this.options.verify(client, (err) => {\r\n          if (err) {\r\n            client.release(err)\r\n            return pendingItem.callback(err, undefined, NOOP)\r\n          }\r\n\r\n          pendingItem.callback(undefined, client, client.release)\r\n        })\r\n      } else {\r\n        pendingItem.callback(undefined, client, client.release)\r\n      }\r\n    } else {\r\n      if (isNew && this.options.verify) {\r\n        this.options.verify(client, client.release)\r\n      } else {\r\n        client.release()\r\n      }\r\n    }\r\n  }\r\n\r\n  // returns a function that wraps _release and throws if called more than once\r\n  _releaseOnce(client, idleListener) {\r\n    let released = false\r\n\r\n    return (err) => {\r\n      if (released) {\r\n        throwOnDoubleRelease()\r\n      }\r\n\r\n      released = true\r\n      this._release(client, idleListener, err)\r\n    }\r\n  }\r\n\r\n  // release a client back to the poll, include an error\r\n  // to remove it from the pool\r\n  _release(client, idleListener, err) {\r\n    client.on('error', idleListener)\r\n\r\n    client._poolUseCount = (client._poolUseCount || 0) + 1\r\n\r\n    this.emit('release', err, client)\r\n\r\n    // TODO(bmc): expose a proper, public interface _queryable and _ending\r\n    if (err || this.ending || !client._queryable || client._ending || client._poolUseCount >= this.options.maxUses) {\r\n      if (client._poolUseCount >= this.options.maxUses) {\r\n        this.log('remove expended client')\r\n      }\r\n\r\n      return this._remove(client, this._pulseQueue.bind(this))\r\n    }\r\n\r\n    const isExpired = this._expired.has(client)\r\n    if (isExpired) {\r\n      this.log('remove expired client')\r\n      this._expired.delete(client)\r\n      return this._remove(client, this._pulseQueue.bind(this))\r\n    }\r\n\r\n    // idle timeout\r\n    let tid\r\n    if (this.options.idleTimeoutMillis && this._isAboveMin()) {\r\n      tid = setTimeout(() => {\r\n        this.log('remove idle client')\r\n        this._remove(client, this._pulseQueue.bind(this))\r\n      }, this.options.idleTimeoutMillis)\r\n\r\n      if (this.options.allowExitOnIdle) {\r\n        // allow Node to exit if this is all that's left\r\n        tid.unref()\r\n      }\r\n    }\r\n\r\n    if (this.options.allowExitOnIdle) {\r\n      client.unref()\r\n    }\r\n\r\n    this._idle.push(new IdleItem(client, idleListener, tid))\r\n    this._pulseQueue()\r\n  }\r\n\r\n  query(text, values, cb) {\r\n    // guard clause against passing a function as the first parameter\r\n    if (typeof text === 'function') {\r\n      const response = promisify(this.Promise, text)\r\n      setImmediate(function () {\r\n        return response.callback(new Error('Passing a function as the first parameter to pool.query is not supported'))\r\n      })\r\n      return response.result\r\n    }\r\n\r\n    // allow plain text query without values\r\n    if (typeof values === 'function') {\r\n      cb = values\r\n      values = undefined\r\n    }\r\n    const response = promisify(this.Promise, cb)\r\n    cb = response.callback\r\n\r\n    this.connect((err, client) => {\r\n      if (err) {\r\n        return cb(err)\r\n      }\r\n\r\n      let clientReleased = false\r\n      const onError = (err) => {\r\n        if (clientReleased) {\r\n          return\r\n        }\r\n        clientReleased = true\r\n        client.release(err)\r\n        cb(err)\r\n      }\r\n\r\n      client.once('error', onError)\r\n      this.log('dispatching query')\r\n      try {\r\n        client.query(text, values, (err, res) => {\r\n          this.log('query dispatched')\r\n          client.removeListener('error', onError)\r\n          if (clientReleased) {\r\n            return\r\n          }\r\n          clientReleased = true\r\n          client.release(err)\r\n          if (err) {\r\n            return cb(err)\r\n          }\r\n          return cb(undefined, res)\r\n        })\r\n      } catch (err) {\r\n        client.release(err)\r\n        return cb(err)\r\n      }\r\n    })\r\n    return response.result\r\n  }\r\n\r\n  end(cb) {\r\n    this.log('ending')\r\n    if (this.ending) {\r\n      const err = new Error('Called end on pool more than once')\r\n      return cb ? cb(err) : this.Promise.reject(err)\r\n    }\r\n    this.ending = true\r\n    const promised = promisify(this.Promise, cb)\r\n    this._endCallback = promised.callback\r\n    this._pulseQueue()\r\n    return promised.result\r\n  }\r\n\r\n  get waitingCount() {\r\n    return this._pendingQueue.length\r\n  }\r\n\r\n  get idleCount() {\r\n    return this._idle.length\r\n  }\r\n\r\n  get expiredCount() {\r\n    return this._clients.reduce((acc, client) => acc + (this._expired.has(client) ? 1 : 0), 0)\r\n  }\r\n\r\n  get totalCount() {\r\n    return this._clients.length\r\n  }\r\n}\r\nmodule.exports = Pool\r\n", "throw new Error(`Could not resolve \"pg-native\" imported by \"pg\". Is it installed?`)", "'use strict'\r\n\r\nconst EventEmitter = require('events').EventEmitter\r\nconst util = require('util')\r\nconst utils = require('../utils')\r\n\r\nconst NativeQuery = (module.exports = function (config, values, callback) {\r\n  EventEmitter.call(this)\r\n  config = utils.normalizeQueryConfig(config, values, callback)\r\n  this.text = config.text\r\n  this.values = config.values\r\n  this.name = config.name\r\n  this.queryMode = config.queryMode\r\n  this.callback = config.callback\r\n  this.state = 'new'\r\n  this._arrayMode = config.rowMode === 'array'\r\n\r\n  // if the 'row' event is listened for\r\n  // then emit them as they come in\r\n  // without setting singleRowMode to true\r\n  // this has almost no meaning because libpq\r\n  // reads all rows into memory befor returning any\r\n  this._emitRowEvents = false\r\n  this.on(\r\n    'newListener',\r\n    function (event) {\r\n      if (event === 'row') this._emitRowEvents = true\r\n    }.bind(this)\r\n  )\r\n})\r\n\r\nutil.inherits(NativeQuery, EventEmitter)\r\n\r\nconst errorFieldMap = {\r\n  sqlState: 'code',\r\n  statementPosition: 'position',\r\n  messagePrimary: 'message',\r\n  context: 'where',\r\n  schemaName: 'schema',\r\n  tableName: 'table',\r\n  columnName: 'column',\r\n  dataTypeName: 'dataType',\r\n  constraintName: 'constraint',\r\n  sourceFile: 'file',\r\n  sourceLine: 'line',\r\n  sourceFunction: 'routine',\r\n}\r\n\r\nNativeQuery.prototype.handleError = function (err) {\r\n  // copy pq error fields into the error object\r\n  const fields = this.native.pq.resultErrorFields()\r\n  if (fields) {\r\n    for (const key in fields) {\r\n      const normalizedFieldName = errorFieldMap[key] || key\r\n      err[normalizedFieldName] = fields[key]\r\n    }\r\n  }\r\n  if (this.callback) {\r\n    this.callback(err)\r\n  } else {\r\n    this.emit('error', err)\r\n  }\r\n  this.state = 'error'\r\n}\r\n\r\nNativeQuery.prototype.then = function (onSuccess, onFailure) {\r\n  return this._getPromise().then(onSuccess, onFailure)\r\n}\r\n\r\nNativeQuery.prototype.catch = function (callback) {\r\n  return this._getPromise().catch(callback)\r\n}\r\n\r\nNativeQuery.prototype._getPromise = function () {\r\n  if (this._promise) return this._promise\r\n  this._promise = new Promise(\r\n    function (resolve, reject) {\r\n      this._once('end', resolve)\r\n      this._once('error', reject)\r\n    }.bind(this)\r\n  )\r\n  return this._promise\r\n}\r\n\r\nNativeQuery.prototype.submit = function (client) {\r\n  this.state = 'running'\r\n  const self = this\r\n  this.native = client.native\r\n  client.native.arrayMode = this._arrayMode\r\n\r\n  let after = function (err, rows, results) {\r\n    client.native.arrayMode = false\r\n    setImmediate(function () {\r\n      self.emit('_done')\r\n    })\r\n\r\n    // handle possible query error\r\n    if (err) {\r\n      return self.handleError(err)\r\n    }\r\n\r\n    // emit row events for each row in the result\r\n    if (self._emitRowEvents) {\r\n      if (results.length > 1) {\r\n        rows.forEach((rowOfRows, i) => {\r\n          rowOfRows.forEach((row) => {\r\n            self.emit('row', row, results[i])\r\n          })\r\n        })\r\n      } else {\r\n        rows.forEach(function (row) {\r\n          self.emit('row', row, results)\r\n        })\r\n      }\r\n    }\r\n\r\n    // handle successful result\r\n    self.state = 'end'\r\n    self.emit('end', results)\r\n    if (self.callback) {\r\n      self.callback(null, results)\r\n    }\r\n  }\r\n\r\n  if (process.domain) {\r\n    after = process.domain.bind(after)\r\n  }\r\n\r\n  // named query\r\n  if (this.name) {\r\n    if (this.name.length > 63) {\r\n      console.error('Warning! Postgres only supports 63 characters for query names.')\r\n      console.error('You supplied %s (%s)', this.name, this.name.length)\r\n      console.error('This can cause conflicts and silent errors executing queries')\r\n    }\r\n    const values = (this.values || []).map(utils.prepareValue)\r\n\r\n    // check if the client has already executed this named query\r\n    // if so...just execute it again - skip the planning phase\r\n    if (client.namedQueries[this.name]) {\r\n      if (this.text && client.namedQueries[this.name] !== this.text) {\r\n        const err = new Error(`Prepared statements must be unique - '${this.name}' was used for a different statement`)\r\n        return after(err)\r\n      }\r\n      return client.native.execute(this.name, values, after)\r\n    }\r\n    // plan the named query the first time, then execute it\r\n    return client.native.prepare(this.name, this.text, values.length, function (err) {\r\n      if (err) return after(err)\r\n      client.namedQueries[self.name] = self.text\r\n      return self.native.execute(self.name, values, after)\r\n    })\r\n  } else if (this.values) {\r\n    if (!Array.isArray(this.values)) {\r\n      const err = new Error('Query values must be an array')\r\n      return after(err)\r\n    }\r\n    const vals = this.values.map(utils.prepareValue)\r\n    client.native.query(this.text, vals, after)\r\n  } else if (this.queryMode === 'extended') {\r\n    client.native.query(this.text, [], after)\r\n  } else {\r\n    client.native.query(this.text, after)\r\n  }\r\n}\r\n", "'use strict'\r\n\r\n// eslint-disable-next-line\r\nvar Native\r\n// eslint-disable-next-line no-useless-catch\r\ntry {\r\n  // Wrap this `require()` in a try-catch to avoid upstream bundlers from complaining that this might not be available since it is an optional import\r\n  Native = require('pg-native')\r\n} catch (e) {\r\n  throw e\r\n}\r\nconst TypeOverrides = require('../type-overrides')\r\nconst EventEmitter = require('events').EventEmitter\r\nconst util = require('util')\r\nconst ConnectionParameters = require('../connection-parameters')\r\n\r\nconst NativeQuery = require('./query')\r\n\r\nconst Client = (module.exports = function (config) {\r\n  EventEmitter.call(this)\r\n  config = config || {}\r\n\r\n  this._Promise = config.Promise || global.Promise\r\n  this._types = new TypeOverrides(config.types)\r\n\r\n  this.native = new Native({\r\n    types: this._types,\r\n  })\r\n\r\n  this._queryQueue = []\r\n  this._ending = false\r\n  this._connecting = false\r\n  this._connected = false\r\n  this._queryable = true\r\n\r\n  // keep these on the object for legacy reasons\r\n  // for the time being. TODO: deprecate all this jazz\r\n  const cp = (this.connectionParameters = new ConnectionParameters(config))\r\n  if (config.nativeConnectionString) cp.nativeConnectionString = config.nativeConnectionString\r\n  this.user = cp.user\r\n\r\n  // \"hiding\" the password so it doesn't show up in stack traces\r\n  // or if the client is console.logged\r\n  Object.defineProperty(this, 'password', {\r\n    configurable: true,\r\n    enumerable: false,\r\n    writable: true,\r\n    value: cp.password,\r\n  })\r\n  this.database = cp.database\r\n  this.host = cp.host\r\n  this.port = cp.port\r\n\r\n  // a hash to hold named queries\r\n  this.namedQueries = {}\r\n})\r\n\r\nClient.Query = NativeQuery\r\n\r\nutil.inherits(Client, EventEmitter)\r\n\r\nClient.prototype._errorAllQueries = function (err) {\r\n  const enqueueError = (query) => {\r\n    process.nextTick(() => {\r\n      query.native = this.native\r\n      query.handleError(err)\r\n    })\r\n  }\r\n\r\n  if (this._hasActiveQuery()) {\r\n    enqueueError(this._activeQuery)\r\n    this._activeQuery = null\r\n  }\r\n\r\n  this._queryQueue.forEach(enqueueError)\r\n  this._queryQueue.length = 0\r\n}\r\n\r\n// connect to the backend\r\n// pass an optional callback to be called once connected\r\n// or with an error if there was a connection error\r\nClient.prototype._connect = function (cb) {\r\n  const self = this\r\n\r\n  if (this._connecting) {\r\n    process.nextTick(() => cb(new Error('Client has already been connected. You cannot reuse a client.')))\r\n    return\r\n  }\r\n\r\n  this._connecting = true\r\n\r\n  this.connectionParameters.getLibpqConnectionString(function (err, conString) {\r\n    if (self.connectionParameters.nativeConnectionString) conString = self.connectionParameters.nativeConnectionString\r\n    if (err) return cb(err)\r\n    self.native.connect(conString, function (err) {\r\n      if (err) {\r\n        self.native.end()\r\n        return cb(err)\r\n      }\r\n\r\n      // set internal states to connected\r\n      self._connected = true\r\n\r\n      // handle connection errors from the native layer\r\n      self.native.on('error', function (err) {\r\n        self._queryable = false\r\n        self._errorAllQueries(err)\r\n        self.emit('error', err)\r\n      })\r\n\r\n      self.native.on('notification', function (msg) {\r\n        self.emit('notification', {\r\n          channel: msg.relname,\r\n          payload: msg.extra,\r\n        })\r\n      })\r\n\r\n      // signal we are connected now\r\n      self.emit('connect')\r\n      self._pulseQueryQueue(true)\r\n\r\n      cb()\r\n    })\r\n  })\r\n}\r\n\r\nClient.prototype.connect = function (callback) {\r\n  if (callback) {\r\n    this._connect(callback)\r\n    return\r\n  }\r\n\r\n  return new this._Promise((resolve, reject) => {\r\n    this._connect((error) => {\r\n      if (error) {\r\n        reject(error)\r\n      } else {\r\n        resolve()\r\n      }\r\n    })\r\n  })\r\n}\r\n\r\n// send a query to the server\r\n// this method is highly overloaded to take\r\n// 1) string query, optional array of parameters, optional function callback\r\n// 2) object query with {\r\n//    string query\r\n//    optional array values,\r\n//    optional function callback instead of as a separate parameter\r\n//    optional string name to name & cache the query plan\r\n//    optional string rowMode = 'array' for an array of results\r\n//  }\r\nClient.prototype.query = function (config, values, callback) {\r\n  let query\r\n  let result\r\n  let readTimeout\r\n  let readTimeoutTimer\r\n  let queryCallback\r\n\r\n  if (config === null || config === undefined) {\r\n    throw new TypeError('Client was passed a null or undefined query')\r\n  } else if (typeof config.submit === 'function') {\r\n    readTimeout = config.query_timeout || this.connectionParameters.query_timeout\r\n    result = query = config\r\n    // accept query(new Query(...), (err, res) => { }) style\r\n    if (typeof values === 'function') {\r\n      config.callback = values\r\n    }\r\n  } else {\r\n    readTimeout = config.query_timeout || this.connectionParameters.query_timeout\r\n    query = new NativeQuery(config, values, callback)\r\n    if (!query.callback) {\r\n      let resolveOut, rejectOut\r\n      result = new this._Promise((resolve, reject) => {\r\n        resolveOut = resolve\r\n        rejectOut = reject\r\n      }).catch((err) => {\r\n        Error.captureStackTrace(err)\r\n        throw err\r\n      })\r\n      query.callback = (err, res) => (err ? rejectOut(err) : resolveOut(res))\r\n    }\r\n  }\r\n\r\n  if (readTimeout) {\r\n    queryCallback = query.callback\r\n\r\n    readTimeoutTimer = setTimeout(() => {\r\n      const error = new Error('Query read timeout')\r\n\r\n      process.nextTick(() => {\r\n        query.handleError(error, this.connection)\r\n      })\r\n\r\n      queryCallback(error)\r\n\r\n      // we already returned an error,\r\n      // just do nothing if query completes\r\n      query.callback = () => {}\r\n\r\n      // Remove from queue\r\n      const index = this._queryQueue.indexOf(query)\r\n      if (index > -1) {\r\n        this._queryQueue.splice(index, 1)\r\n      }\r\n\r\n      this._pulseQueryQueue()\r\n    }, readTimeout)\r\n\r\n    query.callback = (err, res) => {\r\n      clearTimeout(readTimeoutTimer)\r\n      queryCallback(err, res)\r\n    }\r\n  }\r\n\r\n  if (!this._queryable) {\r\n    query.native = this.native\r\n    process.nextTick(() => {\r\n      query.handleError(new Error('Client has encountered a connection error and is not queryable'))\r\n    })\r\n    return result\r\n  }\r\n\r\n  if (this._ending) {\r\n    query.native = this.native\r\n    process.nextTick(() => {\r\n      query.handleError(new Error('Client was closed and is not queryable'))\r\n    })\r\n    return result\r\n  }\r\n\r\n  this._queryQueue.push(query)\r\n  this._pulseQueryQueue()\r\n  return result\r\n}\r\n\r\n// disconnect from the backend server\r\nClient.prototype.end = function (cb) {\r\n  const self = this\r\n\r\n  this._ending = true\r\n\r\n  if (!this._connected) {\r\n    this.once('connect', this.end.bind(this, cb))\r\n  }\r\n  let result\r\n  if (!cb) {\r\n    result = new this._Promise(function (resolve, reject) {\r\n      cb = (err) => (err ? reject(err) : resolve())\r\n    })\r\n  }\r\n  this.native.end(function () {\r\n    self._errorAllQueries(new Error('Connection terminated'))\r\n\r\n    process.nextTick(() => {\r\n      self.emit('end')\r\n      if (cb) cb()\r\n    })\r\n  })\r\n  return result\r\n}\r\n\r\nClient.prototype._hasActiveQuery = function () {\r\n  return this._activeQuery && this._activeQuery.state !== 'error' && this._activeQuery.state !== 'end'\r\n}\r\n\r\nClient.prototype._pulseQueryQueue = function (initialConnection) {\r\n  if (!this._connected) {\r\n    return\r\n  }\r\n  if (this._hasActiveQuery()) {\r\n    return\r\n  }\r\n  const query = this._queryQueue.shift()\r\n  if (!query) {\r\n    if (!initialConnection) {\r\n      this.emit('drain')\r\n    }\r\n    return\r\n  }\r\n  this._activeQuery = query\r\n  query.submit(this)\r\n  const self = this\r\n  query.once('_done', function () {\r\n    self._pulseQueryQueue()\r\n  })\r\n}\r\n\r\n// attempt to cancel an in-progress query\r\nClient.prototype.cancel = function (query) {\r\n  if (this._activeQuery === query) {\r\n    this.native.cancel(function () {})\r\n  } else if (this._queryQueue.indexOf(query) !== -1) {\r\n    this._queryQueue.splice(this._queryQueue.indexOf(query), 1)\r\n  }\r\n}\r\n\r\nClient.prototype.ref = function () {}\r\nClient.prototype.unref = function () {}\r\n\r\nClient.prototype.setTypeParser = function (oid, format, parseFn) {\r\n  return this._types.setTypeParser(oid, format, parseFn)\r\n}\r\n\r\nClient.prototype.getTypeParser = function (oid, format) {\r\n  return this._types.getTypeParser(oid, format)\r\n}\r\n", "'use strict'\r\nmodule.exports = require('./client')\r\n", "'use strict'\r\n\r\nconst Client = require('./client')\r\nconst defaults = require('./defaults')\r\nconst Connection = require('./connection')\r\nconst Result = require('./result')\r\nconst utils = require('./utils')\r\nconst Pool = require('pg-pool')\r\nconst TypeOverrides = require('./type-overrides')\r\nconst { DatabaseError } = require('pg-protocol')\r\nconst { escapeIdentifier, escapeLiteral } = require('./utils')\r\n\r\nconst poolFactory = (Client) => {\r\n  return class BoundPool extends Pool {\r\n    constructor(options) {\r\n      super(options, Client)\r\n    }\r\n  }\r\n}\r\n\r\nconst PG = function (clientConstructor) {\r\n  this.defaults = defaults\r\n  this.Client = clientConstructor\r\n  this.Query = this.Client.Query\r\n  this.Pool = poolFactory(this.Client)\r\n  this._pools = []\r\n  this.Connection = Connection\r\n  this.types = require('pg-types')\r\n  this.DatabaseError = DatabaseError\r\n  this.TypeOverrides = TypeOverrides\r\n  this.escapeIdentifier = escapeIdentifier\r\n  this.escapeLiteral = escapeLiteral\r\n  this.Result = Result\r\n  this.utils = utils\r\n}\r\n\r\nif (typeof process.env.NODE_PG_FORCE_NATIVE !== 'undefined') {\r\n  module.exports = new PG(require('./native'))\r\n} else {\r\n  module.exports = new PG(Client)\r\n\r\n  // lazy require native module...the native module may not have installed\r\n  Object.defineProperty(module.exports, 'native', {\r\n    configurable: true,\r\n    enumerable: false,\r\n    get() {\r\n      let native = null\r\n      try {\r\n        native = new PG(require('./native'))\r\n      } catch (err) {\r\n        if (err.code !== 'MODULE_NOT_FOUND') {\r\n          throw err\r\n        }\r\n      }\r\n\r\n      // overwrite module.exports.native so that getter is never called again\r\n      Object.defineProperty(module.exports, 'native', {\r\n        value: native,\r\n      })\r\n\r\n      return native\r\n    },\r\n  })\r\n}\r\n", "// ESM wrapper for pg\r\nimport pg from '../lib/index.js'\r\n\r\n// Re-export all the properties\r\nexport const Client = pg.Client\r\nexport const Pool = pg.Pool\r\nexport const Connection = pg.Connection\r\nexport const types = pg.types\r\nexport const Query = pg.Query\r\nexport const DatabaseError = pg.DatabaseError\r\nexport const escapeIdentifier = pg.escapeIdentifier\r\nexport const escapeLiteral = pg.escapeLiteral\r\nexport const Result = pg.Result\r\nexport const TypeOverrides = pg.TypeOverrides\r\n\r\n// Also export the defaults\r\nexport const defaults = pg.defaults\r\n\r\n// Re-export the default\r\nexport default pg\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAEA,YAAQ,QAAQ,SAAU,QAAQ,WAAW;AAC3C,aAAO,IAAI,YAAY,QAAQ,SAAS,EAAE,MAAM;AAAA,IAClD;AAEA,QAAM,cAAN,MAAM,aAAY;AAAA,MAChB,YAAa,QAAQ,WAAW;AAC9B,aAAK,SAAS;AACd,aAAK,YAAY,aAAa;AAC9B,aAAK,WAAW;AAChB,aAAK,UAAU,CAAC;AAChB,aAAK,WAAW,CAAC;AACjB,aAAK,YAAY;AAAA,MACnB;AAAA,MAEA,QAAS;AACP,eAAO,KAAK,YAAY,KAAK,OAAO;AAAA,MACtC;AAAA,MAEA,gBAAiB;AACf,YAAI,YAAY,KAAK,OAAO,KAAK,UAAU;AAC3C,YAAI,cAAc,MAAM;AACtB,iBAAO;AAAA,YACL,OAAO,KAAK,OAAO,KAAK,UAAU;AAAA,YAClC,SAAS;AAAA,UACX;AAAA,QACF;AACA,eAAO;AAAA,UACL,OAAO;AAAA,UACP,SAAS;AAAA,QACX;AAAA,MACF;AAAA,MAEA,OAAQ,WAAW;AACjB,aAAK,SAAS,KAAK,SAAS;AAAA,MAC9B;AAAA,MAEA,SAAU,cAAc;AACtB,YAAI;AACJ,YAAI,KAAK,SAAS,SAAS,KAAK,cAAc;AAC5C,kBAAQ,KAAK,SAAS,KAAK,EAAE;AAC7B,cAAI,UAAU,UAAU,CAAC,cAAc;AACrC,oBAAQ;AAAA,UACV;AACA,cAAI,UAAU,KAAM,SAAQ,KAAK,UAAU,KAAK;AAChD,eAAK,QAAQ,KAAK,KAAK;AACvB,eAAK,WAAW,CAAC;AAAA,QACnB;AAAA,MACF;AAAA,MAEA,oBAAqB;AACnB,YAAI,KAAK,OAAO,CAAC,MAAM,KAAK;AAC1B,iBAAO,CAAC,KAAK,MAAM,GAAG;AACpB,gBAAI,OAAO,KAAK,cAAc;AAC9B,gBAAI,KAAK,UAAU,IAAK;AAAA,UAC1B;AAAA,QACF;AAAA,MACF;AAAA,MAEA,MAAO,QAAQ;AACb,YAAI,WAAW,QAAQ;AACvB,aAAK,kBAAkB;AACvB,eAAO,CAAC,KAAK,MAAM,GAAG;AACpB,sBAAY,KAAK,cAAc;AAC/B,cAAI,UAAU,UAAU,OAAO,CAAC,OAAO;AACrC,iBAAK;AACL,gBAAI,KAAK,YAAY,GAAG;AACtB,uBAAS,IAAI,aAAY,KAAK,OAAO,OAAO,KAAK,WAAW,CAAC,GAAG,KAAK,SAAS;AAC9E,mBAAK,QAAQ,KAAK,OAAO,MAAM,IAAI,CAAC;AACpC,mBAAK,YAAY,OAAO,WAAW;AAAA,YACrC;AAAA,UACF,WAAW,UAAU,UAAU,OAAO,CAAC,OAAO;AAC5C,iBAAK;AACL,gBAAI,CAAC,KAAK,WAAW;AACnB,mBAAK,SAAS;AACd,kBAAI,OAAQ,QAAO,KAAK;AAAA,YAC1B;AAAA,UACF,WAAW,UAAU,UAAU,OAAO,CAAC,UAAU,SAAS;AACxD,gBAAI,MAAO,MAAK,SAAS,IAAI;AAC7B,oBAAQ,CAAC;AAAA,UACX,WAAW,UAAU,UAAU,OAAO,CAAC,OAAO;AAC5C,iBAAK,SAAS;AAAA,UAChB,OAAO;AACL,iBAAK,OAAO,UAAU,KAAK;AAAA,UAC7B;AAAA,QACF;AACA,YAAI,KAAK,cAAc,GAAG;AACxB,gBAAM,IAAI,MAAM,8BAA8B;AAAA,QAChD;AACA,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AAEA,aAAS,SAAU,OAAO;AACxB,aAAO;AAAA,IACT;AAAA;AAAA;;;AChGA;AAAA;AAAA,QAAI,QAAQ;AAEZ,WAAO,UAAU;AAAA,MACf,QAAQ,SAAU,QAAQ,WAAW;AACnC,eAAO;AAAA,UACL,OAAO,WAAW;AAChB,mBAAO,MAAM,MAAM,QAAQ,SAAS;AAAA,UACtC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;ACVA;AAAA;AAAA;AAEA,QAAI,YAAY;AAChB,QAAI,OAAO;AACX,QAAI,YAAY;AAChB,QAAI,WAAW;AAEf,WAAO,UAAU,SAAS,UAAW,SAAS;AAC5C,UAAI,SAAS,KAAK,OAAO,GAAG;AAE1B,eAAO,OAAO,QAAQ,QAAQ,KAAK,GAAG,CAAC;AAAA,MACzC;AACA,UAAI,UAAU,UAAU,KAAK,OAAO;AAEpC,UAAI,CAAC,SAAS;AAEZ,eAAO,QAAQ,OAAO,KAAK;AAAA,MAC7B;AAEA,UAAI,OAAO,CAAC,CAAC,QAAQ,CAAC;AACtB,UAAI,OAAO,SAAS,QAAQ,CAAC,GAAG,EAAE;AAClC,UAAI,MAAM;AACR,eAAO,qBAAqB,IAAI;AAAA,MAClC;AAEA,UAAI,QAAQ,SAAS,QAAQ,CAAC,GAAG,EAAE,IAAI;AACvC,UAAI,MAAM,QAAQ,CAAC;AACnB,UAAI,OAAO,SAAS,QAAQ,CAAC,GAAG,EAAE;AAClC,UAAI,SAAS,SAAS,QAAQ,CAAC,GAAG,EAAE;AACpC,UAAI,SAAS,SAAS,QAAQ,CAAC,GAAG,EAAE;AAEpC,UAAI,KAAK,QAAQ,CAAC;AAClB,WAAK,KAAK,MAAO,WAAW,EAAE,IAAI;AAElC,UAAI;AACJ,UAAI,SAAS,eAAe,OAAO;AACnC,UAAI,UAAU,MAAM;AAClB,eAAO,IAAI,KAAK,KAAK,IAAI,MAAM,OAAO,KAAK,MAAM,QAAQ,QAAQ,EAAE,CAAC;AAIpE,YAAI,QAAQ,IAAI,GAAG;AACjB,eAAK,eAAe,IAAI;AAAA,QAC1B;AAEA,YAAI,WAAW,GAAG;AAChB,eAAK,QAAQ,KAAK,QAAQ,IAAI,MAAM;AAAA,QACtC;AAAA,MACF,OAAO;AACL,eAAO,IAAI,KAAK,MAAM,OAAO,KAAK,MAAM,QAAQ,QAAQ,EAAE;AAE1D,YAAI,QAAQ,IAAI,GAAG;AACjB,eAAK,YAAY,IAAI;AAAA,QACvB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,QAAS,SAAS;AACzB,UAAI,UAAU,KAAK,KAAK,OAAO;AAC/B,UAAI,CAAC,SAAS;AACZ;AAAA,MACF;AAEA,UAAI,OAAO,SAAS,QAAQ,CAAC,GAAG,EAAE;AAClC,UAAI,OAAO,CAAC,CAAC,QAAQ,CAAC;AACtB,UAAI,MAAM;AACR,eAAO,qBAAqB,IAAI;AAAA,MAClC;AAEA,UAAI,QAAQ,SAAS,QAAQ,CAAC,GAAG,EAAE,IAAI;AACvC,UAAI,MAAM,QAAQ,CAAC;AAEnB,UAAI,OAAO,IAAI,KAAK,MAAM,OAAO,GAAG;AAEpC,UAAI,QAAQ,IAAI,GAAG;AACjB,aAAK,YAAY,IAAI;AAAA,MACvB;AAEA,aAAO;AAAA,IACT;AAMA,aAAS,eAAgB,SAAS;AAChC,UAAI,QAAQ,SAAS,KAAK,GAAG;AAC3B,eAAO;AAAA,MACT;AAEA,UAAI,OAAO,UAAU,KAAK,QAAQ,MAAM,GAAG,EAAE,CAAC,CAAC;AAC/C,UAAI,CAAC,KAAM;AACX,UAAI,OAAO,KAAK,CAAC;AAEjB,UAAI,SAAS,KAAK;AAChB,eAAO;AAAA,MACT;AACA,UAAI,OAAO,SAAS,MAAM,KAAK;AAC/B,UAAI,SAAS,SAAS,KAAK,CAAC,GAAG,EAAE,IAAI,OACnC,SAAS,KAAK,CAAC,KAAK,GAAG,EAAE,IAAI,KAC7B,SAAS,KAAK,CAAC,KAAK,GAAG,EAAE;AAE3B,aAAO,SAAS,OAAO;AAAA,IACzB;AAEA,aAAS,qBAAsB,MAAM;AAGnC,aAAO,EAAE,OAAO;AAAA,IAClB;AAEA,aAAS,QAAS,KAAK;AACrB,aAAO,OAAO,KAAK,MAAM;AAAA,IAC3B;AAAA;AAAA;;;ACnHA;AAAA;AAAA,WAAO,UAAU;AAEjB,QAAI,iBAAiB,OAAO,UAAU;AAEtC,aAAS,OAAO,QAAQ;AACpB,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,YAAI,SAAS,UAAU,CAAC;AAExB,iBAAS,OAAO,QAAQ;AACpB,cAAI,eAAe,KAAK,QAAQ,GAAG,GAAG;AAClC,mBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,UAC5B;AAAA,QACJ;AAAA,MACJ;AAEA,aAAO;AAAA,IACX;AAAA;AAAA;;;AChBA;AAAA;AAAA;AAEA,QAAI,SAAS;AAEb,WAAO,UAAU;AAEjB,aAAS,iBAAkB,KAAK;AAC9B,UAAI,EAAE,gBAAgB,mBAAmB;AACvC,eAAO,IAAI,iBAAiB,GAAG;AAAA,MACjC;AACA,aAAO,MAAM,MAAM,GAAG,CAAC;AAAA,IACzB;AACA,QAAI,aAAa,CAAC,WAAW,WAAW,SAAS,QAAQ,UAAU,OAAO;AAC1E,qBAAiB,UAAU,aAAa,WAAY;AAClD,UAAI,WAAW,WAAW,OAAO,KAAK,gBAAgB,IAAI;AAG1D,UAAI,KAAK,gBAAgB,SAAS,QAAQ,SAAS,IAAI,GAAG;AACxD,iBAAS,KAAK,SAAS;AAAA,MACzB;AAEA,UAAI,SAAS,WAAW,EAAG,QAAO;AAClC,aAAO,SACJ,IAAI,SAAU,UAAU;AACvB,YAAI,QAAQ,KAAK,QAAQ,KAAK;AAI9B,YAAI,aAAa,aAAa,KAAK,cAAc;AAC/C,mBAAS,QAAQ,KAAK,eAAe,KAAM,QAAQ,CAAC,EAAE,QAAQ,UAAU,EAAE;AAAA,QAC5E;AAEA,eAAO,QAAQ,MAAM;AAAA,MACvB,GAAG,IAAI,EACN,KAAK,GAAG;AAAA,IACb;AAEA,QAAI,0BAA0B;AAAA,MAC5B,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AACA,QAAI,iBAAiB,CAAC,SAAS,UAAU,MAAM;AAC/C,QAAI,iBAAiB,CAAC,SAAS,WAAW,SAAS;AAEnD,qBAAiB,UAAU,cAAc,iBAAiB,UAAU,QAAQ,WAAY;AACtF,UAAI,WAAW,eACZ,IAAI,eAAe,IAAI,EACvB,KAAK,EAAE;AAEV,UAAI,WAAW,eACZ,IAAI,eAAe,IAAI,EACvB,KAAK,EAAE;AAEV,aAAO,MAAM,WAAW,MAAM;AAE9B,eAAS,cAAe,UAAU;AAChC,YAAI,QAAQ,KAAK,QAAQ,KAAK;AAI9B,YAAI,aAAa,aAAa,KAAK,cAAc;AAC/C,mBAAS,QAAQ,KAAK,eAAe,KAAM,QAAQ,CAAC,EAAE,QAAQ,OAAO,EAAE;AAAA,QACzE;AAEA,eAAO,QAAQ,wBAAwB,QAAQ;AAAA,MACjD;AAAA,IACF;AAEA,QAAI,SAAS;AACb,QAAI,OAAO,SAAS;AACpB,QAAI,QAAQ,SAAS;AACrB,QAAI,MAAM,SAAS;AACnB,QAAI,OAAO;AACX,QAAI,WAAW,IAAI,OAAO,CAAC,MAAM,OAAO,KAAK,IAAI,EAAE,IAAI,SAAU,aAAa;AAC5E,aAAO,MAAM,cAAc;AAAA,IAC7B,CAAC,EACE,KAAK,MAAM,CAAC;AAGf,QAAI,YAAY;AAAA,MACd,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,cAAc;AAAA,IAChB;AAEA,QAAI,YAAY,CAAC,SAAS,WAAW,WAAW,cAAc;AAE9D,aAAS,kBAAmB,UAAU;AAEpC,UAAI,eAAe,WAAW,SAAS,MAAM,SAAS,MAAM;AAC5D,aAAO,SAAS,cAAc,EAAE,IAAI;AAAA,IACtC;AAEA,aAAS,MAAO,UAAU;AACxB,UAAI,CAAC,SAAU,QAAO,CAAC;AACvB,UAAI,UAAU,SAAS,KAAK,QAAQ;AACpC,UAAI,aAAa,QAAQ,CAAC,MAAM;AAChC,aAAO,OAAO,KAAK,SAAS,EACzB,OAAO,SAAU,QAAQ,UAAU;AAClC,YAAI,WAAW,UAAU,QAAQ;AACjC,YAAI,QAAQ,QAAQ,QAAQ;AAE5B,YAAI,CAAC,MAAO,QAAO;AAGnB,gBAAQ,aAAa,iBACjB,kBAAkB,KAAK,IACvB,SAAS,OAAO,EAAE;AAEtB,YAAI,CAAC,MAAO,QAAO;AACnB,YAAI,cAAc,CAAC,UAAU,QAAQ,QAAQ,GAAG;AAC9C,mBAAS;AAAA,QACX;AACA,eAAO,QAAQ,IAAI;AACnB,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AAAA,IACT;AAAA;AAAA;;;AC5HA;AAAA;AAAA;AAEA,WAAO,UAAU,SAAS,WAAY,OAAO;AAC3C,UAAI,OAAO,KAAK,KAAK,GAAG;AAEtB,eAAO,IAAI,OAAO,MAAM,OAAO,CAAC,GAAG,KAAK;AAAA,MAC1C;AACA,UAAI,SAAS;AACb,UAAI,IAAI;AACR,aAAO,IAAI,MAAM,QAAQ;AACvB,YAAI,MAAM,CAAC,MAAM,MAAM;AACrB,oBAAU,MAAM,CAAC;AACjB,YAAE;AAAA,QACJ,OAAO;AACL,cAAI,WAAW,KAAK,MAAM,OAAO,IAAI,GAAG,CAAC,CAAC,GAAG;AAC3C,sBAAU,OAAO,aAAa,SAAS,MAAM,OAAO,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;AACjE,iBAAK;AAAA,UACP,OAAO;AACL,gBAAI,cAAc;AAClB,mBAAO,IAAI,cAAc,MAAM,UAAU,MAAM,IAAI,WAAW,MAAM,MAAM;AACxE;AAAA,YACF;AACA,qBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,cAAc,CAAC,GAAG,EAAE,GAAG;AACpD,wBAAU;AAAA,YACZ;AACA,iBAAK,KAAK,MAAM,cAAc,CAAC,IAAI;AAAA,UACrC;AAAA,QACF;AAAA,MACF;AACA,aAAO,IAAI,OAAO,QAAQ,QAAQ;AAAA,IACpC;AAAA;AAAA;;;AC9BA;AAAA;AAAA,QAAI,QAAQ;AACZ,QAAI,cAAc;AAClB,QAAI,YAAY;AAChB,QAAI,gBAAgB;AACpB,QAAI,aAAa;AAEjB,aAAS,UAAW,IAAI;AACtB,aAAO,SAAS,YAAa,OAAO;AAClC,YAAI,UAAU,KAAM,QAAO;AAC3B,eAAO,GAAG,KAAK;AAAA,MACjB;AAAA,IACF;AAEA,aAAS,UAAW,OAAO;AACzB,UAAI,UAAU,KAAM,QAAO;AAC3B,aAAO,UAAU,UACf,UAAU,OACV,UAAU,UACV,UAAU,OACV,UAAU,SACV,UAAU,QACV,UAAU;AAAA,IACd;AAEA,aAAS,eAAgB,OAAO;AAC9B,UAAI,CAAC,MAAO,QAAO;AACnB,aAAO,MAAM,MAAM,OAAO,SAAS;AAAA,IACrC;AAEA,aAAS,gBAAiB,QAAQ;AAChC,aAAO,SAAS,QAAQ,EAAE;AAAA,IAC5B;AAEA,aAAS,kBAAmB,OAAO;AACjC,UAAI,CAAC,MAAO,QAAO;AACnB,aAAO,MAAM,MAAM,OAAO,UAAU,eAAe,CAAC;AAAA,IACtD;AAEA,aAAS,qBAAsB,OAAO;AACpC,UAAI,CAAC,MAAO,QAAO;AACnB,aAAO,MAAM,MAAM,OAAO,UAAU,SAAU,OAAO;AACnD,eAAO,gBAAgB,KAAK,EAAE,KAAK;AAAA,MACrC,CAAC,CAAC;AAAA,IACJ;AAEA,QAAI,kBAAkB,SAAS,OAAO;AACpC,UAAG,CAAC,OAAO;AAAE,eAAO;AAAA,MAAM;AAC1B,UAAI,IAAI,YAAY,OAAO,OAAO,SAAS,OAAO;AAChD,YAAG,UAAU,MAAM;AACjB,kBAAQ,WAAW,KAAK;AAAA,QAC1B;AACA,eAAO;AAAA,MACT,CAAC;AAED,aAAO,EAAE,MAAM;AAAA,IACjB;AAEA,QAAI,kBAAkB,SAAS,OAAO;AACpC,UAAG,CAAC,OAAO;AAAE,eAAO;AAAA,MAAM;AAC1B,UAAI,IAAI,YAAY,OAAO,OAAO,SAAS,OAAO;AAChD,YAAG,UAAU,MAAM;AACjB,kBAAQ,WAAW,KAAK;AAAA,QAC1B;AACA,eAAO;AAAA,MACT,CAAC;AAED,aAAO,EAAE,MAAM;AAAA,IACjB;AAEA,QAAI,mBAAmB,SAAS,OAAO;AACrC,UAAG,CAAC,OAAO;AAAE,eAAO;AAAA,MAAM;AAE1B,UAAI,IAAI,YAAY,OAAO,KAAK;AAChC,aAAO,EAAE,MAAM;AAAA,IACjB;AAEA,QAAI,iBAAiB,SAAS,OAAO;AACnC,UAAI,CAAC,OAAO;AAAE,eAAO;AAAA,MAAM;AAE3B,UAAI,IAAI,YAAY,OAAO,OAAO,SAAS,OAAO;AAChD,YAAI,UAAU,MAAM;AAClB,kBAAQ,UAAU,KAAK;AAAA,QACzB;AACA,eAAO;AAAA,MACT,CAAC;AAED,aAAO,EAAE,MAAM;AAAA,IACjB;AAEA,QAAI,qBAAqB,SAAS,OAAO;AACvC,UAAI,CAAC,OAAO;AAAE,eAAO;AAAA,MAAM;AAE3B,UAAI,IAAI,YAAY,OAAO,OAAO,SAAS,OAAO;AAChD,YAAI,UAAU,MAAM;AAClB,kBAAQ,cAAc,KAAK;AAAA,QAC7B;AACA,eAAO;AAAA,MACT,CAAC;AAED,aAAO,EAAE,MAAM;AAAA,IACjB;AAEA,QAAI,kBAAkB,SAAS,OAAO;AACpC,UAAI,CAAC,OAAO;AAAE,eAAO;AAAA,MAAM;AAE3B,aAAO,MAAM,MAAM,OAAO,UAAU,UAAU,CAAC;AAAA,IACjD;AAEA,QAAI,eAAe,SAAS,OAAO;AACjC,aAAO,SAAS,OAAO,EAAE;AAAA,IAC3B;AAEA,QAAI,kBAAkB,SAAS,OAAO;AACpC,UAAI,SAAS,OAAO,KAAK;AACzB,UAAI,QAAQ,KAAK,MAAM,GAAG;AAAE,eAAO;AAAA,MAAQ;AAC3C,aAAO;AAAA,IACT;AAEA,QAAI,iBAAiB,SAAS,OAAO;AACnC,UAAI,CAAC,OAAO;AAAE,eAAO;AAAA,MAAM;AAE3B,aAAO,MAAM,MAAM,OAAO,UAAU,KAAK,KAAK,CAAC;AAAA,IACjD;AAEA,QAAI,aAAa,SAAS,OAAO;AAC/B,UAAI,MAAM,CAAC,MAAM,KAAK;AAAE,eAAO;AAAA,MAAM;AAErC,cAAQ,MAAM,UAAW,GAAG,MAAM,SAAS,CAAE,EAAE,MAAM,GAAG;AAExD,aAAO;AAAA,QACL,GAAG,WAAW,MAAM,CAAC,CAAC;AAAA,QACtB,GAAG,WAAW,MAAM,CAAC,CAAC;AAAA,MACxB;AAAA,IACF;AAEA,QAAI,cAAc,SAAS,OAAO;AAChC,UAAI,MAAM,CAAC,MAAM,OAAO,MAAM,CAAC,MAAM,KAAK;AAAE,eAAO;AAAA,MAAM;AAEzD,UAAI,QAAQ;AACZ,UAAI,SAAS;AACb,UAAI,cAAc;AAClB,eAAS,IAAI,GAAG,IAAI,MAAM,SAAS,GAAG,KAAI;AACxC,YAAI,CAAC,aAAa;AAChB,mBAAS,MAAM,CAAC;AAAA,QAClB;AAEA,YAAI,MAAM,CAAC,MAAM,KAAK;AACpB,wBAAc;AACd;AAAA,QACF,WAAW,CAAC,aAAa;AACvB;AAAA,QACF;AAEA,YAAI,MAAM,CAAC,MAAM,KAAI;AACnB;AAAA,QACF;AAEA,kBAAU,MAAM,CAAC;AAAA,MACnB;AACA,UAAI,SAAS,WAAW,KAAK;AAC7B,aAAO,SAAS,WAAW,MAAM;AAEjC,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,SAAS,UAAU;AAC5B,eAAS,IAAI,eAAe;AAC5B,eAAS,IAAI,YAAY;AACzB,eAAS,IAAI,YAAY;AACzB,eAAS,IAAI,YAAY;AACzB,eAAS,KAAK,UAAU;AACxB,eAAS,KAAK,UAAU;AACxB,eAAS,IAAI,SAAS;AACtB,eAAS,MAAM,SAAS;AACxB,eAAS,MAAM,SAAS;AACxB,eAAS,MAAM,SAAS;AACxB,eAAS,KAAK,UAAU;AACxB,eAAS,KAAK,gBAAgB;AAC9B,eAAS,KAAK,WAAW;AACzB,eAAS,KAAM,cAAc;AAC7B,eAAS,MAAM,eAAe;AAC9B,eAAS,MAAM,iBAAiB;AAChC,eAAS,MAAM,iBAAiB;AAChC,eAAS,MAAM,iBAAiB;AAChC,eAAS,MAAM,oBAAoB;AACnC,eAAS,MAAM,eAAe;AAC9B,eAAS,MAAM,eAAe;AAC9B,eAAS,MAAM,eAAe;AAC9B,eAAS,MAAM,eAAe;AAC9B,eAAS,MAAM,gBAAgB;AAC/B,eAAS,MAAM,gBAAgB;AAC/B,eAAS,MAAM,gBAAgB;AAC/B,eAAS,MAAM,gBAAgB;AAC/B,eAAS,MAAM,gBAAgB;AAC/B,eAAS,MAAM,gBAAgB;AAC/B,eAAS,MAAM,cAAc;AAC7B,eAAS,MAAM,cAAc;AAC7B,eAAS,MAAM,cAAc;AAC7B,eAAS,MAAM,aAAa;AAC5B,eAAS,MAAM,kBAAkB;AACjC,eAAS,IAAI,UAAU;AACvB,eAAS,KAAK,KAAK,MAAM,KAAK,IAAI,CAAC;AACnC,eAAS,MAAM,KAAK,MAAM,KAAK,IAAI,CAAC;AACpC,eAAS,KAAK,cAAc;AAC5B,eAAS,MAAM,cAAc;AAC7B,eAAS,MAAM,gBAAgB;AAC/B,eAAS,MAAM,gBAAgB;AAC/B,eAAS,KAAK,gBAAgB;AAC9B,eAAS,MAAM,gBAAgB;AAC/B,eAAS,MAAM,gBAAgB;AAAA,IACjC;AAEA,WAAO,UAAU;AAAA,MACf;AAAA,IACF;AAAA;AAAA;;;ACtNA;AAAA;AAAA;AAGA,QAAI,OAAO;AAEX,aAAS,SAAS,QAAQ;AACzB,UAAI,OAAO,OAAO,YAAY,CAAC;AAC/B,UAAI,MAAM,OAAO,aAAa,CAAC;AAC/B,UAAI,OAAO;AAEX,UAAI,OAAO,GAAG;AACb,eAAO,CAAC,QAAQ,QAAQ;AACxB,cAAO,CAAC,MAAM,MAAO;AACrB,eAAO;AAAA,MACR;AAEA,UAAI,SAAS;AACb,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ;AACC,gBAAQ,OAAO;AACf,eAAO,OAAO,SAAS;AAEvB,YAAI,aAAc,QAAQ;AAC1B,cAAM,IAAI,SAAS;AACnB,iBAAS,MAAM,IAAI,OAAO;AAE1B,YAAI,QAAQ,KAAK,SAAS,GAAG;AAC5B,iBAAO,OAAO,SAAS;AAAA,QACxB;AAEA,cAAM;AACN,YAAI,IAAI,OAAO;AAEf,aAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACvB,iBAAO;AAAA,QACR;AAEA,iBAAS,MAAM,SAAS;AAAA,MACzB;AAEA;AACC,gBAAQ,OAAO;AACf,eAAO,OAAO,SAAS;AAEvB,YAAI,aAAc,QAAQ;AAC1B,cAAM,IAAI,SAAS;AACnB,iBAAS,MAAM,IAAI,OAAO;AAE1B,YAAI,QAAQ,KAAK,SAAS,GAAG;AAC5B,iBAAO,OAAO,SAAS;AAAA,QACxB;AAEA,cAAM;AACN,YAAI,IAAI,OAAO;AAEf,aAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACvB,iBAAO;AAAA,QACR;AAEA,iBAAS,MAAM,SAAS;AAAA,MACzB;AAEA;AACC,gBAAQ,OAAO;AACf,eAAO,OAAO,SAAS;AAEvB,YAAI,aAAc,QAAQ;AAC1B,cAAM,IAAI,SAAS;AACnB,iBAAS,MAAM,IAAI,OAAO;AAE1B,YAAI,QAAQ,KAAK,SAAS,GAAG;AAC5B,iBAAO,OAAO,SAAS;AAAA,QACxB;AAEA,cAAM;AACN,YAAI,IAAI,OAAO;AAEf,aAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACvB,iBAAO;AAAA,QACR;AAEA,iBAAS,MAAM,SAAS;AAAA,MACzB;AAEA;AACC,gBAAQ,OAAO;AACf,YAAI,aAAc,QAAQ;AAC1B,iBAAS,KAAK,IAAI;AAElB,eAAO,OAAO,SAAS;AAAA,MACxB;AAAA,IACD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACnGjB;AAAA;AAAA,QAAI,aAAa;AAEjB,QAAI,YAAY,SAAS,MAAM,MAAM,QAAQ,QAAQ,UAAU;AAC7D,eAAS,UAAU;AACnB,eAAS,UAAU;AACnB,iBAAW,YAAY,SAAS,WAAW,UAAUA,OAAM;AAAE,eAAQ,YAAY,KAAK,IAAI,GAAGA,KAAI,IAAK;AAAA,MAAU;AAChH,UAAI,cAAc,UAAU;AAE5B,UAAI,MAAM,SAAS,OAAO;AACxB,YAAI,QAAQ;AACV,iBAAO,CAAC,QAAQ;AAAA,QAClB;AAEA,eAAO;AAAA,MACT;AAGA,UAAI,OAAO;AACX,UAAI,YAAY,IAAK,SAAS;AAC9B,UAAI,OAAO,WAAW;AACpB,eAAQ,OAAS,IAAI,OAAS;AAC9B,oBAAY;AAAA,MACd;AAEA,UAAI,QAAQ;AACV,eAAO,QAAS,SAAS;AAAA,MAC3B;AAEA,UAAI,SAAS;AACb,UAAK,SAAS,IAAK,QAAQ,GAAG;AAC5B,iBAAS,SAAS,GAAG,IAAI,KAAK,WAAW,CAAC,IAAI,MAAM,SAAS;AAAA,MAC/D;AAGA,UAAI,QAAS,OAAO,UAAW;AAC/B,eAAS,IAAI,cAAc,GAAG,IAAI,OAAO,KAAK;AAC5C,iBAAS,SAAS,QAAQ,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC;AAAA,MAC3C;AAGA,UAAI,YAAY,OAAO,UAAU;AACjC,UAAI,WAAW,GAAG;AAChB,iBAAS,SAAS,QAAQ,IAAI,KAAK,KAAK,CAAC,KAAM,IAAI,UAAW,QAAQ;AAAA,MACxE;AAEA,aAAO;AAAA,IACT;AAEA,QAAI,qBAAqB,SAAS,MAAM,eAAe,cAAc;AACnE,UAAI,OAAO,KAAK,IAAI,GAAG,eAAe,CAAC,IAAI;AAC3C,UAAI,OAAO,UAAU,MAAM,CAAC;AAC5B,UAAI,WAAW,UAAU,MAAM,cAAc,CAAC;AAE9C,UAAI,aAAa,GAAG;AAClB,eAAO;AAAA,MACT;AAGA,UAAI,uBAAuB;AAC3B,UAAI,qBAAqB,SAAS,WAAW,UAAU,MAAM;AAC3D,YAAI,cAAc,GAAG;AACnB,sBAAY;AAAA,QACd;AAEA,iBAAS,IAAI,GAAG,KAAK,MAAM,KAAK;AAC9B,kCAAwB;AACxB,eAAK,WAAY,KAAQ,OAAO,KAAO,GAAG;AACxC,yBAAa;AAAA,UACf;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAEA,UAAI,WAAW,UAAU,MAAM,eAAe,eAAe,GAAG,OAAO,kBAAkB;AAGzF,UAAI,YAAa,KAAK,IAAI,GAAG,eAAe,CAAC,IAAI,GAAI;AACnD,YAAI,aAAa,GAAG;AAClB,iBAAQ,SAAS,IAAK,WAAW;AAAA,QACnC;AAEA,eAAO;AAAA,MACT;AAGA,cAAS,SAAS,IAAK,IAAI,MAAM,KAAK,IAAI,GAAG,WAAW,IAAI,IAAI;AAAA,IAClE;AAEA,QAAI,aAAa,SAAS,OAAO;AAC/B,UAAI,UAAU,OAAO,CAAC,KAAK,GAAG;AAC5B,eAAO,MAAM,UAAU,OAAO,IAAI,GAAG,IAAI,IAAI;AAAA,MAC/C;AAEA,aAAO,UAAU,OAAO,IAAI,CAAC;AAAA,IAC/B;AAEA,QAAI,aAAa,SAAS,OAAO;AAC/B,UAAI,UAAU,OAAO,CAAC,KAAK,GAAG;AAC5B,eAAO,MAAM,UAAU,OAAO,IAAI,GAAG,IAAI,IAAI;AAAA,MAC/C;AAEA,aAAO,UAAU,OAAO,IAAI,CAAC;AAAA,IAC/B;AAEA,QAAI,eAAe,SAAS,OAAO;AACjC,aAAO,mBAAmB,OAAO,IAAI,CAAC;AAAA,IACxC;AAEA,QAAI,eAAe,SAAS,OAAO;AACjC,aAAO,mBAAmB,OAAO,IAAI,EAAE;AAAA,IACzC;AAEA,QAAI,eAAe,SAAS,OAAO;AACjC,UAAI,OAAO,UAAU,OAAO,IAAI,EAAE;AAClC,UAAI,QAAQ,OAAQ;AAClB,eAAO;AAAA,MACT;AAEA,UAAI,SAAS,KAAK,IAAI,KAAO,UAAU,OAAO,IAAI,EAAE,CAAC;AACrD,UAAI,SAAS;AAEb,UAAI,SAAS,CAAC;AACd,UAAI,UAAU,UAAU,OAAO,EAAE;AACjC,eAAS,IAAI,GAAG,IAAI,SAAS,KAAK;AAChC,kBAAU,UAAU,OAAO,IAAI,KAAM,KAAK,CAAE,IAAI;AAChD,kBAAU;AAAA,MACZ;AAEA,UAAI,QAAQ,KAAK,IAAI,IAAI,UAAU,OAAO,IAAI,EAAE,CAAC;AACjD,cAAS,SAAS,IAAK,IAAI,MAAM,KAAK,MAAM,SAAS,KAAK,IAAI;AAAA,IAChE;AAEA,QAAI,YAAY,SAAS,OAAO,OAAO;AACrC,UAAI,OAAO,UAAU,OAAO,CAAC;AAC7B,UAAI,WAAW,UAAU,OAAO,IAAI,CAAC;AAGrC,UAAI,SAAS,IAAI,MAAQ,SAAS,IAAK,IAAI,MAAM,WAAW,MAAQ,SAAY;AAEhF,UAAI,CAAC,OAAO;AACV,eAAO,QAAQ,OAAO,QAAQ,IAAI,OAAO,kBAAkB,IAAI,GAAK;AAAA,MACtE;AAGA,aAAO,OAAO,WAAW;AACzB,aAAO,kBAAkB,WAAW;AAClC,eAAO,KAAK;AAAA,MACd;AACA,aAAO,kBAAkB,SAASC,QAAO;AACvC,aAAK,OAAOA;AAAA,MACd;AACA,aAAO,qBAAqB,WAAW;AACrC,eAAO,KAAK;AAAA,MACd;AAEA,aAAO;AAAA,IACT;AAEA,QAAI,aAAa,SAAS,OAAO;AAC/B,UAAI,MAAM,UAAU,OAAO,EAAE;AAE7B,UAAI,QAAQ,UAAU,OAAO,IAAI,EAAE;AACnC,UAAI,cAAc,UAAU,OAAO,IAAI,EAAE;AAEzC,UAAI,SAAS;AACb,UAAI,OAAO,CAAC;AACZ,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAE5B,aAAK,CAAC,IAAI,UAAU,OAAO,IAAI,MAAM;AACrC,kBAAU;AAGV,kBAAU;AAAA,MACZ;AAEA,UAAI,eAAe,SAASC,cAAa;AAEvC,YAAI,SAAS,UAAU,OAAO,IAAI,MAAM;AACxC,kBAAU;AAGV,YAAI,UAAU,YAAY;AACxB,iBAAO;AAAA,QACT;AAEA,YAAI;AACJ,YAAKA,gBAAe,MAAUA,gBAAe,IAAO;AAElD,mBAAS,UAAU,OAAO,SAAS,GAAG,MAAM;AAC5C,oBAAU,SAAS;AACnB,iBAAO;AAAA,QACT,WACSA,gBAAe,IAAM;AAE5B,mBAAS,MAAM,SAAS,KAAK,UAAU,UAAU,IAAI,UAAW,UAAU,MAAO,CAAC;AAClF,iBAAO;AAAA,QACT,OACK;AACH,kBAAQ,IAAI,yCAAyCA,YAAW;AAAA,QAClE;AAAA,MACF;AAEA,UAAI,QAAQ,SAAS,WAAWA,cAAa;AAC3C,YAAI,QAAQ,CAAC;AACb,YAAIC;AAEJ,YAAI,UAAU,SAAS,GAAG;AACxB,cAAI,QAAQ,UAAU,MAAM;AAC5B,eAAKA,KAAI,GAAGA,KAAI,OAAOA,MAAK;AAC1B,kBAAMA,EAAC,IAAI,MAAM,WAAWD,YAAW;AAAA,UACzC;AACA,oBAAU,QAAQ,KAAK;AAAA,QACzB,OACK;AACH,eAAKC,KAAI,GAAGA,KAAI,UAAU,CAAC,GAAGA,MAAK;AACjC,kBAAMA,EAAC,IAAI,aAAaD,YAAW;AAAA,UACrC;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAEA,aAAO,MAAM,MAAM,WAAW;AAAA,IAChC;AAEA,QAAI,YAAY,SAAS,OAAO;AAC9B,aAAO,MAAM,SAAS,MAAM;AAAA,IAC9B;AAEA,QAAI,YAAY,SAAS,OAAO;AAC9B,UAAG,UAAU,KAAM,QAAO;AAC1B,aAAQ,UAAU,OAAO,CAAC,IAAI;AAAA,IAChC;AAEA,QAAI,OAAO,SAAS,UAAU;AAC5B,eAAS,IAAI,UAAU;AACvB,eAAS,IAAI,UAAU;AACvB,eAAS,IAAI,UAAU;AACvB,eAAS,IAAI,UAAU;AACvB,eAAS,MAAM,YAAY;AAC3B,eAAS,KAAK,YAAY;AAC1B,eAAS,KAAK,YAAY;AAC1B,eAAS,IAAI,SAAS;AACtB,eAAS,MAAM,UAAU,KAAK,MAAM,KAAK,CAAC;AAC1C,eAAS,MAAM,UAAU,KAAK,MAAM,IAAI,CAAC;AACzC,eAAS,KAAM,UAAU;AACzB,eAAS,MAAM,UAAU;AACzB,eAAS,MAAM,UAAU;AACzB,eAAS,MAAM,UAAU;AACzB,eAAS,MAAM,UAAU;AACzB,eAAS,IAAI,SAAS;AAAA,IACxB;AAEA,WAAO,UAAU;AAAA,MACf;AAAA,IACF;AAAA;AAAA;;;AChQA;AAAA;AAWA,WAAO,UAAU;AAAA,MACb,MAAM;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,MACT,MAAM;AAAA,MACN,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,KAAK;AAAA,MACL,cAAc;AAAA,MACd,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,OAAO;AAAA,MACP,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,MACX,aAAa;AAAA,MACb,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,SAAS;AAAA,MACT,MAAM;AAAA,MACN,eAAe;AAAA,MACf,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,iBAAiB;AAAA,MACjB,UAAU;AAAA,MACV,SAAS;AAAA,MACT,WAAW;AAAA,MACX,WAAW;AAAA,MACX,eAAe;AAAA,MACf,OAAO;AAAA,MACP,cAAc;AAAA,MACd,SAAS;AAAA,IACb;AAAA;AAAA;;;ACxEA;AAAA;AAAA,QAAI,cAAc;AAClB,QAAI,gBAAgB;AACpB,QAAI,cAAc;AAClB,QAAI,eAAe;AAEnB,YAAQ,gBAAgB;AACxB,YAAQ,gBAAgB;AACxB,YAAQ,cAAc;AACtB,YAAQ,WAAW;AAEnB,QAAI,cAAc;AAAA,MAChB,MAAM,CAAC;AAAA,MACP,QAAQ,CAAC;AAAA,IACX;AAGA,aAAS,QAAS,KAAK;AACrB,aAAO,OAAO,GAAG;AAAA,IACnB;AAMA,aAAS,cAAe,KAAK,QAAQ;AACnC,eAAS,UAAU;AACnB,UAAI,CAAC,YAAY,MAAM,GAAG;AACxB,eAAO;AAAA,MACT;AACA,aAAO,YAAY,MAAM,EAAE,GAAG,KAAK;AAAA,IACrC;AAEA,aAAS,cAAe,KAAK,QAAQ,SAAS;AAC5C,UAAG,OAAO,UAAU,YAAY;AAC9B,kBAAU;AACV,iBAAS;AAAA,MACX;AACA,kBAAY,MAAM,EAAE,GAAG,IAAI;AAAA,IAC7B;AAEA,gBAAY,KAAK,SAAS,KAAK,WAAW;AACxC,kBAAY,KAAK,GAAG,IAAI;AAAA,IAC1B,CAAC;AAED,kBAAc,KAAK,SAAS,KAAK,WAAW;AAC1C,kBAAY,OAAO,GAAG,IAAI;AAAA,IAC5B,CAAC;AAAA;AAAA;;;AC9CD;AAAA;AAAA;AAEA,WAAO,UAAU;AAAA;AAAA,MAEf,MAAM;AAAA;AAAA,MAGN,MAAM,QAAQ,aAAa,UAAU,QAAQ,IAAI,WAAW,QAAQ,IAAI;AAAA;AAAA,MAGxE,UAAU;AAAA;AAAA,MAGV,UAAU;AAAA;AAAA;AAAA;AAAA,MAKV,kBAAkB;AAAA;AAAA,MAGlB,MAAM;AAAA;AAAA;AAAA,MAIN,MAAM;AAAA;AAAA,MAGN,QAAQ;AAAA;AAAA;AAAA;AAAA,MAMR,KAAK;AAAA;AAAA;AAAA,MAIL,mBAAmB;AAAA,MAEnB,iBAAiB;AAAA,MAEjB,KAAK;AAAA,MAEL,kBAAkB;AAAA,MAElB,2BAA2B;AAAA,MAE3B,SAAS;AAAA,MAET,sBAAsB;AAAA;AAAA;AAAA,MAItB,mBAAmB;AAAA;AAAA;AAAA,MAInB,cAAc;AAAA;AAAA;AAAA,MAId,qCAAqC;AAAA;AAAA,MAGrC,eAAe;AAAA,MAEf,iBAAiB;AAAA,MAEjB,YAAY;AAAA,MAEZ,iBAAiB;AAAA,IACnB;AAEA,QAAM,UAAU;AAEhB,QAAM,kBAAkB,QAAQ,cAAc,IAAI,MAAM;AACxD,QAAM,uBAAuB,QAAQ,cAAc,MAAM,MAAM;AAG/D,WAAO,QAAQ,iBAAiB,aAAa,SAAU,KAAK;AAC1D,cAAQ,cAAc,IAAI,QAAQ,MAAM,QAAQ,cAAc,IAAI,MAAM,IAAI,eAAe;AAC3F,cAAQ,cAAc,MAAM,QAAQ,MAAM,QAAQ,cAAc,MAAM,MAAM,IAAI,oBAAoB;AAAA,IACtG,CAAC;AAAA;AAAA;;;ACnFD;AAAA;AAAA;AAEA,QAAME,YAAW;AAEjB,QAAM,OAAO;AACb,QAAM,EAAE,OAAO,IAAI,KAAK,SAAS;AAEjC,aAAS,cAAc,uBAAuB;AAC5C,YAAM,UAAU,sBAAsB,QAAQ,OAAO,MAAM,EAAE,QAAQ,MAAM,KAAK;AAEhF,aAAO,MAAM,UAAU;AAAA,IACzB;AAKA,aAAS,YAAY,KAAK;AACxB,UAAI,SAAS;AACb,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,YAAI,IAAI,GAAG;AACT,mBAAS,SAAS;AAAA,QACpB;AACA,YAAI,IAAI,CAAC,MAAM,QAAQ,OAAO,IAAI,CAAC,MAAM,aAAa;AACpD,mBAAS,SAAS;AAAA,QACpB,WAAW,MAAM,QAAQ,IAAI,CAAC,CAAC,GAAG;AAChC,mBAAS,SAAS,YAAY,IAAI,CAAC,CAAC;AAAA,QACtC,WAAW,YAAY,OAAO,IAAI,CAAC,CAAC,GAAG;AACrC,cAAI,OAAO,IAAI,CAAC;AAChB,cAAI,EAAE,gBAAgB,SAAS;AAC7B,kBAAM,MAAM,OAAO,KAAK,KAAK,QAAQ,KAAK,YAAY,KAAK,UAAU;AACrE,gBAAI,IAAI,WAAW,KAAK,YAAY;AAClC,qBAAO;AAAA,YACT,OAAO;AACL,qBAAO,IAAI,MAAM,KAAK,YAAY,KAAK,aAAa,KAAK,UAAU;AAAA,YACrE;AAAA,UACF;AACA,oBAAU,UAAU,KAAK,SAAS,KAAK;AAAA,QACzC,OAAO;AACL,oBAAU,cAAc,aAAa,IAAI,CAAC,CAAC,CAAC;AAAA,QAC9C;AAAA,MACF;AACA,eAAS,SAAS;AAClB,aAAO;AAAA,IACT;AAMA,QAAM,eAAe,SAAU,KAAK,MAAM;AAExC,UAAI,OAAO,MAAM;AACf,eAAO;AAAA,MACT;AACA,UAAI,OAAO,QAAQ,UAAU;AAC3B,YAAI,eAAe,QAAQ;AACzB,iBAAO;AAAA,QACT;AACA,YAAI,YAAY,OAAO,GAAG,GAAG;AAC3B,gBAAM,MAAM,OAAO,KAAK,IAAI,QAAQ,IAAI,YAAY,IAAI,UAAU;AAClE,cAAI,IAAI,WAAW,IAAI,YAAY;AACjC,mBAAO;AAAA,UACT;AACA,iBAAO,IAAI,MAAM,IAAI,YAAY,IAAI,aAAa,IAAI,UAAU;AAAA,QAClE;AACA,YAAI,OAAO,GAAG,GAAG;AACf,cAAIA,UAAS,sBAAsB;AACjC,mBAAO,gBAAgB,GAAG;AAAA,UAC5B,OAAO;AACL,mBAAO,aAAa,GAAG;AAAA,UACzB;AAAA,QACF;AACA,YAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,iBAAO,YAAY,GAAG;AAAA,QACxB;AAEA,eAAO,cAAc,KAAK,IAAI;AAAA,MAChC;AACA,aAAO,IAAI,SAAS;AAAA,IACtB;AAEA,aAAS,cAAc,KAAK,MAAM;AAChC,UAAI,OAAO,OAAO,IAAI,eAAe,YAAY;AAC/C,eAAO,QAAQ,CAAC;AAChB,YAAI,KAAK,QAAQ,GAAG,MAAM,IAAI;AAC5B,gBAAM,IAAI,MAAM,kDAAkD,MAAM,aAAa;AAAA,QACvF;AACA,aAAK,KAAK,GAAG;AAEb,eAAO,aAAa,IAAI,WAAW,YAAY,GAAG,IAAI;AAAA,MACxD;AACA,aAAO,KAAK,UAAU,GAAG;AAAA,IAC3B;AAEA,aAAS,aAAa,MAAM;AAC1B,UAAI,SAAS,CAAC,KAAK,kBAAkB;AAErC,UAAI,OAAO,KAAK,YAAY;AAC5B,YAAM,WAAW,OAAO;AACxB,UAAI,SAAU,QAAO,KAAK,IAAI,IAAI,IAAI;AAEtC,UAAI,MACF,OAAO,IAAI,EAAE,SAAS,GAAG,GAAG,IAC5B,MACA,OAAO,KAAK,SAAS,IAAI,CAAC,EAAE,SAAS,GAAG,GAAG,IAC3C,MACA,OAAO,KAAK,QAAQ,CAAC,EAAE,SAAS,GAAG,GAAG,IACtC,MACA,OAAO,KAAK,SAAS,CAAC,EAAE,SAAS,GAAG,GAAG,IACvC,MACA,OAAO,KAAK,WAAW,CAAC,EAAE,SAAS,GAAG,GAAG,IACzC,MACA,OAAO,KAAK,WAAW,CAAC,EAAE,SAAS,GAAG,GAAG,IACzC,MACA,OAAO,KAAK,gBAAgB,CAAC,EAAE,SAAS,GAAG,GAAG;AAEhD,UAAI,SAAS,GAAG;AACd,eAAO;AACP,kBAAU;AAAA,MACZ,OAAO;AACL,eAAO;AAAA,MACT;AAEA,aAAO,OAAO,KAAK,MAAM,SAAS,EAAE,CAAC,EAAE,SAAS,GAAG,GAAG,IAAI,MAAM,OAAO,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG;AACnG,UAAI,SAAU,QAAO;AACrB,aAAO;AAAA,IACT;AAEA,aAAS,gBAAgB,MAAM;AAC7B,UAAI,OAAO,KAAK,eAAe;AAC/B,YAAM,WAAW,OAAO;AACxB,UAAI,SAAU,QAAO,KAAK,IAAI,IAAI,IAAI;AAEtC,UAAI,MACF,OAAO,IAAI,EAAE,SAAS,GAAG,GAAG,IAC5B,MACA,OAAO,KAAK,YAAY,IAAI,CAAC,EAAE,SAAS,GAAG,GAAG,IAC9C,MACA,OAAO,KAAK,WAAW,CAAC,EAAE,SAAS,GAAG,GAAG,IACzC,MACA,OAAO,KAAK,YAAY,CAAC,EAAE,SAAS,GAAG,GAAG,IAC1C,MACA,OAAO,KAAK,cAAc,CAAC,EAAE,SAAS,GAAG,GAAG,IAC5C,MACA,OAAO,KAAK,cAAc,CAAC,EAAE,SAAS,GAAG,GAAG,IAC5C,MACA,OAAO,KAAK,mBAAmB,CAAC,EAAE,SAAS,GAAG,GAAG;AAEnD,aAAO;AACP,UAAI,SAAU,QAAO;AACrB,aAAO;AAAA,IACT;AAEA,aAAS,qBAAqB,QAAQ,QAAQ,UAAU;AAEtD,eAAS,OAAO,WAAW,WAAW,EAAE,MAAM,OAAO,IAAI;AACzD,UAAI,QAAQ;AACV,YAAI,OAAO,WAAW,YAAY;AAChC,iBAAO,WAAW;AAAA,QACpB,OAAO;AACL,iBAAO,SAAS;AAAA,QAClB;AAAA,MACF;AACA,UAAI,UAAU;AACZ,eAAO,WAAW;AAAA,MACpB;AACA,aAAO;AAAA,IACT;AAGA,QAAMC,oBAAmB,SAAU,KAAK;AACtC,aAAO,MAAM,IAAI,QAAQ,MAAM,IAAI,IAAI;AAAA,IACzC;AAEA,QAAMC,iBAAgB,SAAU,KAAK;AACnC,UAAI,eAAe;AACnB,UAAI,UAAU;AAEd,UAAI,OAAO,MAAM;AACf,eAAO;AAAA,MACT;AAEA,UAAI,OAAO,QAAQ,UAAU;AAC3B,eAAO;AAAA,MACT;AAEA,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,cAAM,IAAI,IAAI,CAAC;AACf,YAAI,MAAM,KAAK;AACb,qBAAW,IAAI;AAAA,QACjB,WAAW,MAAM,MAAM;AACrB,qBAAW,IAAI;AACf,yBAAe;AAAA,QACjB,OAAO;AACL,qBAAW;AAAA,QACb;AAAA,MACF;AAEA,iBAAW;AAEX,UAAI,iBAAiB,MAAM;AACzB,kBAAU,OAAO;AAAA,MACnB;AAEA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA,MACf,cAAc,SAAS,oBAAoB,OAAO;AAGhD,eAAO,aAAa,KAAK;AAAA,MAC3B;AAAA,MACA;AAAA,MACA,kBAAAD;AAAA,MACA,eAAAC;AAAA,IACF;AAAA;AAAA;;;ACxNA;AAAA;AAAA;AAIA,QAAM,aAAa;AAEnB,aAAS,IAAI,QAAQ;AACnB,aAAO,WAAW,WAAW,KAAK,EAAE,OAAO,QAAQ,OAAO,EAAE,OAAO,KAAK;AAAA,IAC1E;AAGA,aAAS,wBAAwB,MAAM,UAAU,MAAM;AACrD,YAAM,QAAQ,IAAI,WAAW,IAAI;AACjC,YAAM,QAAQ,IAAI,OAAO,OAAO,CAAC,OAAO,KAAK,KAAK,GAAG,IAAI,CAAC,CAAC;AAC3D,aAAO,QAAQ;AAAA,IACjB;AAEA,aAAS,OAAO,MAAM;AACpB,aAAO,WAAW,WAAW,QAAQ,EAAE,OAAO,IAAI,EAAE,OAAO;AAAA,IAC7D;AAEA,aAAS,WAAW,UAAU,MAAM;AAClC,iBAAW,SAAS,QAAQ,SAAS,IAAI;AACzC,aAAO,WAAW,WAAW,QAAQ,EAAE,OAAO,IAAI,EAAE,OAAO;AAAA,IAC7D;AAEA,aAAS,WAAW,KAAK,KAAK;AAC5B,aAAO,WAAW,WAAW,UAAU,GAAG,EAAE,OAAO,GAAG,EAAE,OAAO;AAAA,IACjE;AAEA,mBAAe,UAAU,UAAU,MAAM,YAAY;AACnD,aAAO,WAAW,WAAW,UAAU,MAAM,YAAY,IAAI,QAAQ;AAAA,IACvE;AAEA,WAAO,UAAU;AAAA,MACf;AAAA,MACA,aAAa,WAAW;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA;;;AC1CA;AAAA;AAAA,QAAM,aAAa;AAEnB,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAOA,QAAM,YAAY,WAAW,aAAa,WAAW;AAKrD,QAAM,eAAe,UAAU;AAC/B,QAAM,cAAc,IAAI,YAAY;AAOpC,aAAS,YAAY,QAAQ;AAC3B,aAAO,UAAU,gBAAgB,OAAO,MAAM,MAAM,CAAC;AAAA,IACvD;AAEA,mBAAe,IAAI,QAAQ;AACzB,UAAI;AACF,eAAO,WAAW,WAAW,KAAK,EAAE,OAAO,QAAQ,OAAO,EAAE,OAAO,KAAK;AAAA,MAC1E,SAAS,GAAG;AAIV,cAAM,OAAO,OAAO,WAAW,WAAW,YAAY,OAAO,MAAM,IAAI;AACvE,cAAM,OAAO,MAAM,aAAa,OAAO,OAAO,IAAI;AAClD,eAAO,MAAM,KAAK,IAAI,WAAW,IAAI,CAAC,EACnC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC,EAC1C,KAAK,EAAE;AAAA,MACZ;AAAA,IACF;AAGA,mBAAe,wBAAwB,MAAM,UAAU,MAAM;AAC3D,YAAM,QAAQ,MAAM,IAAI,WAAW,IAAI;AACvC,YAAM,QAAQ,MAAM,IAAI,OAAO,OAAO,CAAC,OAAO,KAAK,KAAK,GAAG,IAAI,CAAC,CAAC;AACjE,aAAO,QAAQ;AAAA,IACjB;AAMA,mBAAe,OAAO,MAAM;AAC1B,aAAO,MAAM,aAAa,OAAO,WAAW,IAAI;AAAA,IAClD;AAEA,mBAAe,WAAW,UAAU,MAAM;AACxC,aAAO,MAAM,aAAa,OAAO,UAAU,IAAI;AAAA,IACjD;AAOA,mBAAe,WAAW,WAAW,KAAK;AACxC,YAAM,MAAM,MAAM,aAAa,UAAU,OAAO,WAAW,EAAE,MAAM,QAAQ,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC;AAC7G,aAAO,MAAM,aAAa,KAAK,QAAQ,KAAK,YAAY,OAAO,GAAG,CAAC;AAAA,IACrE;AAQA,mBAAe,UAAU,UAAU,MAAM,YAAY;AACnD,YAAM,MAAM,MAAM,aAAa,UAAU,OAAO,YAAY,OAAO,QAAQ,GAAG,UAAU,OAAO,CAAC,YAAY,CAAC;AAC7G,YAAM,SAAS,EAAE,MAAM,UAAU,MAAM,WAAW,MAAY,WAAuB;AACrF,aAAO,MAAM,aAAa,WAAW,QAAQ,KAAK,KAAK,GAAG,CAAC,YAAY,CAAC;AAAA,IAC1E;AAAA;AAAA;;;ACxFA,IAAAC,iBAAA;AAAA;AAAA;AAEA,QAAM,kBAAkB,SAAS,QAAQ,YAAY,QAAQ,SAAS,QAAQ,QAAQ,SAAS,KAAK,MAAM,GAAG,EAAE,CAAC,CAAC,IAAI;AACrH,QAAI,iBAAiB;AAEnB,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACRA;AAAA;AAAA,aAAS,UAAU,KAAK,MAAM;AAC5B,aAAO,IAAI,MAAM,2BAA2B,MAAM,sCAAsC,KAAK,SAAS,QAAQ,CAAC;AAAA,IACjH;AAEA,aAAS,eAAe,MAAM,OAAO;AACnC,UAAI,SAAS,KAAK,OAAO;AACzB,UAAI,SAAS,IAAM,QAAO,EAAE,QAAQ,MAAM;AAE1C,YAAM,cAAc,SAAS;AAC7B,UAAI,cAAc,EAAG,OAAM,UAAU,cAAc,IAAI;AAEvD,eAAS;AACT,eAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,iBAAU,UAAU,IAAK,KAAK,OAAO;AAAA,MACvC;AAEA,aAAO,EAAE,QAAQ,MAAM;AAAA,IACzB;AAEA,aAAS,YAAY,MAAM,OAAO;AAChC,UAAI,KAAK,OAAO,MAAM,EAAK,OAAM,UAAU,gBAAgB,IAAI;AAE/D,YAAM,EAAE,QAAQ,WAAW,OAAO,oBAAoB,IAAI,eAAe,MAAM,KAAK;AACpF,cAAQ;AACR,YAAM,YAAY,QAAQ;AAE1B,YAAM,QAAQ,KAAK,OAAO;AAC1B,UAAI,OAAQ,QAAQ,MAAO,KAAK,MAAO,QAAQ;AAE/C,aAAO,QAAQ,WAAW;AAExB,YAAI,QAAQ;AACZ,eAAO,QAAQ,WAAW;AAExB,gBAAM,WAAW,KAAK,OAAO;AAC7B,kBAAS,SAAS,IAAM,WAAW;AACnC,cAAI,WAAW,IAAM;AAAA,QACvB;AACA,eAAO,MAAM;AAAA,MACf;AAEA,aAAO,EAAE,KAAK,MAAM;AAAA,IACtB;AAEA,aAAS,cAAc,MAAM,OAAO;AAClC,UAAI,KAAK,OAAO,MAAM,GAAM,OAAM,UAAU,qBAAqB,IAAI;AACrE,aAAO,eAAe,MAAM,KAAK;AAAA,IACnC;AAEA,aAAS,sCAAsC,MAAM,OAAO;AAE1D,UAAI,UAAU,OAAW,SAAQ;AACjC,cAAQ,cAAc,MAAM,KAAK,EAAE;AACnC,YAAM,EAAE,QAAQ,gBAAgB,OAAO,yBAAyB,IAAI,cAAc,MAAM,KAAK;AAC7F,cAAQ,2BAA2B;AACnC,cAAQ,cAAc,MAAM,KAAK,EAAE;AACnC,YAAM,EAAE,KAAK,OAAO,cAAc,IAAI,YAAY,MAAM,KAAK;AAC7D,cAAQ,KAAK;AAAA,QAEX,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QAET,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QAET,KAAK,yBAAyB;AAC5B,kBAAQ;AACR,kBAAQ,cAAc,MAAM,KAAK,EAAE;AACnC,cAAI,KAAK,OAAO,MAAM,IAAM,OAAM,UAAU,gBAAgB,IAAI;AAChE,kBAAQ,eAAe,MAAM,KAAK,EAAE;AACpC,kBAAQ,cAAc,MAAM,KAAK,EAAE;AACnC,gBAAM,EAAE,KAAK,QAAQ,IAAI,YAAY,MAAM,KAAK;AAChD,kBAAQ,SAAS;AAAA,YAEf,KAAK;AACH,qBAAO;AAAA,YACT,KAAK;AACH,qBAAO;AAAA,YACT,KAAK;AACH,qBAAO;AAAA,YACT,KAAK;AACH,qBAAO;AAAA,YACT,KAAK;AACH,qBAAO;AAAA,UACX;AACA,gBAAM,UAAU,sBAAsB,SAAS,IAAI;AAAA,QACrD;AAAA,QAEA,KAAK;AAAA,QACL,KAAK;AACH,iBAAO;AAAA,QAET,KAAK;AAAA,QACL,KAAK;AACH,gBAAM,UAAU,0EAA0E;AAAA,MAC9F;AACA,YAAM,UAAU,iBAAiB,KAAK,IAAI;AAAA,IAC5C;AAEA,WAAO,UAAU,EAAE,sCAAsC;AAAA;AAAA;;;ACzHzD;AAAA;AAAA;AACA,QAAM,SAAS;AACf,QAAM,EAAE,sCAAsC,IAAI;AAElD,aAAS,aAAa,YAAY,QAAQ;AACxC,YAAM,aAAa,CAAC,eAAe;AACnC,UAAI,OAAQ,YAAW,QAAQ,oBAAoB;AAEnD,YAAM,YAAY,WAAW,KAAK,CAAC,cAAc,WAAW,SAAS,SAAS,CAAC;AAE/E,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,6BAA6B,WAAW,KAAK,OAAO,IAAI,gBAAgB;AAAA,MAC1F;AAEA,UAAI,cAAc,wBAAwB,OAAO,OAAO,uBAAuB,YAAY;AAEzF,cAAM,IAAI,MAAM,2DAA2D;AAAA,MAC7E;AAEA,YAAM,cAAc,OAAO,YAAY,EAAE,EAAE,SAAS,QAAQ;AAC5D,YAAM,YAAY,cAAc,uBAAuB,2BAA2B,SAAS,MAAM;AAEjG,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,UAAU,YAAY,aAAa;AAAA,QACnC,SAAS;AAAA,MACX;AAAA,IACF;AAEA,mBAAe,gBAAgB,SAAS,UAAU,YAAY,QAAQ;AACpE,UAAI,QAAQ,YAAY,uBAAuB;AAC7C,cAAM,IAAI,MAAM,gDAAgD;AAAA,MAClE;AACA,UAAI,OAAO,aAAa,UAAU;AAChC,cAAM,IAAI,MAAM,oEAAoE;AAAA,MACtF;AACA,UAAI,aAAa,IAAI;AACnB,cAAM,IAAI,MAAM,8EAA8E;AAAA,MAChG;AACA,UAAI,OAAO,eAAe,UAAU;AAClC,cAAM,IAAI,MAAM,+DAA+D;AAAA,MACjF;AAEA,YAAM,KAAK,wBAAwB,UAAU;AAE7C,UAAI,CAAC,GAAG,MAAM,WAAW,QAAQ,WAAW,GAAG;AAC7C,cAAM,IAAI,MAAM,iFAAiF;AAAA,MACnG,WAAW,GAAG,MAAM,WAAW,QAAQ,YAAY,QAAQ;AACzD,cAAM,IAAI,MAAM,6DAA6D;AAAA,MAC/E;AAEA,YAAM,yBAAyB,WAAW,QAAQ;AAClD,YAAM,qBAAqB,OAAO,GAAG,QAAQ,QAAQ,GAAG,OAAO,QAAQ,GAAG;AAG1E,UAAI,iBAAiB,SAAS,SAAS;AAGvC,UAAI,QAAQ,cAAc,sBAAsB;AAC9C,cAAM,WAAW,OAAO,mBAAmB,EAAE;AAC7C,YAAI,WAAW,sCAAsC,QAAQ;AAC7D,YAAI,aAAa,SAAS,aAAa,QAAS,YAAW;AAC3D,cAAM,WAAW,MAAM,OAAO,WAAW,UAAU,QAAQ;AAC3D,cAAM,cAAc,OAAO,OAAO,CAAC,OAAO,KAAK,0BAA0B,GAAG,OAAO,KAAK,QAAQ,CAAC,CAAC;AAClG,yBAAiB,YAAY,SAAS,QAAQ;AAAA,MAChD;AAEA,YAAM,iCAAiC,OAAO,iBAAiB,QAAQ,GAAG;AAC1E,YAAM,cAAc,yBAAyB,MAAM,qBAAqB,MAAM;AAE9E,YAAM,YAAY,OAAO,KAAK,GAAG,MAAM,QAAQ;AAC/C,YAAM,iBAAiB,MAAM,OAAO,UAAU,UAAU,WAAW,GAAG,SAAS;AAC/E,YAAM,YAAY,MAAM,OAAO,WAAW,gBAAgB,YAAY;AACtE,YAAM,YAAY,MAAM,OAAO,OAAO,SAAS;AAC/C,YAAM,kBAAkB,MAAM,OAAO,WAAW,WAAW,WAAW;AACtE,YAAM,cAAc,WAAW,OAAO,KAAK,SAAS,GAAG,OAAO,KAAK,eAAe,CAAC,EAAE,SAAS,QAAQ;AACtG,YAAM,YAAY,MAAM,OAAO,WAAW,gBAAgB,YAAY;AACtE,YAAM,uBAAuB,MAAM,OAAO,WAAW,WAAW,WAAW;AAE3E,cAAQ,UAAU;AAClB,cAAQ,kBAAkB,OAAO,KAAK,oBAAoB,EAAE,SAAS,QAAQ;AAC7E,cAAQ,WAAW,iCAAiC,QAAQ;AAAA,IAC9D;AAEA,aAAS,gBAAgB,SAAS,YAAY;AAC5C,UAAI,QAAQ,YAAY,gBAAgB;AACtC,cAAM,IAAI,MAAM,yCAAyC;AAAA,MAC3D;AACA,UAAI,OAAO,eAAe,UAAU;AAClC,cAAM,IAAI,MAAM,+DAA+D;AAAA,MACjF;AAEA,YAAM,EAAE,gBAAgB,IAAI,wBAAwB,UAAU;AAE9D,UAAI,oBAAoB,QAAQ,iBAAiB;AAC/C,cAAM,IAAI,MAAM,mEAAmE;AAAA,MACrF;AAAA,IACF;AAQA,aAAS,iBAAiB,MAAM;AAC9B,UAAI,OAAO,SAAS,UAAU;AAC5B,cAAM,IAAI,UAAU,6BAA6B;AAAA,MACnD;AACA,aAAO,KACJ,MAAM,EAAE,EACR,IAAI,CAAC,GAAG,MAAM,KAAK,WAAW,CAAC,CAAC,EAChC,MAAM,CAAC,MAAO,KAAK,MAAQ,KAAK,MAAU,KAAK,MAAQ,KAAK,GAAK;AAAA,IACtE;AAaA,aAAS,SAAS,MAAM;AACtB,aAAO,mEAAmE,KAAK,IAAI;AAAA,IACrF;AAEA,aAAS,oBAAoB,MAAM;AACjC,UAAI,OAAO,SAAS,UAAU;AAC5B,cAAM,IAAI,UAAU,6CAA6C;AAAA,MACnE;AAEA,aAAO,IAAI;AAAA,QACT,KAAK,MAAM,GAAG,EAAE,IAAI,CAAC,cAAc;AACjC,cAAI,CAAC,MAAM,KAAK,SAAS,GAAG;AAC1B,kBAAM,IAAI,MAAM,oCAAoC;AAAA,UACtD;AACA,gBAAM,OAAO,UAAU,CAAC;AACxB,gBAAM,QAAQ,UAAU,UAAU,CAAC;AACnC,iBAAO,CAAC,MAAM,KAAK;AAAA,QACrB,CAAC;AAAA,MACH;AAAA,IACF;AAEA,aAAS,wBAAwB,MAAM;AACrC,YAAM,YAAY,oBAAoB,IAAI;AAE1C,YAAM,QAAQ,UAAU,IAAI,GAAG;AAC/B,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,iDAAiD;AAAA,MACnE,WAAW,CAAC,iBAAiB,KAAK,GAAG;AACnC,cAAM,IAAI,MAAM,gFAAgF;AAAA,MAClG;AACA,YAAM,OAAO,UAAU,IAAI,GAAG;AAC9B,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,gDAAgD;AAAA,MAClE,WAAW,CAAC,SAAS,IAAI,GAAG;AAC1B,cAAM,IAAI,MAAM,uDAAuD;AAAA,MACzE;AACA,YAAM,gBAAgB,UAAU,IAAI,GAAG;AACvC,UAAI,CAAC,eAAe;AAClB,cAAM,IAAI,MAAM,qDAAqD;AAAA,MACvE,WAAW,CAAC,gBAAgB,KAAK,aAAa,GAAG;AAC/C,cAAM,IAAI,MAAM,2DAA2D;AAAA,MAC7E;AACA,YAAM,YAAY,SAAS,eAAe,EAAE;AAE5C,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAEA,aAAS,wBAAwB,YAAY;AAC3C,YAAM,YAAY,oBAAoB,UAAU;AAChD,YAAM,kBAAkB,UAAU,IAAI,GAAG;AACzC,UAAI,CAAC,iBAAiB;AACpB,cAAM,IAAI,MAAM,+DAA+D;AAAA,MACjF,WAAW,CAAC,SAAS,eAAe,GAAG;AACrC,cAAM,IAAI,MAAM,mEAAmE;AAAA,MACrF;AACA,aAAO;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAEA,aAAS,WAAW,GAAG,GAAG;AACxB,UAAI,CAAC,OAAO,SAAS,CAAC,GAAG;AACvB,cAAM,IAAI,UAAU,iCAAiC;AAAA,MACvD;AACA,UAAI,CAAC,OAAO,SAAS,CAAC,GAAG;AACvB,cAAM,IAAI,UAAU,kCAAkC;AAAA,MACxD;AACA,UAAI,EAAE,WAAW,EAAE,QAAQ;AACzB,cAAM,IAAI,MAAM,2BAA2B;AAAA,MAC7C;AACA,UAAI,EAAE,WAAW,GAAG;AAClB,cAAM,IAAI,MAAM,yBAAyB;AAAA,MAC3C;AACA,aAAO,OAAO,KAAK,EAAE,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAAA,IACjD;AAEA,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA;;;ACnNA;AAAA;AAAA;AAEA,QAAMC,SAAQ;AAEd,aAASC,eAAc,WAAW;AAChC,WAAK,SAAS,aAAaD;AAC3B,WAAK,OAAO,CAAC;AACb,WAAK,SAAS,CAAC;AAAA,IACjB;AAEA,IAAAC,eAAc,UAAU,eAAe,SAAU,QAAQ;AACvD,cAAQ,QAAQ;AAAA,QACd,KAAK;AACH,iBAAO,KAAK;AAAA,QACd,KAAK;AACH,iBAAO,KAAK;AAAA,QACd;AACE,iBAAO,CAAC;AAAA,MACZ;AAAA,IACF;AAEA,IAAAA,eAAc,UAAU,gBAAgB,SAAU,KAAK,QAAQ,SAAS;AACtE,UAAI,OAAO,WAAW,YAAY;AAChC,kBAAU;AACV,iBAAS;AAAA,MACX;AACA,WAAK,aAAa,MAAM,EAAE,GAAG,IAAI;AAAA,IACnC;AAEA,IAAAA,eAAc,UAAU,gBAAgB,SAAU,KAAK,QAAQ;AAC7D,eAAS,UAAU;AACnB,aAAO,KAAK,aAAa,MAAM,EAAE,GAAG,KAAK,KAAK,OAAO,cAAc,KAAK,MAAM;AAAA,IAChF;AAEA,WAAO,UAAUA;AAAA;AAAA;;;AClCjB;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,oFAAoF,GAAG,mIAAmI;AAAA,QACzO;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF;AAAA;AAAA;AAOA,aAAS,MAAM,KAAK,UAAU,CAAC,GAAG;AAEhC,UAAI,IAAI,OAAO,CAAC,MAAM,KAAK;AACzB,cAAMC,UAAS,IAAI,MAAM,GAAG;AAC5B,eAAO,EAAE,MAAMA,QAAO,CAAC,GAAG,UAAUA,QAAO,CAAC,EAAE;AAAA,MAChD;AAIA,YAAM,SAAS,CAAC;AAChB,UAAI;AACJ,UAAI,YAAY;AAChB,UAAI,mCAAmC,KAAK,GAAG,GAAG;AAEhD,cAAM,UAAU,GAAG,EAAE,QAAQ,cAAc,KAAK;AAAA,MAClD;AAEA,UAAI;AACF,YAAI;AACF,mBAAS,IAAI,IAAI,KAAK,iBAAiB;AAAA,QACzC,SAAS,GAAG;AAEV,mBAAS,IAAI,IAAI,IAAI,QAAQ,MAAM,eAAe,GAAG,iBAAiB;AACtE,sBAAY;AAAA,QACd;AAAA,MACF,SAAS,KAAK;AAEZ,YAAI,UAAU,IAAI,QAAQ;AAAA,MAC5B;AAGA,iBAAW,SAAS,OAAO,aAAa,QAAQ,GAAG;AACjD,eAAO,MAAM,CAAC,CAAC,IAAI,MAAM,CAAC;AAAA,MAC5B;AAEA,aAAO,OAAO,OAAO,QAAQ,mBAAmB,OAAO,QAAQ;AAC/D,aAAO,WAAW,OAAO,YAAY,mBAAmB,OAAO,QAAQ;AAEvE,UAAI,OAAO,YAAY,WAAW;AAChC,eAAO,OAAO,UAAU,OAAO,QAAQ;AACvC,eAAO,WAAW,OAAO,aAAa,IAAI,IAAI;AAC9C,eAAO,kBAAkB,OAAO,aAAa,IAAI,UAAU;AAC3D,eAAO;AAAA,MACT;AACA,YAAM,WAAW,YAAY,KAAK,OAAO;AACzC,UAAI,CAAC,OAAO,MAAM;AAEhB,eAAO,OAAO,mBAAmB,QAAQ;AAAA,MAC3C,WAAW,YAAY,QAAQ,KAAK,QAAQ,GAAG;AAE7C,eAAO,WAAW,WAAW,OAAO;AAAA,MACtC;AACA,UAAI,CAAC,OAAO,MAAM;AAEhB,eAAO,OAAO,OAAO;AAAA,MACvB;AAEA,YAAM,WAAW,OAAO,SAAS,MAAM,CAAC,KAAK;AAC7C,aAAO,WAAW,WAAW,UAAU,QAAQ,IAAI;AAEnD,UAAI,OAAO,QAAQ,UAAU,OAAO,QAAQ,KAAK;AAC/C,eAAO,MAAM;AAAA,MACf;AAEA,UAAI,OAAO,QAAQ,KAAK;AACtB,eAAO,MAAM;AAAA,MACf;AAEA,UAAI,OAAO,WAAW,OAAO,UAAU,OAAO,eAAe,OAAO,SAAS;AAC3E,eAAO,MAAM,CAAC;AAAA,MAChB;AAGA,YAAM,KAAK,OAAO,WAAW,OAAO,UAAU,OAAO,cAAc,eAAgB;AAEnF,UAAI,OAAO,SAAS;AAClB,eAAO,IAAI,OAAO,GAAG,aAAa,OAAO,OAAO,EAAE,SAAS;AAAA,MAC7D;AAEA,UAAI,OAAO,QAAQ;AACjB,eAAO,IAAI,MAAM,GAAG,aAAa,OAAO,MAAM,EAAE,SAAS;AAAA,MAC3D;AAEA,UAAI,OAAO,aAAa;AACtB,eAAO,IAAI,KAAK,GAAG,aAAa,OAAO,WAAW,EAAE,SAAS;AAAA,MAC/D;AAEA,UAAI,QAAQ,kBAAkB,OAAO,gBAAgB;AACnD,cAAM,IAAI,MAAM,8EAA8E;AAAA,MAChG;AAEA,UAAI,OAAO,mBAAmB,UAAU,QAAQ,gBAAgB;AAC9D,gBAAQ,OAAO,SAAS;AAAA,UACtB,KAAK,WAAW;AACd,mBAAO,MAAM;AACb;AAAA,UACF;AAAA,UACA,KAAK,UAAU;AACb,mBAAO,IAAI,qBAAqB;AAChC;AAAA,UACF;AAAA,UACA,KAAK,WAAW;AACd,gBAAI,OAAO,aAAa;AAEtB,qBAAO,IAAI,sBAAsB,WAAY;AAAA,cAAC;AAAA,YAChD,OAAO;AACL,qBAAO,IAAI,qBAAqB;AAAA,YAClC;AACA;AAAA,UACF;AAAA,UACA,KAAK,aAAa;AAChB,gBAAI,CAAC,OAAO,IAAI,IAAI;AAClB,oBAAM,IAAI;AAAA,gBACR;AAAA,cACF;AAAA,YACF;AACA,mBAAO,IAAI,sBAAsB,WAAY;AAAA,YAAC;AAC9C;AAAA,UACF;AAAA,UACA,KAAK,eAAe;AAClB;AAAA,UACF;AAAA,QACF;AAAA,MACF,OAAO;AACL,gBAAQ,OAAO,SAAS;AAAA,UACtB,KAAK,WAAW;AACd,mBAAO,MAAM;AACb;AAAA,UACF;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK,eAAe;AAClB;AAAA,UACF;AAAA,UACA,KAAK,aAAa;AAChB,mBAAO,IAAI,qBAAqB;AAChC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAGA,aAAS,oBAAoB,WAAW;AACtC,YAAM,oBAAoB,OAAO,QAAQ,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,KAAK,KAAK,MAAM;AAG9E,YAAI,UAAU,UAAa,UAAU,MAAM;AACzC,YAAE,GAAG,IAAI;AAAA,QACX;AAEA,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AAEL,aAAO;AAAA,IACT;AAGA,aAAS,eAAe,QAAQ;AAC9B,YAAM,aAAa,OAAO,QAAQ,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,KAAK,KAAK,MAAM;AACpE,YAAI,QAAQ,OAAO;AACjB,gBAAM,YAAY;AAElB,cAAI,OAAO,cAAc,WAAW;AAClC,cAAE,GAAG,IAAI;AAAA,UACX;AAEA,cAAI,OAAO,cAAc,UAAU;AACjC,cAAE,GAAG,IAAI,oBAAoB,SAAS;AAAA,UACxC;AAAA,QACF,WAAW,UAAU,UAAa,UAAU,MAAM;AAChD,cAAI,QAAQ,QAAQ;AAGlB,gBAAI,UAAU,IAAI;AAChB,oBAAM,IAAI,SAAS,OAAO,EAAE;AAC5B,kBAAI,MAAM,CAAC,GAAG;AACZ,sBAAM,IAAI,MAAM,WAAW,GAAG,KAAK,KAAK,EAAE;AAAA,cAC5C;AAEA,gBAAE,GAAG,IAAI;AAAA,YACX;AAAA,UACF,OAAO;AACL,cAAE,GAAG,IAAI;AAAA,UACX;AAAA,QACF;AAEA,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AAEL,aAAO;AAAA,IACT;AAGA,aAAS,sBAAsB,KAAK;AAClC,aAAO,eAAe,MAAM,GAAG,CAAC;AAAA,IAClC;AAEA,WAAO,UAAU;AAEjB,UAAM,QAAQ;AACd,UAAM,iBAAiB;AACvB,UAAM,wBAAwB;AAAA;AAAA;;;ACpN9B;AAAA;AAAA;AAEA,QAAM,MAAM;AAEZ,QAAMC,YAAW;AAEjB,QAAM,QAAQ,+BAAgC;AAE9C,QAAM,MAAM,SAAU,KAAK,QAAQ,QAAQ;AACzC,UAAI,WAAW,QAAW;AACxB,iBAAS,QAAQ,IAAI,OAAO,IAAI,YAAY,CAAC;AAAA,MAC/C,WAAW,WAAW,OAAO;AAAA,MAE7B,OAAO;AACL,iBAAS,QAAQ,IAAI,MAAM;AAAA,MAC7B;AAEA,aAAO,OAAO,GAAG,KAAK,UAAUA,UAAS,GAAG;AAAA,IAC9C;AAEA,QAAM,+BAA+B,WAAY;AAC/C,cAAQ,QAAQ,IAAI,WAAW;AAAA,QAC7B,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO,EAAE,oBAAoB,MAAM;AAAA,MACvC;AACA,aAAOA,UAAS;AAAA,IAClB;AAGA,QAAM,kBAAkB,SAAU,OAAO;AACvC,aAAO,OAAO,KAAK,OAAO,QAAQ,OAAO,MAAM,EAAE,QAAQ,MAAM,KAAK,IAAI;AAAA,IAC1E;AAEA,QAAM,MAAM,SAAU,QAAQ,QAAQ,WAAW;AAC/C,YAAM,QAAQ,OAAO,SAAS;AAC9B,UAAI,UAAU,UAAa,UAAU,MAAM;AACzC,eAAO,KAAK,YAAY,MAAM,gBAAgB,KAAK,CAAC;AAAA,MACtD;AAAA,IACF;AAEA,QAAM,uBAAN,MAA2B;AAAA,MACzB,YAAY,QAAQ;AAElB,iBAAS,OAAO,WAAW,WAAW,MAAM,MAAM,IAAI,UAAU,CAAC;AAIjE,YAAI,OAAO,kBAAkB;AAC3B,mBAAS,OAAO,OAAO,CAAC,GAAG,QAAQ,MAAM,OAAO,gBAAgB,CAAC;AAAA,QACnE;AAEA,aAAK,OAAO,IAAI,QAAQ,MAAM;AAC9B,aAAK,WAAW,IAAI,YAAY,MAAM;AAEtC,YAAI,KAAK,aAAa,QAAW;AAC/B,eAAK,WAAW,KAAK;AAAA,QACvB;AAEA,aAAK,OAAO,SAAS,IAAI,QAAQ,MAAM,GAAG,EAAE;AAC5C,aAAK,OAAO,IAAI,QAAQ,MAAM;AAI9B,eAAO,eAAe,MAAM,YAAY;AAAA,UACtC,cAAc;AAAA,UACd,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,OAAO,IAAI,YAAY,MAAM;AAAA,QAC/B,CAAC;AAED,aAAK,SAAS,IAAI,UAAU,MAAM;AAClC,aAAK,UAAU,IAAI,WAAW,MAAM;AAEpC,aAAK,MAAM,OAAO,OAAO,QAAQ,cAAc,6BAA6B,IAAI,OAAO;AAEvF,YAAI,OAAO,KAAK,QAAQ,UAAU;AAChC,cAAI,KAAK,QAAQ,QAAQ;AACvB,iBAAK,MAAM;AAAA,UACb;AAAA,QACF;AAEA,YAAI,KAAK,QAAQ,aAAa;AAC5B,eAAK,MAAM,EAAE,oBAAoB,MAAM;AAAA,QACzC;AACA,YAAI,KAAK,OAAO,KAAK,IAAI,KAAK;AAC5B,iBAAO,eAAe,KAAK,KAAK,OAAO;AAAA,YACrC,YAAY;AAAA,UACd,CAAC;AAAA,QACH;AAEA,aAAK,kBAAkB,IAAI,mBAAmB,MAAM;AACpD,aAAK,cAAc,IAAI,eAAe,MAAM;AAE5C,aAAK,iBAAiB,EAAE,KAAK,QAAQ,IAAI,QAAQ,GAAG;AAEpD,aAAK,mBAAmB,IAAI,oBAAoB,QAAQ,WAAW;AACnE,aAAK,4BAA4B,IAAI,6BAA6B,QAAQ,KAAK;AAC/E,aAAK,oBAAoB,IAAI,qBAAqB,QAAQ,KAAK;AAC/D,aAAK,eAAe,IAAI,gBAAgB,QAAQ,KAAK;AACrD,aAAK,sCAAsC,IAAI,uCAAuC,QAAQ,KAAK;AACnG,aAAK,gBAAgB,IAAI,iBAAiB,QAAQ,KAAK;AAEvD,YAAI,OAAO,4BAA4B,QAAW;AAChD,eAAK,kBAAkB,QAAQ,IAAI,qBAAqB;AAAA,QAC1D,OAAO;AACL,eAAK,kBAAkB,KAAK,MAAM,OAAO,0BAA0B,GAAI;AAAA,QACzE;AAEA,YAAI,OAAO,cAAc,OAAO;AAC9B,eAAK,aAAa;AAAA,QACpB,WAAW,OAAO,cAAc,MAAM;AACpC,eAAK,aAAa;AAAA,QACpB;AAEA,YAAI,OAAO,OAAO,gCAAgC,UAAU;AAC1D,eAAK,kBAAkB,KAAK,MAAM,OAAO,8BAA8B,GAAI;AAAA,QAC7E;AAAA,MACF;AAAA,MAEA,yBAAyB,IAAI;AAC3B,cAAM,SAAS,CAAC;AAChB,YAAI,QAAQ,MAAM,MAAM;AACxB,YAAI,QAAQ,MAAM,UAAU;AAC5B,YAAI,QAAQ,MAAM,MAAM;AACxB,YAAI,QAAQ,MAAM,kBAAkB;AACpC,YAAI,QAAQ,MAAM,2BAA2B;AAC7C,YAAI,QAAQ,MAAM,iBAAiB;AACnC,YAAI,QAAQ,MAAM,SAAS;AAE3B,cAAM,MAAM,OAAO,KAAK,QAAQ,WAAW,KAAK,MAAM,KAAK,MAAM,EAAE,SAAS,KAAK,IAAI,IAAI,CAAC;AAC1F,YAAI,QAAQ,KAAK,SAAS;AAC1B,YAAI,QAAQ,KAAK,OAAO;AACxB,YAAI,QAAQ,KAAK,QAAQ;AACzB,YAAI,QAAQ,KAAK,SAAS;AAC1B,YAAI,QAAQ,KAAK,aAAa;AAE9B,YAAI,KAAK,UAAU;AACjB,iBAAO,KAAK,YAAY,gBAAgB,KAAK,QAAQ,CAAC;AAAA,QACxD;AACA,YAAI,KAAK,aAAa;AACpB,iBAAO,KAAK,iBAAiB,gBAAgB,KAAK,WAAW,CAAC;AAAA,QAChE;AACA,YAAI,KAAK,MAAM;AACb,iBAAO,KAAK,UAAU,gBAAgB,KAAK,IAAI,CAAC;AAAA,QAClD;AACA,YAAI,KAAK,gBAAgB;AACvB,iBAAO,GAAG,MAAM,OAAO,KAAK,GAAG,CAAC;AAAA,QAClC;AACA,YAAI,KAAK,iBAAiB;AACxB,iBAAO,KAAK,qBAAqB,gBAAgB,KAAK,eAAe,CAAC;AAAA,QACxE;AACA,YAAI,OAAO,KAAK,MAAM,SAAU,KAAK,SAAS;AAC5C,cAAI,IAAK,QAAO,GAAG,KAAK,IAAI;AAC5B,iBAAO,KAAK,cAAc,gBAAgB,OAAO,CAAC;AAClD,iBAAO,GAAG,MAAM,OAAO,KAAK,GAAG,CAAC;AAAA,QAClC,CAAC;AAAA,MACH;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACtKjB;AAAA;AAAA;AAEA,QAAMC,SAAQ;AAEd,QAAM,cAAc;AAKpB,QAAMC,UAAN,MAAa;AAAA,MACX,YAAY,SAASD,QAAO;AAC1B,aAAK,UAAU;AACf,aAAK,WAAW;AAChB,aAAK,MAAM;AACX,aAAK,OAAO,CAAC;AACb,aAAK,SAAS,CAAC;AACf,aAAK,WAAW;AAChB,aAAK,SAASA;AACd,aAAK,UAAU;AACf,aAAK,aAAa,YAAY;AAC9B,YAAI,KAAK,YAAY;AACnB,eAAK,WAAW,KAAK;AAAA,QACvB;AACA,aAAK,6BAA6B;AAAA,MACpC;AAAA;AAAA,MAGA,mBAAmB,KAAK;AACtB,YAAI;AACJ,YAAI,IAAI,MAAM;AAEZ,kBAAQ,YAAY,KAAK,IAAI,IAAI;AAAA,QACnC,OAAO;AAEL,kBAAQ,YAAY,KAAK,IAAI,OAAO;AAAA,QACtC;AACA,YAAI,OAAO;AACT,eAAK,UAAU,MAAM,CAAC;AACtB,cAAI,MAAM,CAAC,GAAG;AAEZ,iBAAK,MAAM,SAAS,MAAM,CAAC,GAAG,EAAE;AAChC,iBAAK,WAAW,SAAS,MAAM,CAAC,GAAG,EAAE;AAAA,UACvC,WAAW,MAAM,CAAC,GAAG;AAEnB,iBAAK,WAAW,SAAS,MAAM,CAAC,GAAG,EAAE;AAAA,UACvC;AAAA,QACF;AAAA,MACF;AAAA,MAEA,iBAAiB,SAAS;AACxB,cAAM,MAAM,IAAI,MAAM,QAAQ,MAAM;AACpC,iBAAS,IAAI,GAAG,MAAM,QAAQ,QAAQ,IAAI,KAAK,KAAK;AAClD,gBAAM,WAAW,QAAQ,CAAC;AAC1B,cAAI,aAAa,MAAM;AACrB,gBAAI,CAAC,IAAI,KAAK,SAAS,CAAC,EAAE,QAAQ;AAAA,UACpC,OAAO;AACL,gBAAI,CAAC,IAAI;AAAA,UACX;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,MAEA,SAAS,SAAS;AAChB,cAAM,MAAM,EAAE,GAAG,KAAK,2BAA2B;AACjD,iBAAS,IAAI,GAAG,MAAM,QAAQ,QAAQ,IAAI,KAAK,KAAK;AAClD,gBAAM,WAAW,QAAQ,CAAC;AAC1B,gBAAM,QAAQ,KAAK,OAAO,CAAC,EAAE;AAC7B,cAAI,aAAa,MAAM;AACrB,gBAAI,KAAK,IAAI,KAAK,SAAS,CAAC,EAAE,QAAQ;AAAA,UACxC,OAAO;AACL,gBAAI,KAAK,IAAI;AAAA,UACf;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,MAEA,OAAO,KAAK;AACV,aAAK,KAAK,KAAK,GAAG;AAAA,MACpB;AAAA,MAEA,UAAU,mBAAmB;AAK3B,aAAK,SAAS;AACd,YAAI,KAAK,OAAO,QAAQ;AACtB,eAAK,WAAW,IAAI,MAAM,kBAAkB,MAAM;AAAA,QACpD;AAEA,cAAM,MAAM,CAAC;AAEb,iBAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,KAAK;AACjD,gBAAM,OAAO,kBAAkB,CAAC;AAChC,cAAI,KAAK,IAAI,IAAI;AAEjB,cAAI,KAAK,QAAQ;AACf,iBAAK,SAAS,CAAC,IAAI,KAAK,OAAO,cAAc,KAAK,YAAY,KAAK,UAAU,MAAM;AAAA,UACrF,OAAO;AACL,iBAAK,SAAS,CAAC,IAAIA,OAAM,cAAc,KAAK,YAAY,KAAK,UAAU,MAAM;AAAA,UAC/E;AAAA,QACF;AAEA,aAAK,6BAA6B,EAAE,GAAG,IAAI;AAAA,MAC7C;AAAA,IACF;AAEA,WAAO,UAAUC;AAAA;AAAA;;;AC3GjB;AAAA;AAAA;AAEA,QAAM,EAAE,aAAa,IAAI;AAEzB,QAAMC,UAAS;AACf,QAAM,QAAQ;AAEd,QAAMC,SAAN,cAAoB,aAAa;AAAA,MAC/B,YAAY,QAAQ,QAAQ,UAAU;AACpC,cAAM;AAEN,iBAAS,MAAM,qBAAqB,QAAQ,QAAQ,QAAQ;AAE5D,aAAK,OAAO,OAAO;AACnB,aAAK,SAAS,OAAO;AACrB,aAAK,OAAO,OAAO;AACnB,aAAK,QAAQ,OAAO;AACpB,aAAK,OAAO,OAAO;AACnB,aAAK,YAAY,OAAO;AACxB,aAAK,SAAS,OAAO;AAErB,aAAK,SAAS,OAAO,UAAU;AAC/B,aAAK,WAAW,OAAO;AACvB,aAAK,WAAW,OAAO;AACvB,YAAI,QAAQ,UAAU,OAAO,UAAU;AACrC,eAAK,WAAW,QAAQ,OAAO,KAAK,OAAO,QAAQ;AAAA,QACrD;AACA,aAAK,UAAU,IAAID,QAAO,KAAK,UAAU,KAAK,KAAK;AAGnD,aAAK,WAAW,KAAK;AACrB,aAAK,sBAAsB;AAAA,MAC7B;AAAA,MAEA,sBAAsB;AACpB,YAAI,KAAK,cAAc,YAAY;AACjC,iBAAO;AAAA,QACT;AAGA,YAAI,KAAK,MAAM;AACb,iBAAO;AAAA,QACT;AAGA,YAAI,KAAK,MAAM;AACb,iBAAO;AAAA,QACT;AAEA,YAAI,CAAC,KAAK,MAAM;AACd,iBAAO;AAAA,QACT;AAEA,YAAI,CAAC,KAAK,QAAQ;AAChB,iBAAO;AAAA,QACT;AACA,eAAO,KAAK,OAAO,SAAS;AAAA,MAC9B;AAAA,MAEA,oBAAoB;AAIlB,YAAI,KAAK,QAAQ,SAAS;AACxB,cAAI,CAAC,MAAM,QAAQ,KAAK,QAAQ,GAAG;AACjC,iBAAK,WAAW,CAAC,KAAK,OAAO;AAAA,UAC/B;AACA,eAAK,UAAU,IAAIA,QAAO,KAAK,UAAU,KAAK,QAAQ,MAAM;AAC5D,eAAK,SAAS,KAAK,KAAK,OAAO;AAAA,QACjC;AAAA,MACF;AAAA;AAAA;AAAA;AAAA,MAKA,qBAAqB,KAAK;AACxB,aAAK,kBAAkB;AACvB,aAAK,QAAQ,UAAU,IAAI,MAAM;AACjC,aAAK,kBAAkB,KAAK,YAAY,CAAC,KAAK,UAAU,KAAK,EAAE;AAAA,MACjE;AAAA,MAEA,cAAc,KAAK;AACjB,YAAI;AAEJ,YAAI,KAAK,qBAAqB;AAC5B;AAAA,QACF;AAEA,YAAI;AACF,gBAAM,KAAK,QAAQ,SAAS,IAAI,MAAM;AAAA,QACxC,SAAS,KAAK;AACZ,eAAK,sBAAsB;AAC3B;AAAA,QACF;AAEA,aAAK,KAAK,OAAO,KAAK,KAAK,OAAO;AAClC,YAAI,KAAK,iBAAiB;AACxB,eAAK,QAAQ,OAAO,GAAG;AAAA,QACzB;AAAA,MACF;AAAA,MAEA,sBAAsB,KAAK,YAAY;AACrC,aAAK,kBAAkB;AACvB,aAAK,QAAQ,mBAAmB,GAAG;AAGnC,YAAI,KAAK,MAAM;AACb,qBAAW,KAAK;AAAA,QAClB;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,iBAAiB,YAAY;AAC3B,YAAI,KAAK,MAAM;AACb,qBAAW,KAAK;AAAA,QAClB;AAAA,MACF;AAAA,MAEA,YAAY,KAAK,YAAY;AAE3B,YAAI,KAAK,qBAAqB;AAC5B,gBAAM,KAAK;AACX,eAAK,sBAAsB;AAAA,QAC7B;AAGA,YAAI,KAAK,UAAU;AACjB,iBAAO,KAAK,SAAS,GAAG;AAAA,QAC1B;AACA,aAAK,KAAK,SAAS,GAAG;AAAA,MACxB;AAAA,MAEA,oBAAoB,KAAK;AACvB,YAAI,KAAK,qBAAqB;AAC5B,iBAAO,KAAK,YAAY,KAAK,qBAAqB,GAAG;AAAA,QACvD;AACA,YAAI,KAAK,UAAU;AACjB,cAAI;AACF,iBAAK,SAAS,MAAM,KAAK,QAAQ;AAAA,UACnC,SAAS,KAAK;AACZ,oBAAQ,SAAS,MAAM;AACrB,oBAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF;AACA,aAAK,KAAK,OAAO,KAAK,QAAQ;AAAA,MAChC;AAAA,MAEA,OAAO,YAAY;AACjB,YAAI,OAAO,KAAK,SAAS,YAAY,OAAO,KAAK,SAAS,UAAU;AAClE,iBAAO,IAAI,MAAM,4EAA4E;AAAA,QAC/F;AACA,cAAM,WAAW,WAAW,iBAAiB,KAAK,IAAI;AACtD,YAAI,KAAK,QAAQ,YAAY,KAAK,SAAS,UAAU;AACnD,iBAAO,IAAI,MAAM,yCAAyC,KAAK,IAAI,sCAAsC;AAAA,QAC3G;AACA,YAAI,KAAK,UAAU,CAAC,MAAM,QAAQ,KAAK,MAAM,GAAG;AAC9C,iBAAO,IAAI,MAAM,+BAA+B;AAAA,QAClD;AACA,YAAI,KAAK,oBAAoB,GAAG;AAQ9B,qBAAW,OAAO,QAAQ,WAAW,OAAO,KAAK;AACjD,cAAI;AACF,iBAAK,QAAQ,UAAU;AAAA,UACzB,UAAE;AAGA,uBAAW,OAAO,UAAU,WAAW,OAAO,OAAO;AAAA,UACvD;AAAA,QACF,OAAO;AACL,qBAAW,MAAM,KAAK,IAAI;AAAA,QAC5B;AACA,eAAO;AAAA,MACT;AAAA,MAEA,cAAc,YAAY;AACxB,eAAO,KAAK,QAAQ,WAAW,iBAAiB,KAAK,IAAI;AAAA,MAC3D;AAAA,MAEA,sBAAsB,YAAY;AAChC,aAAK,SAAS,YAAY,KAAK,IAAI;AAAA,MACrC;AAAA,MAEA,SAAS,YAAY,MAAM;AACzB,mBAAW,QAAQ;AAAA,UACjB,QAAQ,KAAK;AAAA,UACb;AAAA,QACF,CAAC;AAGD,YAAI,CAAC,MAAM;AACT,qBAAW,KAAK;AAAA,QAClB,OAAO;AAEL,qBAAW,MAAM;AAAA,QACnB;AAAA,MACF;AAAA;AAAA,MAGA,QAAQ,YAAY;AAElB,YAAI,CAAC,KAAK,cAAc,UAAU,GAAG;AACnC,qBAAW,MAAM;AAAA,YACf,MAAM,KAAK;AAAA,YACX,MAAM,KAAK;AAAA,YACX,OAAO,KAAK;AAAA,UACd,CAAC;AAAA,QACH;AAKA,YAAI;AACF,qBAAW,KAAK;AAAA,YACd,QAAQ,KAAK;AAAA,YACb,WAAW,KAAK;AAAA,YAChB,QAAQ,KAAK;AAAA,YACb,QAAQ,KAAK;AAAA,YACb,aAAa,MAAM;AAAA,UACrB,CAAC;AAAA,QACH,SAAS,KAAK;AACZ,eAAK,YAAY,KAAK,UAAU;AAChC;AAAA,QACF;AAEA,mBAAW,SAAS;AAAA,UAClB,MAAM;AAAA,UACN,MAAM,KAAK,UAAU;AAAA,QACvB,CAAC;AAED,aAAK,SAAS,YAAY,KAAK,IAAI;AAAA,MACrC;AAAA,MAEA,qBAAqB,YAAY;AAC/B,mBAAW,aAAa,0BAA0B;AAAA,MACpD;AAAA,MAEA,eAAe,KAAK,YAAY;AAAA,MAEhC;AAAA,IACF;AAEA,WAAO,UAAUC;AAAA;AAAA;;;;;;;;ACvNJ,YAAA,gBAAgC;MAC3C,MAAM;MACN,QAAQ;;AAGG,YAAA,eAA+B;MAC1C,MAAM;MACN,QAAQ;;AAGG,YAAA,gBAAgC;MAC3C,MAAM;MACN,QAAQ;;AAGG,YAAA,SAAyB;MACpC,MAAM;MACN,QAAQ;;AAGG,YAAA,kBAAkC;MAC7C,MAAM;MACN,QAAQ;;AAGG,YAAA,mBAAmC;MAC9C,MAAM;MACN,QAAQ;;AAGG,YAAA,aAA6B;MACxC,MAAM;MACN,QAAQ;;AAGG,YAAA,WAA2B;MACtC,MAAM;MACN,QAAQ;;AAuBV,QAAaC,iBAAb,cAAmC,MAAK;MAiBtC,YACE,SACgB,QACA,MAAiB;AAEjC,cAAM,OAAO;AAHG,aAAA,SAAA;AACA,aAAA,OAAA;MAGlB;;AAvBF,YAAA,gBAAAA;AA0BA,QAAa,kBAAb,MAA4B;MAE1B,YACkB,QACA,OAAa;AADb,aAAA,SAAA;AACA,aAAA,QAAA;AAHF,aAAA,OAAO;MAIpB;;AALL,YAAA,kBAAA;AAQA,QAAa,eAAb,MAAyB;MAEvB,YACkB,QACA,MACA,QAChB,aAAmB;AAHH,aAAA,SAAA;AACA,aAAA,OAAA;AACA,aAAA,SAAA;AAGhB,aAAK,cAAc,IAAI,MAAM,WAAW;MAC1C;;AATF,YAAA,eAAA;AAYA,QAAa,QAAb,MAAkB;MAChB,YACkB,MACA,SACA,UACA,YACA,cACA,kBACA,QAAY;AANZ,aAAA,OAAA;AACA,aAAA,UAAA;AACA,aAAA,WAAA;AACA,aAAA,aAAA;AACA,aAAA,eAAA;AACA,aAAA,mBAAA;AACA,aAAA,SAAA;MACf;;AATL,YAAA,QAAA;AAYA,QAAa,wBAAb,MAAkC;MAGhC,YACkB,QACA,YAAkB;AADlB,aAAA,SAAA;AACA,aAAA,aAAA;AAJF,aAAA,OAAoB;AAMlC,aAAK,SAAS,IAAI,MAAM,KAAK,UAAU;MACzC;;AARF,YAAA,wBAAA;AAWA,QAAa,8BAAb,MAAwC;MAGtC,YACkB,QACA,gBAAsB;AADtB,aAAA,SAAA;AACA,aAAA,iBAAA;AAJF,aAAA,OAAoB;AAMlC,aAAK,cAAc,IAAI,MAAM,KAAK,cAAc;MAClD;;AARF,YAAA,8BAAA;AAWA,QAAa,yBAAb,MAAmC;MAEjC,YACkB,QACA,eACA,gBAAsB;AAFtB,aAAA,SAAA;AACA,aAAA,gBAAA;AACA,aAAA,iBAAA;AAJF,aAAA,OAAoB;MAKjC;;AANL,YAAA,yBAAA;AASA,QAAa,4BAAb,MAAsC;MAEpC,YACkB,QACA,MAAY;AADZ,aAAA,SAAA;AACA,aAAA,OAAA;AAHF,aAAA,OAAoB;MAIjC;;AALL,YAAA,4BAAA;AAQA,QAAa,wBAAb,MAAkC;MAEhC,YACkB,QACA,WACA,WAAiB;AAFjB,aAAA,SAAA;AACA,aAAA,YAAA;AACA,aAAA,YAAA;AAJF,aAAA,OAAoB;MAKjC;;AANL,YAAA,wBAAA;AASA,QAAa,8BAAb,MAAwC;MAEtC,YACkB,QACA,WACA,SACA,SAAe;AAHf,aAAA,SAAA;AACA,aAAA,YAAA;AACA,aAAA,UAAA;AACA,aAAA,UAAA;AALF,aAAA,OAAoB;MAMjC;;AAPL,YAAA,8BAAA;AAUA,QAAa,uBAAb,MAAiC;MAE/B,YACkB,QACA,QAAc;AADd,aAAA,SAAA;AACA,aAAA,SAAA;AAHF,aAAA,OAAoB;MAIjC;;AALL,YAAA,uBAAA;AAQA,QAAa,yBAAb,MAAmC;MAEjC,YACkB,QACA,MAAY;AADZ,aAAA,SAAA;AACA,aAAA,OAAA;AAHF,aAAA,OAAoB;MAIjC;;AALL,YAAA,yBAAA;AAQA,QAAa,iBAAb,MAA2B;MAGzB,YACS,QACA,QAAa;AADb,aAAA,SAAA;AACA,aAAA,SAAA;AAHO,aAAA,OAAoB;AAKlC,aAAK,aAAa,OAAO;MAC3B;;AARF,YAAA,iBAAA;AAWA,QAAa,gBAAb,MAA0B;MACxB,YACkB,QACA,SAA2B;AAD3B,aAAA,SAAA;AACA,aAAA,UAAA;AAEF,aAAA,OAAO;MADpB;;AAJL,YAAA,gBAAA;;;;;;;;;;AC7OA,QAAa,SAAb,MAAmB;MAIjB,YAAoB,OAAO,KAAG;AAAV,aAAA,OAAA;AAFZ,aAAA,SAAiB;AACjB,aAAA,iBAAyB;AAE/B,aAAK,SAAS,OAAO,YAAY,IAAI;MACvC;MAEQ,OAAO,MAAY;AACzB,cAAM,YAAY,KAAK,OAAO,SAAS,KAAK;AAC5C,YAAI,YAAY,MAAM;AACpB,gBAAM,YAAY,KAAK;AAGvB,gBAAM,UAAU,UAAU,UAAU,UAAU,UAAU,KAAK;AAC7D,eAAK,SAAS,OAAO,YAAY,OAAO;AACxC,oBAAU,KAAK,KAAK,MAAM;;MAE9B;MAEO,SAAS,KAAW;AACzB,aAAK,OAAO,CAAC;AACb,aAAK,OAAO,KAAK,QAAQ,IAAK,QAAQ,KAAM;AAC5C,aAAK,OAAO,KAAK,QAAQ,IAAK,QAAQ,KAAM;AAC5C,aAAK,OAAO,KAAK,QAAQ,IAAK,QAAQ,IAAK;AAC3C,aAAK,OAAO,KAAK,QAAQ,IAAK,QAAQ,IAAK;AAC3C,eAAO;MACT;MAEO,SAAS,KAAW;AACzB,aAAK,OAAO,CAAC;AACb,aAAK,OAAO,KAAK,QAAQ,IAAK,QAAQ,IAAK;AAC3C,aAAK,OAAO,KAAK,QAAQ,IAAK,QAAQ,IAAK;AAC3C,eAAO;MACT;MAEO,WAAW,QAAc;AAC9B,YAAI,CAAC,QAAQ;AACX,eAAK,OAAO,CAAC;eACR;AACL,gBAAM,MAAM,OAAO,WAAW,MAAM;AACpC,eAAK,OAAO,MAAM,CAAC;AACnB,eAAK,OAAO,MAAM,QAAQ,KAAK,QAAQ,OAAO;AAC9C,eAAK,UAAU;;AAGjB,aAAK,OAAO,KAAK,QAAQ,IAAI;AAC7B,eAAO;MACT;MAEO,UAAU,SAAiB,IAAE;AAClC,cAAM,MAAM,OAAO,WAAW,MAAM;AACpC,aAAK,OAAO,GAAG;AACf,aAAK,OAAO,MAAM,QAAQ,KAAK,MAAM;AACrC,aAAK,UAAU;AACf,eAAO;MACT;MAEO,IAAI,aAAmB;AAC5B,aAAK,OAAO,YAAY,MAAM;AAC9B,oBAAY,KAAK,KAAK,QAAQ,KAAK,MAAM;AACzC,aAAK,UAAU,YAAY;AAC3B,eAAO;MACT;MAEQ,KAAK,MAAa;AACxB,YAAI,MAAM;AACR,eAAK,OAAO,KAAK,cAAc,IAAI;AAEnC,gBAAM,SAAS,KAAK,UAAU,KAAK,iBAAiB;AACpD,eAAK,OAAO,aAAa,QAAQ,KAAK,iBAAiB,CAAC;;AAE1D,eAAO,KAAK,OAAO,MAAM,OAAO,IAAI,GAAG,KAAK,MAAM;MACpD;MAEO,MAAM,MAAa;AACxB,cAAM,SAAS,KAAK,KAAK,IAAI;AAC7B,aAAK,SAAS;AACd,aAAK,iBAAiB;AACtB,aAAK,SAAS,OAAO,YAAY,KAAK,IAAI;AAC1C,eAAO;MACT;;AAjFF,YAAA,SAAA;;;;;;;;;;ACFA,QAAA,kBAAA;AAkBA,QAAM,SAAS,IAAI,gBAAA,OAAM;AAEzB,QAAM,UAAU,CAAC,SAAwC;AAEvD,aAAO,SAAS,CAAC,EAAE,SAAS,CAAC;AAC7B,iBAAW,OAAO,OAAO,KAAK,IAAI,GAAG;AACnC,eAAO,WAAW,GAAG,EAAE,WAAW,KAAK,GAAG,CAAC;;AAG7C,aAAO,WAAW,iBAAiB,EAAE,WAAW,MAAM;AAEtD,YAAM,aAAa,OAAO,WAAW,EAAE,EAAE,MAAK;AAG9C,YAAM,SAAS,WAAW,SAAS;AAEnC,aAAO,IAAI,gBAAA,OAAM,EAAG,SAAS,MAAM,EAAE,IAAI,UAAU,EAAE,MAAK;IAC5D;AAEA,QAAM,aAAa,MAAa;AAC9B,YAAM,WAAW,OAAO,YAAY,CAAC;AACrC,eAAS,aAAa,GAAG,CAAC;AAC1B,eAAS,aAAa,UAAU,CAAC;AACjC,aAAO;IACT;AAEA,QAAM,WAAW,CAACC,cAA4B;AAC5C,aAAO,OAAO,WAAWA,SAAQ,EAAE;QAAK;;MAAA;IAC1C;AAEA,QAAM,iCAAiC,SAAU,WAAmB,iBAAuB;AAEzF,aAAO,WAAW,SAAS,EAAE,SAAS,OAAO,WAAW,eAAe,CAAC,EAAE,UAAU,eAAe;AAEnG,aAAO,OAAO;QAAK;;MAAA;IACrB;AAEA,QAAM,8BAA8B,SAAU,gBAAsB;AAClE,aAAO,OAAO,UAAU,cAAc,EAAE;QAAK;;MAAA;IAC/C;AAEA,QAAM,QAAQ,CAAC,SAAwB;AACrC,aAAO,OAAO,WAAW,IAAI,EAAE;QAAK;;MAAA;IACtC;AAQA,QAAM,aAAoB,CAAA;AAE1B,QAAM,QAAQ,CAACC,WAA4B;AAOzC,YAAM,OAAOA,OAAM,QAAQ;AAC3B,UAAI,KAAK,SAAS,IAAI;AACpB,gBAAQ,MAAM,gEAAgE;AAC9E,gBAAQ,MAAM,wBAAwB,MAAM,KAAK,MAAM;AACvD,gBAAQ,MAAM,8DAA8D;;AAG9E,YAAMC,SAAQD,OAAM,SAAS;AAE7B,YAAM,MAAMC,OAAM;AAElB,YAAM,SAAS,OACZ,WAAW,IAAI,EACf,WAAWD,OAAM,IAAI,EACrB,SAAS,GAAG;AAEf,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,eAAO,SAASC,OAAM,CAAC,CAAC;;AAG1B,aAAO,OAAO;QAAK;;MAAA;IACrB;AAaA,QAAM,cAAc,IAAI,gBAAA,OAAM;AAQ9B,QAAM,cAAc,SAAU,QAAe,aAAyB;AACpE,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,cAAM,YAAY,cAAc,YAAY,OAAO,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC;AACpE,YAAI,aAAa,MAAM;AAErB,iBAAO;YAAQ;;UAAA;AAEf,sBAAY,SAAS,EAAE;mBACd,qBAAqB,QAAQ;AAEtC,iBAAO;YAAQ;;UAAA;AAEf,sBAAY,SAAS,UAAU,MAAM;AACrC,sBAAY,IAAI,SAAS;eACpB;AAEL,iBAAO;YAAQ;;UAAA;AACf,sBAAY,SAAS,OAAO,WAAW,SAAS,CAAC;AACjD,sBAAY,UAAU,SAAS;;;IAGrC;AAEA,QAAM,OAAO,CAAC,SAAmB,CAAA,MAAc;AAE7C,YAAM,SAAS,OAAO,UAAU;AAChC,YAAM,YAAY,OAAO,aAAa;AACtC,YAAM,SAAS,OAAO,UAAU;AAChC,YAAM,SAAS,OAAO,UAAU;AAChC,YAAM,MAAM,OAAO;AAEnB,aAAO,WAAW,MAAM,EAAE,WAAW,SAAS;AAC9C,aAAO,SAAS,GAAG;AAEnB,kBAAY,QAAQ,OAAO,WAAW;AAEtC,aAAO,SAAS,GAAG;AACnB,aAAO,IAAI,YAAY,MAAK,CAAE;AAG9B,aAAO;QAAS,SAAQ,IAAmB;;MAAiB;AAC5D,aAAO,OAAO;QAAK;;MAAA;IACrB;AAOA,QAAM,eAAe,OAAO,KAAK,CAAA,IAAe,GAAM,GAAM,GAAM,GAAM,GAAM,GAAM,GAAM,GAAM,CAAI,CAAC;AAErG,QAAM,UAAU,CAAC,WAA6B;AAE5C,UAAI,CAAC,UAAW,CAAC,OAAO,UAAU,CAAC,OAAO,MAAO;AAC/C,eAAO;;AAGT,YAAM,SAAS,OAAO,UAAU;AAChC,YAAM,OAAO,OAAO,QAAQ;AAE5B,YAAM,eAAe,OAAO,WAAW,MAAM;AAC7C,YAAM,MAAM,IAAI,eAAe,IAAI;AAEnC,YAAM,OAAO,OAAO,YAAY,IAAI,GAAG;AACvC,WAAK,CAAC,IAAC;AACP,WAAK,aAAa,KAAK,CAAC;AACxB,WAAK,MAAM,QAAQ,GAAG,OAAO;AAC7B,WAAK,eAAe,CAAC,IAAI;AACzB,WAAK,cAAc,MAAM,KAAK,SAAS,CAAC;AACxC,aAAO;IACT;AAEA,QAAM,SAAS,CAAC,WAAmB,cAA6B;AAC9D,YAAM,SAAS,OAAO,YAAY,EAAE;AACpC,aAAO,aAAa,IAAI,CAAC;AACzB,aAAO,aAAa,MAAM,CAAC;AAC3B,aAAO,aAAa,MAAM,CAAC;AAC3B,aAAO,aAAa,WAAW,CAAC;AAChC,aAAO,aAAa,WAAW,EAAE;AACjC,aAAO;IACT;AAOA,QAAM,iBAAiB,CAAC,MAAY,WAA0B;AAC5D,YAAM,YAAY,OAAO,WAAW,MAAM;AAC1C,YAAM,MAAM,IAAI,YAAY;AAE5B,YAAM,SAAS,OAAO,YAAY,IAAI,GAAG;AACzC,aAAO,CAAC,IAAI;AACZ,aAAO,aAAa,KAAK,CAAC;AAC1B,aAAO,MAAM,QAAQ,GAAG,OAAO;AAC/B,aAAO,GAAG,IAAI;AACd,aAAO;IACT;AAEA,QAAM,sBAAsB,OAAO,WAAW,GAAG,EAAE;MAAK;;IAAA;AACxD,QAAM,yBAAyB,OAAO,WAAW,GAAG,EAAE;MAAK;;IAAA;AAE3D,QAAM,WAAW,CAAC,QAA2B;AAC3C,aAAO,IAAI,OACP,eAAc,IAAgB,GAAG,IAAI,IAAI,GAAG,IAAI,QAAQ,EAAE,EAAE,IAC5D,IAAI,SAAS,MACb,sBACA;IACN;AAEA,QAAM,QAAQ,CAAC,QAA2B;AACxC,YAAM,OAAO,GAAG,IAAI,IAAI,GAAG,IAAI,QAAQ,EAAE;AACzC,aAAO,eAAc,IAAa,IAAI;IACxC;AAEA,QAAM,WAAW,CAAC,UAAyB;AACzC,aAAO,OAAO,IAAI,KAAK,EAAE;QAAK;;MAAA;IAChC;AAEA,QAAM,WAAW,CAAC,YAA2B;AAC3C,aAAO,eAAc,KAAgB,OAAO;IAC9C;AAEA,QAAM,iBAAiB,CAAC,SAAuB,OAAO,KAAK,CAAC,MAAM,GAAM,GAAM,GAAM,CAAI,CAAC;AAEzF,QAAM,cAAc;MAAc;;IAAA;AAClC,QAAM,aAAa;MAAc;;IAAA;AACjC,QAAM,YAAY;MAAc;;IAAA;AAChC,QAAM,iBAAiB;MAAc;;IAAA;AAErC,QAAM,YAAY;MAChB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,OAAO,MAAM;MACb,MAAM,MAAM;MACZ,KAAK,MAAM;MACX;MACA,UAAU,MAAM;MAChB;MACA;;AAGO,YAAA,YAAA;;;;;;;;;;AC/QT,QAAM,cAAc,OAAO,YAAY,CAAC;AAExC,QAAa,eAAb,MAAyB;MAMvB,YAAoB,SAAiB,GAAC;AAAlB,aAAA,SAAA;AALZ,aAAA,SAAiB;AAGjB,aAAA,WAAmB;MAEc;MAElC,UAAU,QAAgB,QAAc;AAC7C,aAAK,SAAS;AACd,aAAK,SAAS;MAChB;MAEO,QAAK;AACV,cAAM,SAAS,KAAK,OAAO,YAAY,KAAK,MAAM;AAClD,aAAK,UAAU;AACf,eAAO;MACT;MAEO,OAAI;AACT,cAAM,SAAS,KAAK,OAAO,KAAK,MAAM;AACtC,aAAK;AACL,eAAO;MACT;MAEO,QAAK;AACV,cAAM,SAAS,KAAK,OAAO,YAAY,KAAK,MAAM;AAClD,aAAK,UAAU;AACf,eAAO;MACT;MAEO,SAAM;AACX,cAAM,SAAS,KAAK,OAAO,aAAa,KAAK,MAAM;AACnD,aAAK,UAAU;AACf,eAAO;MACT;MAEO,OAAO,QAAc;AAC1B,cAAM,SAAS,KAAK,OAAO,SAAS,KAAK,UAAU,KAAK,QAAQ,KAAK,SAAS,MAAM;AACpF,aAAK,UAAU;AACf,eAAO;MACT;MAEO,UAAO;AACZ,cAAM,QAAQ,KAAK;AACnB,YAAI,MAAM;AAEV,eAAO,KAAK,OAAO,KAAK,MAAM,GAAG;QAAA;AACjC,aAAK,SAAS;AACd,eAAO,KAAK,OAAO,SAAS,KAAK,UAAU,OAAO,MAAM,CAAC;MAC3D;MAEO,MAAM,QAAc;AACzB,cAAM,SAAS,KAAK,OAAO,MAAM,KAAK,QAAQ,KAAK,SAAS,MAAM;AAClE,aAAK,UAAU;AACf,eAAO;MACT;;AAxDF,YAAA,eAAA;;;;;;;;;;ACDA,QAAA,aAAA;AA2BA,QAAA,kBAAA;AAGA,QAAM,cAAc;AAGpB,QAAM,aAAa;AAEnB,QAAM,gBAAgB,cAAc;AAOpC,QAAM,cAAc,OAAO,YAAY,CAAC;AAiCxC,QAAa,SAAb,MAAmB;MAOjB,YAAY,MAAoB;AANxB,aAAA,SAAiB;AACjB,aAAA,eAAuB;AACvB,aAAA,eAAuB;AACvB,aAAA,SAAS,IAAI,gBAAA,aAAY;AAI/B,aAAI,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,UAAS,UAAU;AAC3B,gBAAM,IAAI,MAAM,+BAA+B;;AAEjD,aAAK,QAAO,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,SAAQ;MAC5B;MAEO,MAAM,QAAgB,UAAyB;AACpD,aAAK,YAAY,MAAM;AACvB,cAAM,mBAAmB,KAAK,eAAe,KAAK;AAClD,YAAI,SAAS,KAAK;AAClB,eAAO,SAAS,iBAAiB,kBAAkB;AAEjD,gBAAM,OAAO,KAAK,OAAO,MAAM;AAE/B,gBAAM,SAAS,KAAK,OAAO,aAAa,SAAS,WAAW;AAC5D,gBAAM,oBAAoB,cAAc;AACxC,cAAI,oBAAoB,UAAU,kBAAkB;AAClD,kBAAM,UAAU,KAAK,aAAa,SAAS,eAAe,MAAM,QAAQ,KAAK,MAAM;AACnF,qBAAS,OAAO;AAChB,sBAAU;iBACL;AACL;;;AAGJ,YAAI,WAAW,kBAAkB;AAE/B,eAAK,SAAS;AACd,eAAK,eAAe;AACpB,eAAK,eAAe;eACf;AAEL,eAAK,eAAe,mBAAmB;AACvC,eAAK,eAAe;;MAExB;MAEQ,YAAY,QAAc;AAChC,YAAI,KAAK,eAAe,GAAG;AACzB,gBAAM,YAAY,KAAK,eAAe,OAAO;AAC7C,gBAAM,gBAAgB,YAAY,KAAK;AACvC,cAAI,gBAAgB,KAAK,OAAO,YAAY;AAE1C,gBAAI;AACJ,gBAAI,aAAa,KAAK,OAAO,cAAc,KAAK,gBAAgB,KAAK,cAAc;AAEjF,0BAAY,KAAK;mBACZ;AAEL,kBAAI,kBAAkB,KAAK,OAAO,aAAa;AAC/C,qBAAO,aAAa,iBAAiB;AACnC,mCAAmB;;AAErB,0BAAY,OAAO,YAAY,eAAe;;AAGhD,iBAAK,OAAO,KAAK,WAAW,GAAG,KAAK,cAAc,KAAK,eAAe,KAAK,YAAY;AACvF,iBAAK,SAAS;AACd,iBAAK,eAAe;;AAGtB,iBAAO,KAAK,KAAK,QAAQ,KAAK,eAAe,KAAK,YAAY;AAC9D,eAAK,eAAe;eACf;AACL,eAAK,SAAS;AACd,eAAK,eAAe;AACpB,eAAK,eAAe,OAAO;;MAE/B;MAEQ,aAAa,QAAgB,MAAc,QAAgB,OAAa;AAC9E,gBAAQ,MAAM;UACZ,KAAA;AACE,mBAAO,WAAA;UACT,KAAA;AACE,mBAAO,WAAA;UACT,KAAA;AACE,mBAAO,WAAA;UACT,KAAA;AACE,mBAAO,WAAA;UACT,KAAA;AACE,mBAAO,WAAA;UACT,KAAA;AACE,mBAAO,WAAA;UACT,KAAA;AACE,mBAAO,WAAA;UACT,KAAA;AACE,mBAAO,WAAA;UACT,KAAA;AACE,mBAAO,KAAK,oBAAoB,QAAQ,QAAQ,KAAK;UACvD,KAAA;AACE,mBAAO,KAAK,4BAA4B,QAAQ,QAAQ,KAAK;UAC/D,KAAA;AACE,mBAAO,KAAK,0BAA0B,QAAQ,QAAQ,KAAK;UAC7D,KAAA;AACE,mBAAO,KAAK,yBAAyB,QAAQ,QAAQ,KAAK;UAC5D,KAAA;AACE,mBAAO,KAAK,4BAA4B,QAAQ,QAAQ,KAAK;UAC/D,KAAA;AACE,mBAAO,KAAK,4BAA4B,QAAQ,QAAQ,KAAK;UAC/D,KAAA;AACE,mBAAO,KAAK,oBAAoB,QAAQ,QAAQ,KAAK;UACvD,KAAA;AACE,mBAAO,KAAK,kBAAkB,QAAQ,QAAQ,OAAO,OAAO;UAC9D,KAAA;AACE,mBAAO,KAAK,kBAAkB,QAAQ,QAAQ,OAAO,QAAQ;UAC/D,KAAA;AACE,mBAAO,KAAK,2BAA2B,QAAQ,QAAQ,KAAK;UAC9D,KAAA;AACE,mBAAO,KAAK,iCAAiC,QAAQ,QAAQ,KAAK;UACpE,KAAA;AACE,mBAAO,KAAK,mBAAmB,QAAQ,QAAQ,KAAK;UACtD,KAAA;AACE,mBAAO,KAAK,oBAAoB,QAAQ,QAAQ,KAAK;UACvD,KAAA;AACE,mBAAO,KAAK,cAAc,QAAQ,QAAQ,KAAK;UACjD;AACE,mBAAO,IAAI,WAAA,cAAc,gCAAgC,KAAK,SAAS,EAAE,GAAG,QAAQ,OAAO;;MAEjG;MAEQ,0BAA0B,QAAgB,QAAgB,OAAa;AAC7E,aAAK,OAAO,UAAU,QAAQ,KAAK;AACnC,cAAM,SAAS,KAAK,OAAO,OAAO,CAAC;AACnC,eAAO,IAAI,WAAA,qBAAqB,QAAQ,MAAM;MAChD;MAEQ,4BAA4B,QAAgB,QAAgB,OAAa;AAC/E,aAAK,OAAO,UAAU,QAAQ,KAAK;AACnC,cAAM,OAAO,KAAK,OAAO,QAAO;AAChC,eAAO,IAAI,WAAA,uBAAuB,QAAQ,IAAI;MAChD;MAEQ,cAAc,QAAgB,QAAgB,OAAa;AACjE,cAAM,QAAQ,MAAM,MAAM,QAAQ,UAAU,SAAS,EAAE;AACvD,eAAO,IAAI,WAAA,gBAAgB,QAAQ,KAAK;MAC1C;MAEQ,mBAAmB,QAAgB,QAAgB,OAAa;AACtE,eAAO,KAAK,iBAAiB,QAAQ,QAAQ,OAAO,gBAAgB;MACtE;MAEQ,oBAAoB,QAAgB,QAAgB,OAAa;AACvE,eAAO,KAAK,iBAAiB,QAAQ,QAAQ,OAAO,iBAAiB;MACvE;MAEQ,iBAAiB,QAAgB,QAAgB,OAAe,aAAwB;AAC9F,aAAK,OAAO,UAAU,QAAQ,KAAK;AACnC,cAAM,WAAW,KAAK,OAAO,KAAI,MAAO;AACxC,cAAM,cAAc,KAAK,OAAO,MAAK;AACrC,cAAM,UAAU,IAAI,WAAA,aAAa,QAAQ,aAAa,UAAU,WAAW;AAC3E,iBAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,kBAAQ,YAAY,CAAC,IAAI,KAAK,OAAO,MAAK;;AAE5C,eAAO;MACT;MAEQ,yBAAyB,QAAgB,QAAgB,OAAa;AAC5E,aAAK,OAAO,UAAU,QAAQ,KAAK;AACnC,cAAM,YAAY,KAAK,OAAO,MAAK;AACnC,cAAM,UAAU,KAAK,OAAO,QAAO;AACnC,cAAM,UAAU,KAAK,OAAO,QAAO;AACnC,eAAO,IAAI,WAAA,4BAA4B,QAAQ,WAAW,SAAS,OAAO;MAC5E;MAEQ,2BAA2B,QAAgB,QAAgB,OAAa;AAC9E,aAAK,OAAO,UAAU,QAAQ,KAAK;AACnC,cAAM,aAAa,KAAK,OAAO,MAAK;AACpC,cAAM,UAAU,IAAI,WAAA,sBAAsB,QAAQ,UAAU;AAC5D,iBAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,kBAAQ,OAAO,CAAC,IAAI,KAAK,WAAU;;AAErC,eAAO;MACT;MAEQ,aAAU;AAChB,cAAM,OAAO,KAAK,OAAO,QAAO;AAChC,cAAM,UAAU,KAAK,OAAO,OAAM;AAClC,cAAM,WAAW,KAAK,OAAO,MAAK;AAClC,cAAM,aAAa,KAAK,OAAO,OAAM;AACrC,cAAM,eAAe,KAAK,OAAO,MAAK;AACtC,cAAM,mBAAmB,KAAK,OAAO,MAAK;AAC1C,cAAM,OAAO,KAAK,OAAO,MAAK,MAAO,IAAI,SAAS;AAClD,eAAO,IAAI,WAAA,MAAM,MAAM,SAAS,UAAU,YAAY,cAAc,kBAAkB,IAAI;MAC5F;MAEQ,iCAAiC,QAAgB,QAAgB,OAAa;AACpF,aAAK,OAAO,UAAU,QAAQ,KAAK;AACnC,cAAM,iBAAiB,KAAK,OAAO,MAAK;AACxC,cAAM,UAAU,IAAI,WAAA,4BAA4B,QAAQ,cAAc;AACtE,iBAAS,IAAI,GAAG,IAAI,gBAAgB,KAAK;AACvC,kBAAQ,YAAY,CAAC,IAAI,KAAK,OAAO,MAAK;;AAE5C,eAAO;MACT;MAEQ,oBAAoB,QAAgB,QAAgB,OAAa;AACvE,aAAK,OAAO,UAAU,QAAQ,KAAK;AACnC,cAAM,aAAa,KAAK,OAAO,MAAK;AACpC,cAAM,SAAgB,IAAI,MAAM,UAAU;AAC1C,iBAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,gBAAM,MAAM,KAAK,OAAO,MAAK;AAE7B,iBAAO,CAAC,IAAI,QAAQ,KAAK,OAAO,KAAK,OAAO,OAAO,GAAG;;AAExD,eAAO,IAAI,WAAA,eAAe,QAAQ,MAAM;MAC1C;MAEQ,4BAA4B,QAAgB,QAAgB,OAAa;AAC/E,aAAK,OAAO,UAAU,QAAQ,KAAK;AACnC,cAAM,OAAO,KAAK,OAAO,QAAO;AAChC,cAAM,QAAQ,KAAK,OAAO,QAAO;AACjC,eAAO,IAAI,WAAA,uBAAuB,QAAQ,MAAM,KAAK;MACvD;MAEQ,oBAAoB,QAAgB,QAAgB,OAAa;AACvE,aAAK,OAAO,UAAU,QAAQ,KAAK;AACnC,cAAM,YAAY,KAAK,OAAO,MAAK;AACnC,cAAM,YAAY,KAAK,OAAO,MAAK;AACnC,eAAO,IAAI,WAAA,sBAAsB,QAAQ,WAAW,SAAS;MAC/D;MAEO,4BAA4B,QAAgB,QAAgB,OAAa;AAC9E,aAAK,OAAO,UAAU,QAAQ,KAAK;AACnC,cAAM,OAAO,KAAK,OAAO,MAAK;AAE9B,cAAM,UAAgC;UACpC,MAAM;UACN;;AAGF,gBAAQ,MAAM;UACZ,KAAK;AACH;UACF,KAAK;AACH,gBAAI,QAAQ,WAAW,GAAG;AACxB,sBAAQ,OAAO;;AAEjB;UACF,KAAK;AACH,gBAAI,QAAQ,WAAW,IAAI;AACzB,sBAAQ,OAAO;AACf,oBAAM,OAAO,KAAK,OAAO,MAAM,CAAC;AAChC,qBAAO,IAAI,WAAA,0BAA0B,QAAQ,IAAI;;AAEnD;UACF,KAAK;AACH;AACE,sBAAQ,OAAO;AACf,sBAAQ,aAAa,CAAA;AACrB,kBAAI;AACJ,iBAAG;AACD,4BAAY,KAAK,OAAO,QAAO;AAC/B,oBAAI,WAAW;AACb,0BAAQ,WAAW,KAAK,SAAS;;uBAE5B;;AAEX;UACF,KAAK;AACH,oBAAQ,OAAO;AACf,oBAAQ,OAAO,KAAK,OAAO,OAAO,SAAS,CAAC;AAC5C;UACF,KAAK;AACH,oBAAQ,OAAO;AACf,oBAAQ,OAAO,KAAK,OAAO,OAAO,SAAS,CAAC;AAC5C;UACF;AACE,kBAAM,IAAI,MAAM,2CAA2C,IAAI;;AAEnE,eAAO;MACT;MAEQ,kBAAkB,QAAgB,QAAgB,OAAe,MAAiB;AACxF,aAAK,OAAO,UAAU,QAAQ,KAAK;AACnC,cAAM,SAAiC,CAAA;AACvC,YAAI,YAAY,KAAK,OAAO,OAAO,CAAC;AACpC,eAAO,cAAc,MAAM;AACzB,iBAAO,SAAS,IAAI,KAAK,OAAO,QAAO;AACvC,sBAAY,KAAK,OAAO,OAAO,CAAC;;AAGlC,cAAM,eAAe,OAAO;AAE5B,cAAM,UACJ,SAAS,WAAW,IAAI,WAAA,cAAc,QAAQ,YAAY,IAAI,IAAI,WAAA,cAAc,cAAc,QAAQ,IAAI;AAE5G,gBAAQ,WAAW,OAAO;AAC1B,gBAAQ,OAAO,OAAO;AACtB,gBAAQ,SAAS,OAAO;AACxB,gBAAQ,OAAO,OAAO;AACtB,gBAAQ,WAAW,OAAO;AAC1B,gBAAQ,mBAAmB,OAAO;AAClC,gBAAQ,gBAAgB,OAAO;AAC/B,gBAAQ,QAAQ,OAAO;AACvB,gBAAQ,SAAS,OAAO;AACxB,gBAAQ,QAAQ,OAAO;AACvB,gBAAQ,SAAS,OAAO;AACxB,gBAAQ,WAAW,OAAO;AAC1B,gBAAQ,aAAa,OAAO;AAC5B,gBAAQ,OAAO,OAAO;AACtB,gBAAQ,OAAO,OAAO;AACtB,gBAAQ,UAAU,OAAO;AACzB,eAAO;MACT;;AAvTF,YAAA,SAAA;;;;;;;;;;AC5EA,QAAA,aAAA;AAUoB,WAAA,eAAA,SAAA,iBAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAVX,WAAA;IAAa,EAAA,CAAA;AACtB,QAAA,eAAA;AASS,WAAA,eAAA,SAAA,aAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aATA,aAAA;IAAS,EAAA,CAAA;AAClB,QAAA,WAAA;AAEA,aAAgB,MAAM,QAA+B,UAAyB;AAC5E,YAAM,SAAS,IAAI,SAAA,OAAM;AACzB,aAAO,GAAG,QAAQ,CAAC,WAAmB,OAAO,MAAM,QAAQ,QAAQ,CAAC;AACpE,aAAO,IAAI,QAAQ,CAAC,YAAY,OAAO,GAAG,OAAO,MAAM,QAAO,CAAE,CAAC;IACnE;AAJA,YAAA,QAAA;;;;;;;;;ACFA,YAAA,UAAe,CAAA;;;;;ACFf,IAAAC,kBAAA;AAAA;AAAA,QAAM,EAAE,WAAW,gBAAgB,IAAI,eAAe;AAEtD,WAAO,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA;AAAA,IACF;AAKA,aAAS,uBAAuB;AAC9B,eAASC,WAAU,KAAK;AACtB,cAAM,MAAM;AACZ,eAAO,IAAI,IAAI,OAAO;AAAA,MACxB;AAEA,eAASC,iBAAgB,SAAS;AAChC,cAAM,MAAM;AACZ,eAAO,IAAI,QAAQ,OAAO;AAAA,MAC5B;AACA,aAAO;AAAA,QACL,WAAAD;AAAA,QACA,iBAAAC;AAAA,MACF;AAAA,IACF;AAKA,aAAS,2BAA2B;AAClC,eAASD,WAAU,KAAK;AACtB,cAAM,EAAE,iBAAiB,IAAI;AAC7B,eAAO,IAAI,iBAAiB,GAAG;AAAA,MACjC;AAEA,eAASC,iBAAgB,SAAS;AAChC,gBAAQ,OAAO,SAAS,OAAO;AAC/B,eAAO,QAAQ;AAAA,MACjB;AACA,aAAO;AAAA,QACL,WAAAD;AAAA,QACA,iBAAAC;AAAA,MACF;AAAA,IACF;AAOA,aAAS,sBAAsB;AAI7B,UAAI,OAAO,cAAc,YAAY,cAAc,QAAQ,OAAO,UAAU,cAAc,UAAU;AAElG,eAAO,UAAU,cAAc;AAAA,MACjC;AAEA,UAAI,OAAO,aAAa,YAAY;AAClC,cAAM,OAAO,IAAI,SAAS,MAAM,EAAE,IAAI,EAAE,OAAO,KAAK,EAAE,CAAC;AACvD,YAAI,OAAO,KAAK,OAAO,YAAY,KAAK,OAAO,QAAQ,KAAK,GAAG,OAAO;AACpE,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,aAAS,iBAAiB;AACxB,UAAI,oBAAoB,GAAG;AACzB,eAAO,yBAAyB;AAAA,MAClC;AACA,aAAO,qBAAqB;AAAA,IAC9B;AAAA;AAAA;;;AClFA;AAAA;AAAA;AAEA,QAAM,eAAe,iBAAkB;AAEvC,QAAM,EAAE,OAAO,UAAU,IAAI;AAC7B,QAAM,EAAE,WAAW,gBAAgB,IAAI;AAEvC,QAAM,cAAc,UAAU,MAAM;AACpC,QAAM,aAAa,UAAU,KAAK;AAClC,QAAM,YAAY,UAAU,IAAI;AAGhC,QAAMC,cAAN,cAAyB,aAAa;AAAA,MACpC,YAAY,QAAQ;AAClB,cAAM;AACN,iBAAS,UAAU,CAAC;AAEpB,aAAK,SAAS,OAAO,UAAU,UAAU,OAAO,GAAG;AACnD,YAAI,OAAO,KAAK,WAAW,YAAY;AACrC,eAAK,SAAS,KAAK,OAAO,MAAM;AAAA,QAClC;AAEA,aAAK,aAAa,OAAO;AACzB,aAAK,+BAA+B,OAAO;AAC3C,aAAK,aAAa;AAClB,aAAK,mBAAmB,CAAC;AACzB,aAAK,MAAM,OAAO,OAAO;AACzB,aAAK,UAAU;AACf,aAAK,eAAe;AACpB,cAAM,OAAO;AACb,aAAK,GAAG,eAAe,SAAU,WAAW;AAC1C,cAAI,cAAc,WAAW;AAC3B,iBAAK,eAAe;AAAA,UACtB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MAEA,QAAQ,MAAM,MAAM;AAClB,cAAM,OAAO;AAEb,aAAK,cAAc;AACnB,aAAK,OAAO,WAAW,IAAI;AAC3B,aAAK,OAAO,QAAQ,MAAM,IAAI;AAE9B,aAAK,OAAO,KAAK,WAAW,WAAY;AACtC,cAAI,KAAK,YAAY;AACnB,iBAAK,OAAO,aAAa,MAAM,KAAK,4BAA4B;AAAA,UAClE;AACA,eAAK,KAAK,SAAS;AAAA,QACrB,CAAC;AAED,cAAM,oBAAoB,SAAU,OAAO;AAEzC,cAAI,KAAK,YAAY,MAAM,SAAS,gBAAgB,MAAM,SAAS,UAAU;AAC3E;AAAA,UACF;AACA,eAAK,KAAK,SAAS,KAAK;AAAA,QAC1B;AACA,aAAK,OAAO,GAAG,SAAS,iBAAiB;AAEzC,aAAK,OAAO,GAAG,SAAS,WAAY;AAClC,eAAK,KAAK,KAAK;AAAA,QACjB,CAAC;AAED,YAAI,CAAC,KAAK,KAAK;AACb,iBAAO,KAAK,gBAAgB,KAAK,MAAM;AAAA,QACzC;AAEA,aAAK,OAAO,KAAK,QAAQ,SAAU,QAAQ;AACzC,gBAAM,eAAe,OAAO,SAAS,MAAM;AAC3C,kBAAQ,cAAc;AAAA,YACpB,KAAK;AACH;AAAA,YACF,KAAK;AACH,mBAAK,OAAO,IAAI;AAChB,qBAAO,KAAK,KAAK,SAAS,IAAI,MAAM,6CAA6C,CAAC;AAAA,YACpF;AAEE,mBAAK,OAAO,IAAI;AAChB,qBAAO,KAAK,KAAK,SAAS,IAAI,MAAM,mDAAmD,CAAC;AAAA,UAC5F;AACA,gBAAM,UAAU;AAAA,YACd,QAAQ,KAAK;AAAA,UACf;AAEA,cAAI,KAAK,QAAQ,MAAM;AACrB,mBAAO,OAAO,SAAS,KAAK,GAAG;AAE/B,gBAAI,SAAS,KAAK,KAAK;AACrB,sBAAQ,MAAM,KAAK,IAAI;AAAA,YACzB;AAAA,UACF;AAEA,gBAAM,MAAM;AACZ,cAAI,IAAI,QAAQ,IAAI,KAAK,IAAI,MAAM,GAAG;AACpC,oBAAQ,aAAa;AAAA,UACvB;AACA,cAAI;AACF,iBAAK,SAAS,gBAAgB,OAAO;AAAA,UACvC,SAAS,KAAK;AACZ,mBAAO,KAAK,KAAK,SAAS,GAAG;AAAA,UAC/B;AACA,eAAK,gBAAgB,KAAK,MAAM;AAChC,eAAK,OAAO,GAAG,SAAS,iBAAiB;AAEzC,eAAK,KAAK,YAAY;AAAA,QACxB,CAAC;AAAA,MACH;AAAA,MAEA,gBAAgB,QAAQ;AACtB,cAAM,QAAQ,CAAC,QAAQ;AACrB,gBAAM,YAAY,IAAI,SAAS,UAAU,iBAAiB,IAAI;AAC9D,cAAI,KAAK,cAAc;AACrB,iBAAK,KAAK,WAAW,GAAG;AAAA,UAC1B;AACA,eAAK,KAAK,WAAW,GAAG;AAAA,QAC1B,CAAC;AAAA,MACH;AAAA,MAEA,aAAa;AACX,aAAK,OAAO,MAAM,UAAU,WAAW,CAAC;AAAA,MAC1C;AAAA,MAEA,QAAQ,QAAQ;AACd,aAAK,OAAO,MAAM,UAAU,QAAQ,MAAM,CAAC;AAAA,MAC7C;AAAA,MAEA,OAAO,WAAW,WAAW;AAC3B,aAAK,MAAM,UAAU,OAAO,WAAW,SAAS,CAAC;AAAA,MACnD;AAAA,MAEA,SAAS,UAAU;AACjB,aAAK,MAAM,UAAU,SAAS,QAAQ,CAAC;AAAA,MACzC;AAAA,MAEA,+BAA+B,WAAW,iBAAiB;AACzD,aAAK,MAAM,UAAU,+BAA+B,WAAW,eAAe,CAAC;AAAA,MACjF;AAAA,MAEA,4BAA4B,gBAAgB;AAC1C,aAAK,MAAM,UAAU,4BAA4B,cAAc,CAAC;AAAA,MAClE;AAAA,MAEA,MAAM,QAAQ;AACZ,YAAI,CAAC,KAAK,OAAO,UAAU;AACzB,iBAAO;AAAA,QACT;AACA,eAAO,KAAK,OAAO,MAAM,MAAM;AAAA,MACjC;AAAA,MAEA,MAAM,MAAM;AACV,aAAK,MAAM,UAAU,MAAM,IAAI,CAAC;AAAA,MAClC;AAAA;AAAA,MAGA,MAAM,OAAO;AACX,aAAK,MAAM,UAAU,MAAM,KAAK,CAAC;AAAA,MACnC;AAAA;AAAA,MAGA,KAAK,QAAQ;AACX,aAAK,MAAM,UAAU,KAAK,MAAM,CAAC;AAAA,MACnC;AAAA;AAAA,MAGA,QAAQ,QAAQ;AACd,aAAK,MAAM,UAAU,QAAQ,MAAM,CAAC;AAAA,MACtC;AAAA,MAEA,QAAQ;AACN,YAAI,KAAK,OAAO,UAAU;AACxB,eAAK,OAAO,MAAM,WAAW;AAAA,QAC/B;AAAA,MACF;AAAA,MAEA,OAAO;AACL,aAAK,UAAU;AACf,aAAK,MAAM,UAAU;AAAA,MACvB;AAAA,MAEA,MAAM;AACJ,aAAK,OAAO,IAAI;AAAA,MAClB;AAAA,MAEA,QAAQ;AACN,aAAK,OAAO,MAAM;AAAA,MACpB;AAAA,MAEA,MAAM;AAEJ,aAAK,UAAU;AACf,YAAI,CAAC,KAAK,eAAe,CAAC,KAAK,OAAO,UAAU;AAC9C,eAAK,OAAO,IAAI;AAChB;AAAA,QACF;AACA,eAAO,KAAK,OAAO,MAAM,WAAW,MAAM;AACxC,eAAK,OAAO,IAAI;AAAA,QAClB,CAAC;AAAA,MACH;AAAA,MAEA,MAAM,KAAK;AACT,aAAK,MAAM,UAAU,MAAM,GAAG,CAAC;AAAA,MACjC;AAAA,MAEA,SAAS,KAAK;AACZ,aAAK,MAAM,UAAU,SAAS,GAAG,CAAC;AAAA,MACpC;AAAA,MAEA,kBAAkB,OAAO;AACvB,aAAK,MAAM,UAAU,SAAS,KAAK,CAAC;AAAA,MACtC;AAAA,MAEA,cAAc;AACZ,aAAK,MAAM,UAAU,SAAS,CAAC;AAAA,MACjC;AAAA,MAEA,aAAa,KAAK;AAChB,aAAK,MAAM,UAAU,SAAS,GAAG,CAAC;AAAA,MACpC;AAAA,IACF;AAEA,WAAO,UAAUA;AAAA;AAAA;;;AC7NjB;AAAA;AAAA;AAkBA,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,EAAE,cAAc,IAAI;AAC1B,QAAM,QAAQ,OAAO,MAAM;AAC3B,QAAM,WAAW,OAAO,SAAS;AAEjC,aAAS,UAAW,OAAO,KAAK,IAAI;AAClC,UAAI;AACJ,UAAI,KAAK,UAAU;AACjB,cAAM,MAAM,KAAK,QAAQ,EAAE,MAAM,KAAK;AACtC,eAAO,IAAI,MAAM,KAAK,OAAO;AAE7B,YAAI,KAAK,WAAW,EAAG,QAAO,GAAG;AAGjC,aAAK,MAAM;AACX,aAAK,WAAW;AAAA,MAClB,OAAO;AACL,aAAK,KAAK,KAAK,KAAK,QAAQ,EAAE,MAAM,KAAK;AACzC,eAAO,KAAK,KAAK,EAAE,MAAM,KAAK,OAAO;AAAA,MACvC;AAEA,WAAK,KAAK,IAAI,KAAK,IAAI;AAEvB,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,YAAI;AACF,eAAK,MAAM,KAAK,OAAO,KAAK,CAAC,CAAC,CAAC;AAAA,QACjC,SAAS,OAAO;AACd,iBAAO,GAAG,KAAK;AAAA,QACjB;AAAA,MACF;AAEA,WAAK,WAAW,KAAK,KAAK,EAAE,SAAS,KAAK;AAC1C,UAAI,KAAK,YAAY,CAAC,KAAK,cAAc;AACvC,WAAG,IAAI,MAAM,wBAAwB,CAAC;AACtC;AAAA,MACF;AAEA,SAAG;AAAA,IACL;AAEA,aAAS,MAAO,IAAI;AAElB,WAAK,KAAK,KAAK,KAAK,QAAQ,EAAE,IAAI;AAElC,UAAI,KAAK,KAAK,GAAG;AACf,YAAI;AACF,eAAK,MAAM,KAAK,OAAO,KAAK,KAAK,CAAC,CAAC;AAAA,QACrC,SAAS,OAAO;AACd,iBAAO,GAAG,KAAK;AAAA,QACjB;AAAA,MACF;AAEA,SAAG;AAAA,IACL;AAEA,aAAS,KAAM,MAAM,KAAK;AACxB,UAAI,QAAQ,QAAW;AACrB,aAAK,KAAK,GAAG;AAAA,MACf;AAAA,IACF;AAEA,aAAS,KAAM,UAAU;AACvB,aAAO;AAAA,IACT;AAEA,aAAS,MAAO,SAAS,QAAQ,SAAS;AAExC,gBAAU,WAAW;AACrB,eAAS,UAAU;AACnB,gBAAU,WAAW,CAAC;AAGtB,cAAQ,UAAU,QAAQ;AAAA,QACxB,KAAK;AAEH,cAAI,OAAO,YAAY,YAAY;AACjC,qBAAS;AACT,sBAAU;AAAA,UAEZ,WAAW,OAAO,YAAY,YAAY,EAAE,mBAAmB,WAAW,CAAC,QAAQ,OAAO,KAAK,GAAG;AAChG,sBAAU;AACV,sBAAU;AAAA,UACZ;AACA;AAAA,QAEF,KAAK;AAEH,cAAI,OAAO,YAAY,YAAY;AACjC,sBAAU;AACV,qBAAS;AACT,sBAAU;AAAA,UAEZ,WAAW,OAAO,WAAW,UAAU;AACrC,sBAAU;AACV,qBAAS;AAAA,UACX;AAAA,MACJ;AAEA,gBAAU,OAAO,OAAO,CAAC,GAAG,OAAO;AACnC,cAAQ,cAAc;AACtB,cAAQ,YAAY;AACpB,cAAQ,QAAQ;AAChB,cAAQ,qBAAqB;AAE7B,YAAM,SAAS,IAAI,UAAU,OAAO;AAEpC,aAAO,KAAK,IAAI;AAChB,aAAO,QAAQ,IAAI,IAAI,cAAc,MAAM;AAC3C,aAAO,UAAU;AACjB,aAAO,SAAS;AAChB,aAAO,YAAY,QAAQ;AAC3B,aAAO,eAAe,QAAQ,gBAAgB;AAC9C,aAAO,WAAW;AAClB,aAAO,WAAW,SAAU,KAAK,IAAI;AAEnC,aAAK,eAAe,eAAe;AACnC,WAAG,GAAG;AAAA,MACR;AAEA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC5IjB;AAAA;AAAA;AAEA,QAAI,OAAO;AAAX,QACI,SAAS,iBAAkB;AAD/B,QAEI,QAAQ;AAFZ,QAGI,OAAO;AAHX,QAII,cAAc;AAJlB,QAKI,QAAS,QAAQ,aAAa;AALlC,QAMI,aAAa,QAAQ;AAIzB,QAAI,UAAU;AAAd,QACI,UAAU;AADd,QAEI,SAAU;AAFd,QAGI,UAAU;AAEd,aAAS,UAAU,MAAM;AACrB,cAAS,OAAO,WAAW;AAAA,IAC/B;AAEA,QAAI,aAAa,CAAE,QAAQ,QAAQ,YAAY,QAAQ,UAAW;AAClE,QAAI,aAAa,WAAW;AAC5B,QAAI,UAAU,WAAY,aAAY,CAAE;AAGxC,aAAS,OAAO;AACZ,UAAI,aACA,sBAAsB,UACpB,SAAS,WAAW;AAG1B,UAAI,YAAY;AACZ,YAAI,OAAO,MAAM,UAAU,MAAM,KAAK,SAAS,EAAE,OAAO,IAAI;AAC5D,mBAAW,MAAO,KAAK,OAAO,MAAM,MAAM,IAAI,CAAE;AAAA,MACpD;AAAA,IACJ;AAGA,WAAO,eAAe,OAAO,SAAS,SAAS;AAAA,MAC3C,KAAM,WAAW;AACb,eAAO;AAAA,MACX;AAAA,MACA,KAAM,SAAS,KAAK;AAChB,gBAAQ;AAAA,MACZ;AAAA,IACJ,CAAC;AAGD,WAAO,QAAQ,SAAS,SAAS,QAAQ;AACrC,UAAI,MAAM;AACV,mBAAa;AACb,aAAO;AAAA,IACX;AAEA,WAAO,QAAQ,cAAc,SAAS,QAAO;AACzC,UAAI,MAAM,UAAU,QAAQ;AAC5B,UAAI,OAAO,IAAI,eACX,QACE,KAAK,KAAM,IAAI,WAAW,MAAO,cAAc,aAAc,IAC7D,KAAK,KAAM,IAAI,QAAQ,MAAM,SAAU;AAE7C,aAAO;AAAA,IACX;AAEA,WAAO,QAAQ,YAAY,SAAS,OAAO,OAAO;AAC9C,UAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,KAAK,YAAY,GAAG;AACjE,eAAO;AAAA,MACX;AAEA,UAAI,OAAO;AACP,eAAO;AAAA,MACX;AAEA,cAAQ,SAAS;AAEjB,UAAI,CAAE,UAAU,MAAM,IAAI,GAAG;AACzB,aAAK,mDAAmD,KAAK;AAC7D,eAAO;AAAA,MACX;AAEA,UAAI,MAAM,QAAQ,UAAU,UAAU;AAElC,aAAK,oGAAoG,KAAK;AAC9G,eAAO;AAAA,MACX;AAEA,aAAO;AAAA,IACX;AAGA,QAAI,UAAU,OAAO,QAAQ,QAAQ,SAAS,UAAU,OAAO;AAC3D,aAAO,WAAW,MAAM,GAAG,EAAE,EAAE,OAAO,SAAS,MAAM,OAAO,KAAI;AAC5D,YAAI,OAAO,GAAG;AAEV,cAAK,OAAQ,SAAS,KAAK,KAAK,WAAY,MAAM,OAAQ,MAAM,KAAK,CAAE,GAAI;AACvE,mBAAO,QAAQ;AAAA,UACnB;AAAA,QACJ;AACA,eAAO,SACH,MAAM,KAAK,MAAM,OACf,MAAM,KAAK,MAAM,SAAS,KAAK;AAAA,MAEzC,GAAG,IAAI;AAAA,IACX;AAGA,WAAO,QAAQ,cAAc,SAAS,UAAU,QAAQ,IAAI;AACxD,UAAI;AACJ,UAAI,aAAa,OAAO,KAAK,MAAM,CAAC;AAEpC,eAAS,OAAO,MAAM;AAClB,YAAI,QAAQ,UAAU,IAAI;AAC1B,YAAI,SAAS,aAAa,KAAK,KAAK,QAAQ,UAAU,KAAK,GAAG;AAC1D,iBAAO,MAAM,OAAO;AACpB,qBAAW,IAAI;AAAA,QACnB;AAAA,MACJ;AAEA,UAAI,QAAQ,WAAW;AACnB,eAAO,QAAQ;AACf,WAAG,IAAI;AAAA,MACX;AAEA,UAAI,QAAQ,SAAS,KAAK;AACtB,eAAO,QAAQ;AACf,aAAK,sCAAsC,GAAG;AAC9C,WAAG,MAAS;AAAA,MAChB;AAEA,aAAO,GAAG,SAAS,KAAK;AACxB,iBACK,GAAG,QAAQ,MAAM,EACjB,GAAG,OAAO,KAAK,EACf,GAAG,SAAS,KAAK;AAAA,IAG1B;AAGA,QAAI,YAAY,OAAO,QAAQ,YAAY,SAAS,MAAM;AACtD,UAAI,KAAK,SAAS,MAAM,KAAK,MAAM,OAAO,GAAG;AACzC,eAAO;AAAA,MACX;AAEA,UAAI,UAAU;AACd,UAAI,WAAW;AACf,UAAI,WAAW;AACf,UAAI,WAAW;AACf,UAAI,SAAS;AACb,UAAI,MAAM,CAAC;AACX,UAAI,cAAc;AAClB,UAAI,WAAW,SAAS,KAAK,IAAI,IAAI;AACjC,YAAI,QAAQ,KAAK,UAAU,IAAI,EAAE;AAEjC,YAAI,CAAE,OAAO,eAAe,KAAK,QAAQ,KAAK,oBAAoB,GAAG;AACjE,kBAAQ,MAAM,QAAQ,cAAc,IAAI;AAAA,QAC5C;AAEA,YAAK,WAAW,GAAG,CAAE,IAAI;AAAA,MAC7B;AAEA,eAAS,IAAI,GAAI,IAAI,KAAK,SAAO,GAAI,KAAK,GAAG;AACzC,kBAAU,KAAK,OAAO,IAAE,CAAC;AACzB,mBAAW,KAAK,OAAO,CAAC;AAExB,sBAAe,YAAY,aAAW;AAEtC,YAAI,aAAa;AACb,mBAAS,UAAU,QAAQ;AAC3B;AAAA,QACJ;AAEA,YAAI,KAAK,KAAK,WAAW,OAAO,aAAa,MAAM;AAC/C,mBAAS,UAAU,UAAU,IAAE,CAAC;AAEhC,qBAAW,IAAE;AACb,sBAAY;AAAA,QAChB;AAAA,MACJ;AAEA,YAAQ,OAAO,KAAK,GAAG,EAAE,WAAW,aAAe,MAAM;AAEzD,aAAO;AAAA,IACX;AAGA,QAAI,eAAe,OAAO,QAAQ,eAAe,SAAS,OAAM;AAC5D,UAAI,QAAQ;AAAA;AAAA,QAER,GAAI,SAAS,GAAE;AACX,iBAAO,EAAE,SAAS;AAAA,QACtB;AAAA;AAAA,QAEA,GAAI,SAAS,GAAE;AACX,cAAI,MAAM,KAAK;AACX,mBAAO;AAAA,UACX;AACA,cAAI,OAAO,CAAC;AACZ,iBACI,SAAS,CAAC,KACR,IAAI,KACJ,IAAI,oBACJ,KAAK,MAAM,CAAC,MAAM;AAAA,QAE5B;AAAA;AAAA,QAEA,GAAI,SAAS,GAAE;AACX,iBAAO,EAAE,SAAS;AAAA,QACtB;AAAA;AAAA,QAEA,GAAI,SAAS,GAAE;AACX,iBAAO,EAAE,SAAS;AAAA,QACtB;AAAA;AAAA,QAEA,GAAI,SAAS,GAAE;AACX,iBAAO,EAAE,SAAS;AAAA,QACtB;AAAA,MACJ;AAEA,eAAS,MAAM,GAAI,MAAM,WAAW,QAAS,OAAO,GAAG;AACnD,YAAI,OAAO,MAAM,GAAG;AACpB,YAAI,QAAQ,MAAO,WAAW,GAAG,CAAE,KAAK;AAExC,YAAI,MAAM,KAAK,KAAK;AACpB,YAAI,CAAC,KAAK;AACN,iBAAO;AAAA,QACX;AAAA,MACJ;AAEA,aAAO;AAAA,IACX;AAAA;AAAA;;;ACvOA;AAAA;AAAA;AAEA,QAAI,OAAO;AAAX,QACI,KAAK;AADT,QAEI,SAAS;AAIb,WAAO,UAAU,SAAS,UAAU,IAAI;AACpC,UAAI,OAAO,OAAO,YAAY;AAE9B,SAAG,KAAK,MAAM,SAAS,KAAK,MAAK;AAC7B,YAAI,OAAO,CAAC,OAAO,UAAU,MAAM,IAAI,GAAG;AACtC,iBAAO,GAAG,MAAS;AAAA,QACvB;AAEA,YAAI,KAAK,GAAG,iBAAiB,IAAI;AAEjC,eAAO,YAAY,UAAU,IAAI,EAAE;AAAA,MACvC,CAAC;AAAA,IACL;AAEA,WAAO,QAAQ,SAAS,OAAO;AAAA;AAAA;;;ACtB/B;AAAA;AAAA;AAEA,QAAM,eAAe,iBAAkB;AACvC,QAAM,QAAQ;AACd,QAAM,OAAO;AACb,QAAMC,iBAAgB;AAEtB,QAAM,uBAAuB;AAC7B,QAAMC,SAAQ;AACd,QAAMC,YAAW;AACjB,QAAMC,cAAa;AACnB,QAAM,SAAS;AAEf,QAAMC,UAAN,cAAqB,aAAa;AAAA,MAChC,YAAY,QAAQ;AAClB,cAAM;AAEN,aAAK,uBAAuB,IAAI,qBAAqB,MAAM;AAC3D,aAAK,OAAO,KAAK,qBAAqB;AACtC,aAAK,WAAW,KAAK,qBAAqB;AAC1C,aAAK,OAAO,KAAK,qBAAqB;AACtC,aAAK,OAAO,KAAK,qBAAqB;AAItC,eAAO,eAAe,MAAM,YAAY;AAAA,UACtC,cAAc;AAAA,UACd,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,OAAO,KAAK,qBAAqB;AAAA,QACnC,CAAC;AAED,aAAK,cAAc,KAAK,qBAAqB;AAE7C,cAAM,IAAI,UAAU,CAAC;AAErB,aAAK,WAAW,EAAE,WAAW,OAAO;AACpC,aAAK,SAAS,IAAIJ,eAAc,EAAE,KAAK;AACvC,aAAK,UAAU;AACf,aAAK,SAAS;AACd,aAAK,cAAc;AACnB,aAAK,aAAa;AAClB,aAAK,mBAAmB;AACxB,aAAK,aAAa;AAElB,aAAK,uBAAuB,QAAQ,EAAE,oBAAoB;AAC1D,aAAK,aACH,EAAE,cACF,IAAIG,YAAW;AAAA,UACb,QAAQ,EAAE;AAAA,UACV,KAAK,KAAK,qBAAqB;AAAA,UAC/B,WAAW,EAAE,aAAa;AAAA,UAC1B,6BAA6B,EAAE,+BAA+B;AAAA,UAC9D,UAAU,KAAK,qBAAqB,mBAAmB;AAAA,QACzD,CAAC;AACH,aAAK,aAAa,CAAC;AACnB,aAAK,SAAS,EAAE,UAAUD,UAAS;AACnC,aAAK,YAAY;AACjB,aAAK,YAAY;AACjB,aAAK,MAAM,KAAK,qBAAqB,OAAO;AAI5C,YAAI,KAAK,OAAO,KAAK,IAAI,KAAK;AAC5B,iBAAO,eAAe,KAAK,KAAK,OAAO;AAAA,YACrC,YAAY;AAAA,UACd,CAAC;AAAA,QACH;AAEA,aAAK,2BAA2B,EAAE,2BAA2B;AAAA,MAC/D;AAAA,MAEA,iBAAiB,KAAK;AACpB,cAAM,eAAe,CAAC,UAAU;AAC9B,kBAAQ,SAAS,MAAM;AACrB,kBAAM,YAAY,KAAK,KAAK,UAAU;AAAA,UACxC,CAAC;AAAA,QACH;AAEA,YAAI,KAAK,aAAa;AACpB,uBAAa,KAAK,WAAW;AAC7B,eAAK,cAAc;AAAA,QACrB;AAEA,aAAK,WAAW,QAAQ,YAAY;AACpC,aAAK,WAAW,SAAS;AAAA,MAC3B;AAAA,MAEA,SAAS,UAAU;AACjB,cAAM,OAAO;AACb,cAAM,MAAM,KAAK;AACjB,aAAK,sBAAsB;AAE3B,YAAI,KAAK,eAAe,KAAK,YAAY;AACvC,gBAAM,MAAM,IAAI,MAAM,+DAA+D;AACrF,kBAAQ,SAAS,MAAM;AACrB,qBAAS,GAAG;AAAA,UACd,CAAC;AACD;AAAA,QACF;AACA,aAAK,cAAc;AAEnB,YAAI,KAAK,2BAA2B,GAAG;AACrC,eAAK,0BAA0B,WAAW,MAAM;AAC9C,gBAAI,UAAU;AACd,gBAAI,OAAO,QAAQ,IAAI,MAAM,iBAAiB,CAAC;AAAA,UACjD,GAAG,KAAK,wBAAwB;AAEhC,cAAI,KAAK,wBAAwB,OAAO;AACtC,iBAAK,wBAAwB,MAAM;AAAA,UACrC;AAAA,QACF;AAEA,YAAI,KAAK,QAAQ,KAAK,KAAK,QAAQ,GAAG,MAAM,GAAG;AAC7C,cAAI,QAAQ,KAAK,OAAO,eAAe,KAAK,IAAI;AAAA,QAClD,OAAO;AACL,cAAI,QAAQ,KAAK,MAAM,KAAK,IAAI;AAAA,QAClC;AAGA,YAAI,GAAG,WAAW,WAAY;AAC5B,cAAI,KAAK,KAAK;AACZ,gBAAI,WAAW;AAAA,UACjB,OAAO;AACL,gBAAI,QAAQ,KAAK,eAAe,CAAC;AAAA,UACnC;AAAA,QACF,CAAC;AAED,YAAI,GAAG,cAAc,WAAY;AAC/B,cAAI,QAAQ,KAAK,eAAe,CAAC;AAAA,QACnC,CAAC;AAED,aAAK,iBAAiB,GAAG;AAEzB,YAAI,KAAK,OAAO,MAAM;AACpB,gBAAM,QAAQ,KAAK,UAAU,IAAI,MAAM,uBAAuB,IAAI,IAAI,MAAM,oCAAoC;AAEhH,uBAAa,KAAK,uBAAuB;AACzC,eAAK,iBAAiB,KAAK;AAC3B,eAAK,SAAS;AAEd,cAAI,CAAC,KAAK,SAAS;AAKjB,gBAAI,KAAK,eAAe,CAAC,KAAK,kBAAkB;AAC9C,kBAAI,KAAK,qBAAqB;AAC5B,qBAAK,oBAAoB,KAAK;AAAA,cAChC,OAAO;AACL,qBAAK,kBAAkB,KAAK;AAAA,cAC9B;AAAA,YACF,WAAW,CAAC,KAAK,kBAAkB;AACjC,mBAAK,kBAAkB,KAAK;AAAA,YAC9B;AAAA,UACF;AAEA,kBAAQ,SAAS,MAAM;AACrB,iBAAK,KAAK,KAAK;AAAA,UACjB,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,MAEA,QAAQ,UAAU;AAChB,YAAI,UAAU;AACZ,eAAK,SAAS,QAAQ;AACtB;AAAA,QACF;AAEA,eAAO,IAAI,KAAK,SAAS,CAAC,SAAS,WAAW;AAC5C,eAAK,SAAS,CAAC,UAAU;AACvB,gBAAI,OAAO;AACT,qBAAO,KAAK;AAAA,YACd,OAAO;AACL,sBAAQ;AAAA,YACV;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,MAEA,iBAAiB,KAAK;AAEpB,YAAI,GAAG,mCAAmC,KAAK,6BAA6B,KAAK,IAAI,CAAC;AAEtF,YAAI,GAAG,6BAA6B,KAAK,uBAAuB,KAAK,IAAI,CAAC;AAE1E,YAAI,GAAG,sBAAsB,KAAK,gBAAgB,KAAK,IAAI,CAAC;AAC5D,YAAI,GAAG,8BAA8B,KAAK,wBAAwB,KAAK,IAAI,CAAC;AAC5E,YAAI,GAAG,2BAA2B,KAAK,qBAAqB,KAAK,IAAI,CAAC;AACtE,YAAI,GAAG,kBAAkB,KAAK,sBAAsB,KAAK,IAAI,CAAC;AAC9D,YAAI,GAAG,SAAS,KAAK,kBAAkB,KAAK,IAAI,CAAC;AACjD,YAAI,GAAG,gBAAgB,KAAK,oBAAoB,KAAK,IAAI,CAAC;AAC1D,YAAI,GAAG,iBAAiB,KAAK,qBAAqB,KAAK,IAAI,CAAC;AAC5D,YAAI,GAAG,UAAU,KAAK,cAAc,KAAK,IAAI,CAAC;AAC9C,YAAI,GAAG,kBAAkB,KAAK,sBAAsB,KAAK,IAAI,CAAC;AAC9D,YAAI,GAAG,WAAW,KAAK,eAAe,KAAK,IAAI,CAAC;AAChD,YAAI,GAAG,mBAAmB,KAAK,uBAAuB,KAAK,IAAI,CAAC;AAChE,YAAI,GAAG,cAAc,KAAK,kBAAkB,KAAK,IAAI,CAAC;AACtD,YAAI,GAAG,mBAAmB,KAAK,uBAAuB,KAAK,IAAI,CAAC;AAChE,YAAI,GAAG,iBAAiB,KAAK,qBAAqB,KAAK,IAAI,CAAC;AAC5D,YAAI,GAAG,kBAAkB,KAAK,sBAAsB,KAAK,IAAI,CAAC;AAC9D,YAAI,GAAG,YAAY,KAAK,gBAAgB,KAAK,IAAI,CAAC;AAClD,YAAI,GAAG,gBAAgB,KAAK,oBAAoB,KAAK,IAAI,CAAC;AAAA,MAC5D;AAAA;AAAA;AAAA,MAIA,aAAa,IAAI;AACf,cAAM,MAAM,KAAK;AACjB,YAAI,OAAO,KAAK,aAAa,YAAY;AACvC,eAAK,SACF,QAAQ,EACR,KAAK,MAAM,KAAK,SAAS,CAAC,EAC1B,KAAK,CAAC,SAAS;AACd,gBAAI,SAAS,QAAW;AACtB,kBAAI,OAAO,SAAS,UAAU;AAC5B,oBAAI,KAAK,SAAS,IAAI,UAAU,2BAA2B,CAAC;AAC5D;AAAA,cACF;AACA,mBAAK,qBAAqB,WAAW,KAAK,WAAW;AAAA,YACvD,OAAO;AACL,mBAAK,qBAAqB,WAAW,KAAK,WAAW;AAAA,YACvD;AACA,eAAG;AAAA,UACL,CAAC,EACA,MAAM,CAAC,QAAQ;AACd,gBAAI,KAAK,SAAS,GAAG;AAAA,UACvB,CAAC;AAAA,QACL,WAAW,KAAK,aAAa,MAAM;AACjC,aAAG;AAAA,QACL,OAAO;AACL,cAAI;AACF,kBAAM,SAAS;AACf,mBAAO,KAAK,sBAAsB,CAAC,SAAS;AAC1C,kBAAI,WAAc,MAAM;AACtB,qBAAK,qBAAqB,WAAW,KAAK,WAAW;AAAA,cACvD;AACA,iBAAG;AAAA,YACL,CAAC;AAAA,UACH,SAAS,GAAG;AACV,iBAAK,KAAK,SAAS,CAAC;AAAA,UACtB;AAAA,QACF;AAAA,MACF;AAAA,MAEA,6BAA6B,KAAK;AAChC,aAAK,aAAa,MAAM;AACtB,eAAK,WAAW,SAAS,KAAK,QAAQ;AAAA,QACxC,CAAC;AAAA,MACH;AAAA,MAEA,uBAAuB,KAAK;AAC1B,aAAK,aAAa,YAAY;AAC5B,cAAI;AACF,kBAAM,iBAAiB,MAAM,OAAO,wBAAwB,KAAK,MAAM,KAAK,UAAU,IAAI,IAAI;AAC9F,iBAAK,WAAW,SAAS,cAAc;AAAA,UACzC,SAAS,GAAG;AACV,iBAAK,KAAK,SAAS,CAAC;AAAA,UACtB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MAEA,gBAAgB,KAAK;AACnB,aAAK,aAAa,MAAM;AACtB,cAAI;AACF,iBAAK,cAAc,KAAK,aAAa,IAAI,YAAY,KAAK,wBAAwB,KAAK,WAAW,MAAM;AACxG,iBAAK,WAAW,+BAA+B,KAAK,YAAY,WAAW,KAAK,YAAY,QAAQ;AAAA,UACtG,SAAS,KAAK;AACZ,iBAAK,WAAW,KAAK,SAAS,GAAG;AAAA,UACnC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MAEA,MAAM,wBAAwB,KAAK;AACjC,YAAI;AACF,gBAAM,KAAK;AAAA,YACT,KAAK;AAAA,YACL,KAAK;AAAA,YACL,IAAI;AAAA,YACJ,KAAK,wBAAwB,KAAK,WAAW;AAAA,UAC/C;AACA,eAAK,WAAW,4BAA4B,KAAK,YAAY,QAAQ;AAAA,QACvE,SAAS,KAAK;AACZ,eAAK,WAAW,KAAK,SAAS,GAAG;AAAA,QACnC;AAAA,MACF;AAAA,MAEA,qBAAqB,KAAK;AACxB,YAAI;AACF,eAAK,gBAAgB,KAAK,aAAa,IAAI,IAAI;AAC/C,eAAK,cAAc;AAAA,QACrB,SAAS,KAAK;AACZ,eAAK,WAAW,KAAK,SAAS,GAAG;AAAA,QACnC;AAAA,MACF;AAAA,MAEA,sBAAsB,KAAK;AACzB,aAAK,YAAY,IAAI;AACrB,aAAK,YAAY,IAAI;AAAA,MACvB;AAAA,MAEA,qBAAqB,KAAK;AACxB,YAAI,KAAK,aAAa;AACpB,eAAK,cAAc;AACnB,eAAK,aAAa;AAClB,uBAAa,KAAK,uBAAuB;AAGzC,cAAI,KAAK,qBAAqB;AAC5B,iBAAK,oBAAoB,MAAM,IAAI;AAGnC,iBAAK,sBAAsB;AAAA,UAC7B;AACA,eAAK,KAAK,SAAS;AAAA,QACrB;AACA,cAAM,EAAE,YAAY,IAAI;AACxB,aAAK,cAAc;AACnB,aAAK,gBAAgB;AACrB,YAAI,aAAa;AACf,sBAAY,oBAAoB,KAAK,UAAU;AAAA,QACjD;AACA,aAAK,iBAAiB;AAAA,MACxB;AAAA;AAAA;AAAA,MAIA,4BAA4B,KAAK;AAC/B,YAAI,KAAK,kBAAkB;AAEzB;AAAA,QACF;AACA,aAAK,mBAAmB;AACxB,qBAAa,KAAK,uBAAuB;AACzC,YAAI,KAAK,qBAAqB;AAC5B,iBAAO,KAAK,oBAAoB,GAAG;AAAA,QACrC;AACA,aAAK,KAAK,SAAS,GAAG;AAAA,MACxB;AAAA;AAAA;AAAA;AAAA,MAKA,kBAAkB,KAAK;AACrB,YAAI,KAAK,aAAa;AACpB,iBAAO,KAAK,4BAA4B,GAAG;AAAA,QAC7C;AACA,aAAK,aAAa;AAClB,aAAK,iBAAiB,GAAG;AACzB,aAAK,KAAK,SAAS,GAAG;AAAA,MACxB;AAAA;AAAA,MAGA,oBAAoB,KAAK;AACvB,YAAI,KAAK,aAAa;AACpB,iBAAO,KAAK,4BAA4B,GAAG;AAAA,QAC7C;AACA,cAAM,cAAc,KAAK;AAEzB,YAAI,CAAC,aAAa;AAChB,eAAK,kBAAkB,GAAG;AAC1B;AAAA,QACF;AAEA,aAAK,cAAc;AACnB,oBAAY,YAAY,KAAK,KAAK,UAAU;AAAA,MAC9C;AAAA,MAEA,sBAAsB,KAAK;AAEzB,aAAK,YAAY,qBAAqB,GAAG;AAAA,MAC3C;AAAA,MAEA,eAAe,KAAK;AAElB,aAAK,YAAY,cAAc,GAAG;AAAA,MACpC;AAAA,MAEA,uBAAuB,KAAK;AAE1B,aAAK,YAAY,sBAAsB,KAAK,UAAU;AAAA,MACxD;AAAA,MAEA,kBAAkB,KAAK;AAErB,aAAK,YAAY,iBAAiB,KAAK,UAAU;AAAA,MACnD;AAAA,MAEA,uBAAuB,KAAK;AAC1B,YAAI,KAAK,eAAe,MAAM;AAC5B,gBAAM,QAAQ,IAAI,MAAM,2DAA2D;AACnF,eAAK,kBAAkB,KAAK;AAC5B;AAAA,QACF;AAEA,aAAK,YAAY,sBAAsB,KAAK,KAAK,UAAU;AAAA,MAC7D;AAAA,MAEA,uBAAuB;AACrB,YAAI,KAAK,eAAe,MAAM;AAC5B,gBAAM,QAAQ,IAAI,MAAM,yDAAyD;AACjF,eAAK,kBAAkB,KAAK;AAC5B;AAAA,QACF;AAIA,YAAI,KAAK,YAAY,MAAM;AACzB,eAAK,WAAW,iBAAiB,KAAK,YAAY,IAAI,IAAI,KAAK,YAAY;AAAA,QAC7E;AAAA,MACF;AAAA,MAEA,sBAAsB,KAAK;AACzB,aAAK,YAAY,qBAAqB,KAAK,UAAU;AAAA,MACvD;AAAA,MAEA,gBAAgB,KAAK;AACnB,aAAK,YAAY,eAAe,KAAK,KAAK,UAAU;AAAA,MACtD;AAAA,MAEA,oBAAoB,KAAK;AACvB,aAAK,KAAK,gBAAgB,GAAG;AAAA,MAC/B;AAAA,MAEA,cAAc,KAAK;AACjB,aAAK,KAAK,UAAU,GAAG;AAAA,MACzB;AAAA,MAEA,iBAAiB;AACf,cAAM,SAAS,KAAK;AAEpB,cAAM,OAAO;AAAA,UACX,MAAM,OAAO;AAAA,UACb,UAAU,OAAO;AAAA,QACnB;AAEA,cAAM,UAAU,OAAO,oBAAoB,OAAO;AAClD,YAAI,SAAS;AACX,eAAK,mBAAmB;AAAA,QAC1B;AACA,YAAI,OAAO,aAAa;AACtB,eAAK,cAAc,KAAK,OAAO;AAAA,QACjC;AACA,YAAI,OAAO,mBAAmB;AAC5B,eAAK,oBAAoB,OAAO,SAAS,OAAO,mBAAmB,EAAE,CAAC;AAAA,QACxE;AACA,YAAI,OAAO,cAAc;AACvB,eAAK,eAAe,OAAO,SAAS,OAAO,cAAc,EAAE,CAAC;AAAA,QAC9D;AACA,YAAI,OAAO,qCAAqC;AAC9C,eAAK,sCAAsC,OAAO,SAAS,OAAO,qCAAqC,EAAE,CAAC;AAAA,QAC5G;AACA,YAAI,OAAO,SAAS;AAClB,eAAK,UAAU,OAAO;AAAA,QACxB;AAEA,eAAO;AAAA,MACT;AAAA,MAEA,OAAO,QAAQ,OAAO;AACpB,YAAI,OAAO,gBAAgB,OAAO;AAChC,gBAAM,MAAM,KAAK;AAEjB,cAAI,KAAK,QAAQ,KAAK,KAAK,QAAQ,GAAG,MAAM,GAAG;AAC7C,gBAAI,QAAQ,KAAK,OAAO,eAAe,KAAK,IAAI;AAAA,UAClD,OAAO;AACL,gBAAI,QAAQ,KAAK,MAAM,KAAK,IAAI;AAAA,UAClC;AAGA,cAAI,GAAG,WAAW,WAAY;AAC5B,gBAAI,OAAO,OAAO,WAAW,OAAO,SAAS;AAAA,UAC/C,CAAC;AAAA,QACH,WAAW,OAAO,WAAW,QAAQ,KAAK,MAAM,IAAI;AAClD,iBAAO,WAAW,OAAO,OAAO,WAAW,QAAQ,KAAK,GAAG,CAAC;AAAA,QAC9D;AAAA,MACF;AAAA,MAEA,cAAc,KAAK,QAAQ,SAAS;AAClC,eAAO,KAAK,OAAO,cAAc,KAAK,QAAQ,OAAO;AAAA,MACvD;AAAA,MAEA,cAAc,KAAK,QAAQ;AACzB,eAAO,KAAK,OAAO,cAAc,KAAK,MAAM;AAAA,MAC9C;AAAA;AAAA;AAAA;AAAA,MAKA,iBAAiB,KAAK;AACpB,eAAO,MAAM,iBAAiB,GAAG;AAAA,MACnC;AAAA,MAEA,cAAc,KAAK;AACjB,eAAO,MAAM,cAAc,GAAG;AAAA,MAChC;AAAA,MAEA,mBAAmB;AACjB,YAAI,KAAK,kBAAkB,MAAM;AAC/B,eAAK,cAAc,KAAK,WAAW,MAAM;AACzC,cAAI,KAAK,aAAa;AACpB,iBAAK,gBAAgB;AACrB,iBAAK,cAAc;AAEnB,kBAAM,aAAa,KAAK,YAAY,OAAO,KAAK,UAAU;AAC1D,gBAAI,YAAY;AACd,sBAAQ,SAAS,MAAM;AACrB,qBAAK,YAAY,YAAY,YAAY,KAAK,UAAU;AACxD,qBAAK,gBAAgB;AACrB,qBAAK,iBAAiB;AAAA,cACxB,CAAC;AAAA,YACH;AAAA,UACF,WAAW,KAAK,aAAa;AAC3B,iBAAK,cAAc;AACnB,iBAAK,KAAK,OAAO;AAAA,UACnB;AAAA,QACF;AAAA,MACF;AAAA,MAEA,MAAM,QAAQ,QAAQ,UAAU;AAE9B,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI;AAEJ,YAAI,WAAW,QAAQ,WAAW,QAAW;AAC3C,gBAAM,IAAI,UAAU,6CAA6C;AAAA,QACnE,WAAW,OAAO,OAAO,WAAW,YAAY;AAC9C,wBAAc,OAAO,iBAAiB,KAAK,qBAAqB;AAChE,mBAAS,QAAQ;AACjB,cAAI,OAAO,WAAW,YAAY;AAChC,kBAAM,WAAW,MAAM,YAAY;AAAA,UACrC;AAAA,QACF,OAAO;AACL,wBAAc,OAAO,iBAAiB,KAAK,qBAAqB;AAChE,kBAAQ,IAAID,OAAM,QAAQ,QAAQ,QAAQ;AAC1C,cAAI,CAAC,MAAM,UAAU;AACnB,qBAAS,IAAI,KAAK,SAAS,CAAC,SAAS,WAAW;AAC9C,oBAAM,WAAW,CAAC,KAAK,QAAS,MAAM,OAAO,GAAG,IAAI,QAAQ,GAAG;AAAA,YACjE,CAAC,EAAE,MAAM,CAAC,QAAQ;AAGhB,oBAAM,kBAAkB,GAAG;AAC3B,oBAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF;AAEA,YAAI,aAAa;AACf,0BAAgB,MAAM;AAEtB,6BAAmB,WAAW,MAAM;AAClC,kBAAM,QAAQ,IAAI,MAAM,oBAAoB;AAE5C,oBAAQ,SAAS,MAAM;AACrB,oBAAM,YAAY,OAAO,KAAK,UAAU;AAAA,YAC1C,CAAC;AAED,0BAAc,KAAK;AAInB,kBAAM,WAAW,MAAM;AAAA,YAAC;AAGxB,kBAAM,QAAQ,KAAK,WAAW,QAAQ,KAAK;AAC3C,gBAAI,QAAQ,IAAI;AACd,mBAAK,WAAW,OAAO,OAAO,CAAC;AAAA,YACjC;AAEA,iBAAK,iBAAiB;AAAA,UACxB,GAAG,WAAW;AAEd,gBAAM,WAAW,CAAC,KAAK,QAAQ;AAC7B,yBAAa,gBAAgB;AAC7B,0BAAc,KAAK,GAAG;AAAA,UACxB;AAAA,QACF;AAEA,YAAI,KAAK,UAAU,CAAC,MAAM,QAAQ;AAChC,gBAAM,SAAS;AAAA,QACjB;AAEA,YAAI,MAAM,WAAW,CAAC,MAAM,QAAQ,QAAQ;AAC1C,gBAAM,QAAQ,SAAS,KAAK;AAAA,QAC9B;AAEA,YAAI,CAAC,KAAK,YAAY;AACpB,kBAAQ,SAAS,MAAM;AACrB,kBAAM,YAAY,IAAI,MAAM,gEAAgE,GAAG,KAAK,UAAU;AAAA,UAChH,CAAC;AACD,iBAAO;AAAA,QACT;AAEA,YAAI,KAAK,SAAS;AAChB,kBAAQ,SAAS,MAAM;AACrB,kBAAM,YAAY,IAAI,MAAM,wCAAwC,GAAG,KAAK,UAAU;AAAA,UACxF,CAAC;AACD,iBAAO;AAAA,QACT;AAEA,aAAK,WAAW,KAAK,KAAK;AAC1B,aAAK,iBAAiB;AACtB,eAAO;AAAA,MACT;AAAA,MAEA,MAAM;AACJ,aAAK,WAAW,IAAI;AAAA,MACtB;AAAA,MAEA,QAAQ;AACN,aAAK,WAAW,MAAM;AAAA,MACxB;AAAA,MAEA,IAAI,IAAI;AACN,aAAK,UAAU;AAGf,YAAI,CAAC,KAAK,WAAW,eAAe,KAAK,QAAQ;AAC/C,cAAI,IAAI;AACN,eAAG;AAAA,UACL,OAAO;AACL,mBAAO,KAAK,SAAS,QAAQ;AAAA,UAC/B;AAAA,QACF;AAEA,YAAI,KAAK,eAAe,CAAC,KAAK,YAAY;AAGxC,eAAK,WAAW,OAAO,QAAQ;AAAA,QACjC,OAAO;AACL,eAAK,WAAW,IAAI;AAAA,QACtB;AAEA,YAAI,IAAI;AACN,eAAK,WAAW,KAAK,OAAO,EAAE;AAAA,QAChC,OAAO;AACL,iBAAO,IAAI,KAAK,SAAS,CAAC,YAAY;AACpC,iBAAK,WAAW,KAAK,OAAO,OAAO;AAAA,UACrC,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAGA,IAAAG,QAAO,QAAQH;AAEf,WAAO,UAAUG;AAAA;AAAA;;;ACzoBjB;AAAA;AAAA;AACA,QAAM,eAAe,iBAAkB;AAEvC,QAAM,OAAO,WAAY;AAAA,IAAC;AAE1B,QAAM,cAAc,CAAC,MAAM,cAAc;AACvC,YAAM,IAAI,KAAK,UAAU,SAAS;AAElC,aAAO,MAAM,KAAK,SAAY,KAAK,OAAO,GAAG,CAAC,EAAE,CAAC;AAAA,IACnD;AAEA,QAAM,WAAN,MAAe;AAAA,MACb,YAAY,QAAQ,cAAc,WAAW;AAC3C,aAAK,SAAS;AACd,aAAK,eAAe;AACpB,aAAK,YAAY;AAAA,MACnB;AAAA,IACF;AAEA,QAAM,cAAN,MAAkB;AAAA,MAChB,YAAY,UAAU;AACpB,aAAK,WAAW;AAAA,MAClB;AAAA,IACF;AAEA,aAAS,uBAAuB;AAC9B,YAAM,IAAI,MAAM,uEAAuE;AAAA,IACzF;AAEA,aAAS,UAAUC,UAAS,UAAU;AACpC,UAAI,UAAU;AACZ,eAAO,EAAE,UAAoB,QAAQ,OAAU;AAAA,MACjD;AACA,UAAI;AACJ,UAAI;AACJ,YAAM,KAAK,SAAU,KAAK,QAAQ;AAChC,cAAM,IAAI,GAAG,IAAI,IAAI,MAAM;AAAA,MAC7B;AACA,YAAM,SAAS,IAAIA,SAAQ,SAAU,SAAS,QAAQ;AACpD,cAAM;AACN,cAAM;AAAA,MACR,CAAC,EAAE,MAAM,CAAC,QAAQ;AAGhB,cAAM,kBAAkB,GAAG;AAC3B,cAAM;AAAA,MACR,CAAC;AACD,aAAO,EAAE,UAAU,IAAI,OAAe;AAAA,IACxC;AAEA,aAAS,iBAAiB,MAAM,QAAQ;AACtC,aAAO,SAAS,aAAa,KAAK;AAChC,YAAI,SAAS;AAEb,eAAO,eAAe,SAAS,YAAY;AAC3C,eAAO,GAAG,SAAS,MAAM;AACvB,eAAK,IAAI,4DAA4D,GAAG;AAAA,QAC1E,CAAC;AACD,aAAK,QAAQ,MAAM;AAGnB,aAAK,KAAK,SAAS,KAAK,MAAM;AAAA,MAChC;AAAA,IACF;AAEA,QAAMC,QAAN,cAAmB,aAAa;AAAA,MAC9B,YAAY,SAASC,SAAQ;AAC3B,cAAM;AACN,aAAK,UAAU,OAAO,OAAO,CAAC,GAAG,OAAO;AAExC,YAAI,WAAW,QAAQ,cAAc,SAAS;AAG5C,iBAAO,eAAe,KAAK,SAAS,YAAY;AAAA,YAC9C,cAAc;AAAA,YACd,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,OAAO,QAAQ;AAAA,UACjB,CAAC;AAAA,QACH;AACA,YAAI,WAAW,QAAQ,QAAQ,OAAO,QAAQ,IAAI,KAAK;AAGrD,iBAAO,eAAe,KAAK,QAAQ,KAAK,OAAO;AAAA,YAC7C,YAAY;AAAA,UACd,CAAC;AAAA,QACH;AAEA,aAAK,QAAQ,MAAM,KAAK,QAAQ,OAAO,KAAK,QAAQ,YAAY;AAChE,aAAK,QAAQ,MAAM,KAAK,QAAQ,OAAO;AACvC,aAAK,QAAQ,UAAU,KAAK,QAAQ,WAAW;AAC/C,aAAK,QAAQ,kBAAkB,KAAK,QAAQ,mBAAmB;AAC/D,aAAK,QAAQ,qBAAqB,KAAK,QAAQ,sBAAsB;AACrE,aAAK,MAAM,KAAK,QAAQ,OAAO,WAAY;AAAA,QAAC;AAC5C,aAAK,SAAS,KAAK,QAAQ,UAAUA,WAAU,eAAc;AAC7D,aAAK,UAAU,KAAK,QAAQ,WAAW,OAAO;AAE9C,YAAI,OAAO,KAAK,QAAQ,sBAAsB,aAAa;AACzD,eAAK,QAAQ,oBAAoB;AAAA,QACnC;AAEA,aAAK,WAAW,CAAC;AACjB,aAAK,QAAQ,CAAC;AACd,aAAK,WAAW,oBAAI,QAAQ;AAC5B,aAAK,gBAAgB,CAAC;AACtB,aAAK,eAAe;AACpB,aAAK,SAAS;AACd,aAAK,QAAQ;AAAA,MACf;AAAA,MAEA,UAAU;AACR,eAAO,KAAK,SAAS,UAAU,KAAK,QAAQ;AAAA,MAC9C;AAAA,MAEA,cAAc;AACZ,eAAO,KAAK,SAAS,SAAS,KAAK,QAAQ;AAAA,MAC7C;AAAA,MAEA,cAAc;AACZ,aAAK,IAAI,aAAa;AACtB,YAAI,KAAK,OAAO;AACd,eAAK,IAAI,mBAAmB;AAC5B;AAAA,QACF;AACA,YAAI,KAAK,QAAQ;AACf,eAAK,IAAI,uBAAuB;AAChC,cAAI,KAAK,MAAM,QAAQ;AACrB,iBAAK,MAAM,MAAM,EAAE,IAAI,CAAC,SAAS;AAC/B,mBAAK,QAAQ,KAAK,MAAM;AAAA,YAC1B,CAAC;AAAA,UACH;AACA,cAAI,CAAC,KAAK,SAAS,QAAQ;AACzB,iBAAK,QAAQ;AACb,iBAAK,aAAa;AAAA,UACpB;AACA;AAAA,QACF;AAGA,YAAI,CAAC,KAAK,cAAc,QAAQ;AAC9B,eAAK,IAAI,oBAAoB;AAC7B;AAAA,QACF;AAEA,YAAI,CAAC,KAAK,MAAM,UAAU,KAAK,QAAQ,GAAG;AACxC;AAAA,QACF;AACA,cAAM,cAAc,KAAK,cAAc,MAAM;AAC7C,YAAI,KAAK,MAAM,QAAQ;AACrB,gBAAM,WAAW,KAAK,MAAM,IAAI;AAChC,uBAAa,SAAS,SAAS;AAC/B,gBAAM,SAAS,SAAS;AACxB,iBAAO,OAAO,OAAO,IAAI;AACzB,gBAAM,eAAe,SAAS;AAE9B,iBAAO,KAAK,eAAe,QAAQ,aAAa,cAAc,KAAK;AAAA,QACrE;AACA,YAAI,CAAC,KAAK,QAAQ,GAAG;AACnB,iBAAO,KAAK,UAAU,WAAW;AAAA,QACnC;AACA,cAAM,IAAI,MAAM,sBAAsB;AAAA,MACxC;AAAA,MAEA,QAAQ,QAAQ,UAAU;AACxB,cAAM,UAAU,YAAY,KAAK,OAAO,CAAC,SAAS,KAAK,WAAW,MAAM;AAExE,YAAI,YAAY,QAAW;AACzB,uBAAa,QAAQ,SAAS;AAAA,QAChC;AAEA,aAAK,WAAW,KAAK,SAAS,OAAO,CAAC,MAAM,MAAM,MAAM;AACxD,cAAM,UAAU;AAChB,eAAO,IAAI,MAAM;AACf,kBAAQ,KAAK,UAAU,MAAM;AAE7B,cAAI,OAAO,aAAa,YAAY;AAClC,qBAAS;AAAA,UACX;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MAEA,QAAQ,IAAI;AACV,YAAI,KAAK,QAAQ;AACf,gBAAM,MAAM,IAAI,MAAM,iDAAiD;AACvE,iBAAO,KAAK,GAAG,GAAG,IAAI,KAAK,QAAQ,OAAO,GAAG;AAAA,QAC/C;AAEA,cAAM,WAAW,UAAU,KAAK,SAAS,EAAE;AAC3C,cAAM,SAAS,SAAS;AAGxB,YAAI,KAAK,QAAQ,KAAK,KAAK,MAAM,QAAQ;AAEvC,cAAI,KAAK,MAAM,QAAQ;AACrB,oBAAQ,SAAS,MAAM,KAAK,YAAY,CAAC;AAAA,UAC3C;AAEA,cAAI,CAAC,KAAK,QAAQ,yBAAyB;AACzC,iBAAK,cAAc,KAAK,IAAI,YAAY,SAAS,QAAQ,CAAC;AAC1D,mBAAO;AAAA,UACT;AAEA,gBAAM,gBAAgB,CAAC,KAAK,KAAK,SAAS;AACxC,yBAAa,GAAG;AAChB,qBAAS,SAAS,KAAK,KAAK,IAAI;AAAA,UAClC;AAEA,gBAAM,cAAc,IAAI,YAAY,aAAa;AAGjD,gBAAM,MAAM,WAAW,MAAM;AAG3B,wBAAY,KAAK,eAAe,CAAC,MAAM,EAAE,aAAa,aAAa;AACnE,wBAAY,WAAW;AACvB,qBAAS,SAAS,IAAI,MAAM,yCAAyC,CAAC;AAAA,UACxE,GAAG,KAAK,QAAQ,uBAAuB;AAEvC,cAAI,IAAI,OAAO;AACb,gBAAI,MAAM;AAAA,UACZ;AAEA,eAAK,cAAc,KAAK,WAAW;AACnC,iBAAO;AAAA,QACT;AAEA,aAAK,UAAU,IAAI,YAAY,SAAS,QAAQ,CAAC;AAEjD,eAAO;AAAA,MACT;AAAA,MAEA,UAAU,aAAa;AACrB,cAAM,SAAS,IAAI,KAAK,OAAO,KAAK,OAAO;AAC3C,aAAK,SAAS,KAAK,MAAM;AACzB,cAAM,eAAe,iBAAiB,MAAM,MAAM;AAElD,aAAK,IAAI,yBAAyB;AAGlC,YAAI;AACJ,YAAI,aAAa;AACjB,YAAI,KAAK,QAAQ,yBAAyB;AACxC,gBAAM,WAAW,MAAM;AACrB,iBAAK,IAAI,8BAA8B;AACvC,yBAAa;AAEb,mBAAO,aAAa,OAAO,WAAW,OAAO,QAAQ,IAAI,OAAO,IAAI;AAAA,UACtE,GAAG,KAAK,QAAQ,uBAAuB;AAAA,QACzC;AAEA,aAAK,IAAI,uBAAuB;AAChC,eAAO,QAAQ,CAAC,QAAQ;AACtB,cAAI,KAAK;AACP,yBAAa,GAAG;AAAA,UAClB;AACA,iBAAO,GAAG,SAAS,YAAY;AAC/B,cAAI,KAAK;AACP,iBAAK,IAAI,4BAA4B,GAAG;AAExC,iBAAK,WAAW,KAAK,SAAS,OAAO,CAAC,MAAM,MAAM,MAAM;AACxD,gBAAI,YAAY;AACd,oBAAM,IAAI,MAAM,mDAAmD,EAAE,OAAO,IAAI,CAAC;AAAA,YACnF;AAGA,iBAAK,YAAY;AAEjB,gBAAI,CAAC,YAAY,UAAU;AACzB,0BAAY,SAAS,KAAK,QAAW,IAAI;AAAA,YAC3C;AAAA,UACF,OAAO;AACL,iBAAK,IAAI,sBAAsB;AAE/B,gBAAI,KAAK,QAAQ,uBAAuB,GAAG;AACzC,oBAAM,qBAAqB,WAAW,MAAM;AAC1C,qBAAK,IAAI,uCAAuC;AAChD,qBAAK,SAAS,IAAI,MAAM;AACxB,sBAAM,YAAY,KAAK,MAAM,UAAU,CAAC,aAAa,SAAS,WAAW,MAAM;AAC/E,oBAAI,cAAc,IAAI;AACpB,uBAAK;AAAA,oBACH;AAAA,oBACA,IAAI,YAAY,CAACC,MAAKC,SAAQ,kBAAkB,cAAc,CAAC;AAAA,oBAC/D;AAAA,oBACA;AAAA,kBACF;AAAA,gBACF;AAAA,cACF,GAAG,KAAK,QAAQ,qBAAqB,GAAI;AAEzC,iCAAmB,MAAM;AACzB,qBAAO,KAAK,OAAO,MAAM,aAAa,kBAAkB,CAAC;AAAA,YAC3D;AAEA,mBAAO,KAAK,eAAe,QAAQ,aAAa,cAAc,IAAI;AAAA,UACpE;AAAA,QACF,CAAC;AAAA,MACH;AAAA;AAAA,MAGA,eAAe,QAAQ,aAAa,cAAc,OAAO;AACvD,YAAI,OAAO;AACT,eAAK,KAAK,WAAW,MAAM;AAAA,QAC7B;AAEA,aAAK,KAAK,WAAW,MAAM;AAE3B,eAAO,UAAU,KAAK,aAAa,QAAQ,YAAY;AAEvD,eAAO,eAAe,SAAS,YAAY;AAE3C,YAAI,CAAC,YAAY,UAAU;AACzB,cAAI,SAAS,KAAK,QAAQ,QAAQ;AAChC,iBAAK,QAAQ,OAAO,QAAQ,CAAC,QAAQ;AACnC,kBAAI,KAAK;AACP,uBAAO,QAAQ,GAAG;AAClB,uBAAO,YAAY,SAAS,KAAK,QAAW,IAAI;AAAA,cAClD;AAEA,0BAAY,SAAS,QAAW,QAAQ,OAAO,OAAO;AAAA,YACxD,CAAC;AAAA,UACH,OAAO;AACL,wBAAY,SAAS,QAAW,QAAQ,OAAO,OAAO;AAAA,UACxD;AAAA,QACF,OAAO;AACL,cAAI,SAAS,KAAK,QAAQ,QAAQ;AAChC,iBAAK,QAAQ,OAAO,QAAQ,OAAO,OAAO;AAAA,UAC5C,OAAO;AACL,mBAAO,QAAQ;AAAA,UACjB;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA,aAAa,QAAQ,cAAc;AACjC,YAAI,WAAW;AAEf,eAAO,CAAC,QAAQ;AACd,cAAI,UAAU;AACZ,iCAAqB;AAAA,UACvB;AAEA,qBAAW;AACX,eAAK,SAAS,QAAQ,cAAc,GAAG;AAAA,QACzC;AAAA,MACF;AAAA;AAAA;AAAA,MAIA,SAAS,QAAQ,cAAc,KAAK;AAClC,eAAO,GAAG,SAAS,YAAY;AAE/B,eAAO,iBAAiB,OAAO,iBAAiB,KAAK;AAErD,aAAK,KAAK,WAAW,KAAK,MAAM;AAGhC,YAAI,OAAO,KAAK,UAAU,CAAC,OAAO,cAAc,OAAO,WAAW,OAAO,iBAAiB,KAAK,QAAQ,SAAS;AAC9G,cAAI,OAAO,iBAAiB,KAAK,QAAQ,SAAS;AAChD,iBAAK,IAAI,wBAAwB;AAAA,UACnC;AAEA,iBAAO,KAAK,QAAQ,QAAQ,KAAK,YAAY,KAAK,IAAI,CAAC;AAAA,QACzD;AAEA,cAAM,YAAY,KAAK,SAAS,IAAI,MAAM;AAC1C,YAAI,WAAW;AACb,eAAK,IAAI,uBAAuB;AAChC,eAAK,SAAS,OAAO,MAAM;AAC3B,iBAAO,KAAK,QAAQ,QAAQ,KAAK,YAAY,KAAK,IAAI,CAAC;AAAA,QACzD;AAGA,YAAI;AACJ,YAAI,KAAK,QAAQ,qBAAqB,KAAK,YAAY,GAAG;AACxD,gBAAM,WAAW,MAAM;AACrB,iBAAK,IAAI,oBAAoB;AAC7B,iBAAK,QAAQ,QAAQ,KAAK,YAAY,KAAK,IAAI,CAAC;AAAA,UAClD,GAAG,KAAK,QAAQ,iBAAiB;AAEjC,cAAI,KAAK,QAAQ,iBAAiB;AAEhC,gBAAI,MAAM;AAAA,UACZ;AAAA,QACF;AAEA,YAAI,KAAK,QAAQ,iBAAiB;AAChC,iBAAO,MAAM;AAAA,QACf;AAEA,aAAK,MAAM,KAAK,IAAI,SAAS,QAAQ,cAAc,GAAG,CAAC;AACvD,aAAK,YAAY;AAAA,MACnB;AAAA,MAEA,MAAM,MAAM,QAAQ,IAAI;AAEtB,YAAI,OAAO,SAAS,YAAY;AAC9B,gBAAMC,YAAW,UAAU,KAAK,SAAS,IAAI;AAC7C,uBAAa,WAAY;AACvB,mBAAOA,UAAS,SAAS,IAAI,MAAM,0EAA0E,CAAC;AAAA,UAChH,CAAC;AACD,iBAAOA,UAAS;AAAA,QAClB;AAGA,YAAI,OAAO,WAAW,YAAY;AAChC,eAAK;AACL,mBAAS;AAAA,QACX;AACA,cAAM,WAAW,UAAU,KAAK,SAAS,EAAE;AAC3C,aAAK,SAAS;AAEd,aAAK,QAAQ,CAAC,KAAK,WAAW;AAC5B,cAAI,KAAK;AACP,mBAAO,GAAG,GAAG;AAAA,UACf;AAEA,cAAI,iBAAiB;AACrB,gBAAM,UAAU,CAACF,SAAQ;AACvB,gBAAI,gBAAgB;AAClB;AAAA,YACF;AACA,6BAAiB;AACjB,mBAAO,QAAQA,IAAG;AAClB,eAAGA,IAAG;AAAA,UACR;AAEA,iBAAO,KAAK,SAAS,OAAO;AAC5B,eAAK,IAAI,mBAAmB;AAC5B,cAAI;AACF,mBAAO,MAAM,MAAM,QAAQ,CAACA,MAAK,QAAQ;AACvC,mBAAK,IAAI,kBAAkB;AAC3B,qBAAO,eAAe,SAAS,OAAO;AACtC,kBAAI,gBAAgB;AAClB;AAAA,cACF;AACA,+BAAiB;AACjB,qBAAO,QAAQA,IAAG;AAClB,kBAAIA,MAAK;AACP,uBAAO,GAAGA,IAAG;AAAA,cACf;AACA,qBAAO,GAAG,QAAW,GAAG;AAAA,YAC1B,CAAC;AAAA,UACH,SAASA,MAAK;AACZ,mBAAO,QAAQA,IAAG;AAClB,mBAAO,GAAGA,IAAG;AAAA,UACf;AAAA,QACF,CAAC;AACD,eAAO,SAAS;AAAA,MAClB;AAAA,MAEA,IAAI,IAAI;AACN,aAAK,IAAI,QAAQ;AACjB,YAAI,KAAK,QAAQ;AACf,gBAAM,MAAM,IAAI,MAAM,mCAAmC;AACzD,iBAAO,KAAK,GAAG,GAAG,IAAI,KAAK,QAAQ,OAAO,GAAG;AAAA,QAC/C;AACA,aAAK,SAAS;AACd,cAAM,WAAW,UAAU,KAAK,SAAS,EAAE;AAC3C,aAAK,eAAe,SAAS;AAC7B,aAAK,YAAY;AACjB,eAAO,SAAS;AAAA,MAClB;AAAA,MAEA,IAAI,eAAe;AACjB,eAAO,KAAK,cAAc;AAAA,MAC5B;AAAA,MAEA,IAAI,YAAY;AACd,eAAO,KAAK,MAAM;AAAA,MACpB;AAAA,MAEA,IAAI,eAAe;AACjB,eAAO,KAAK,SAAS,OAAO,CAAC,KAAK,WAAW,OAAO,KAAK,SAAS,IAAI,MAAM,IAAI,IAAI,IAAI,CAAC;AAAA,MAC3F;AAAA,MAEA,IAAI,aAAa;AACf,eAAO,KAAK,SAAS;AAAA,MACvB;AAAA,IACF;AACA,WAAO,UAAUF;AAAA;AAAA;;;AC9djB;AAAA;AAAA,UAAM,IAAI,MAAM,kEAAkE;AAAA;AAAA;;;ACAlF,IAAAK,iBAAA;AAAA;AAAA;AAEA,QAAM,eAAe,iBAAkB;AACvC,QAAM,OAAO;AACb,QAAM,QAAQ;AAEd,QAAM,cAAe,OAAO,UAAU,SAAU,QAAQ,QAAQ,UAAU;AACxE,mBAAa,KAAK,IAAI;AACtB,eAAS,MAAM,qBAAqB,QAAQ,QAAQ,QAAQ;AAC5D,WAAK,OAAO,OAAO;AACnB,WAAK,SAAS,OAAO;AACrB,WAAK,OAAO,OAAO;AACnB,WAAK,YAAY,OAAO;AACxB,WAAK,WAAW,OAAO;AACvB,WAAK,QAAQ;AACb,WAAK,aAAa,OAAO,YAAY;AAOrC,WAAK,iBAAiB;AACtB,WAAK;AAAA,QACH;AAAA,SACA,SAAU,OAAO;AACf,cAAI,UAAU,MAAO,MAAK,iBAAiB;AAAA,QAC7C,GAAE,KAAK,IAAI;AAAA,MACb;AAAA,IACF;AAEA,SAAK,SAAS,aAAa,YAAY;AAEvC,QAAM,gBAAgB;AAAA,MACpB,UAAU;AAAA,MACV,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,gBAAgB;AAAA,IAClB;AAEA,gBAAY,UAAU,cAAc,SAAU,KAAK;AAEjD,YAAM,SAAS,KAAK,OAAO,GAAG,kBAAkB;AAChD,UAAI,QAAQ;AACV,mBAAW,OAAO,QAAQ;AACxB,gBAAM,sBAAsB,cAAc,GAAG,KAAK;AAClD,cAAI,mBAAmB,IAAI,OAAO,GAAG;AAAA,QACvC;AAAA,MACF;AACA,UAAI,KAAK,UAAU;AACjB,aAAK,SAAS,GAAG;AAAA,MACnB,OAAO;AACL,aAAK,KAAK,SAAS,GAAG;AAAA,MACxB;AACA,WAAK,QAAQ;AAAA,IACf;AAEA,gBAAY,UAAU,OAAO,SAAU,WAAW,WAAW;AAC3D,aAAO,KAAK,YAAY,EAAE,KAAK,WAAW,SAAS;AAAA,IACrD;AAEA,gBAAY,UAAU,QAAQ,SAAU,UAAU;AAChD,aAAO,KAAK,YAAY,EAAE,MAAM,QAAQ;AAAA,IAC1C;AAEA,gBAAY,UAAU,cAAc,WAAY;AAC9C,UAAI,KAAK,SAAU,QAAO,KAAK;AAC/B,WAAK,WAAW,IAAI;AAAA,SAClB,SAAU,SAAS,QAAQ;AACzB,eAAK,MAAM,OAAO,OAAO;AACzB,eAAK,MAAM,SAAS,MAAM;AAAA,QAC5B,GAAE,KAAK,IAAI;AAAA,MACb;AACA,aAAO,KAAK;AAAA,IACd;AAEA,gBAAY,UAAU,SAAS,SAAU,QAAQ;AAC/C,WAAK,QAAQ;AACb,YAAM,OAAO;AACb,WAAK,SAAS,OAAO;AACrB,aAAO,OAAO,YAAY,KAAK;AAE/B,UAAI,QAAQ,SAAU,KAAK,MAAM,SAAS;AACxC,eAAO,OAAO,YAAY;AAC1B,qBAAa,WAAY;AACvB,eAAK,KAAK,OAAO;AAAA,QACnB,CAAC;AAGD,YAAI,KAAK;AACP,iBAAO,KAAK,YAAY,GAAG;AAAA,QAC7B;AAGA,YAAI,KAAK,gBAAgB;AACvB,cAAI,QAAQ,SAAS,GAAG;AACtB,iBAAK,QAAQ,CAAC,WAAW,MAAM;AAC7B,wBAAU,QAAQ,CAAC,QAAQ;AACzB,qBAAK,KAAK,OAAO,KAAK,QAAQ,CAAC,CAAC;AAAA,cAClC,CAAC;AAAA,YACH,CAAC;AAAA,UACH,OAAO;AACL,iBAAK,QAAQ,SAAU,KAAK;AAC1B,mBAAK,KAAK,OAAO,KAAK,OAAO;AAAA,YAC/B,CAAC;AAAA,UACH;AAAA,QACF;AAGA,aAAK,QAAQ;AACb,aAAK,KAAK,OAAO,OAAO;AACxB,YAAI,KAAK,UAAU;AACjB,eAAK,SAAS,MAAM,OAAO;AAAA,QAC7B;AAAA,MACF;AAEA,UAAI,QAAQ,QAAQ;AAClB,gBAAQ,QAAQ,OAAO,KAAK,KAAK;AAAA,MACnC;AAGA,UAAI,KAAK,MAAM;AACb,YAAI,KAAK,KAAK,SAAS,IAAI;AACzB,kBAAQ,MAAM,gEAAgE;AAC9E,kBAAQ,MAAM,wBAAwB,KAAK,MAAM,KAAK,KAAK,MAAM;AACjE,kBAAQ,MAAM,8DAA8D;AAAA,QAC9E;AACA,cAAM,UAAU,KAAK,UAAU,CAAC,GAAG,IAAI,MAAM,YAAY;AAIzD,YAAI,OAAO,aAAa,KAAK,IAAI,GAAG;AAClC,cAAI,KAAK,QAAQ,OAAO,aAAa,KAAK,IAAI,MAAM,KAAK,MAAM;AAC7D,kBAAM,MAAM,IAAI,MAAM,yCAAyC,KAAK,IAAI,sCAAsC;AAC9G,mBAAO,MAAM,GAAG;AAAA,UAClB;AACA,iBAAO,OAAO,OAAO,QAAQ,KAAK,MAAM,QAAQ,KAAK;AAAA,QACvD;AAEA,eAAO,OAAO,OAAO,QAAQ,KAAK,MAAM,KAAK,MAAM,OAAO,QAAQ,SAAU,KAAK;AAC/E,cAAI,IAAK,QAAO,MAAM,GAAG;AACzB,iBAAO,aAAa,KAAK,IAAI,IAAI,KAAK;AACtC,iBAAO,KAAK,OAAO,QAAQ,KAAK,MAAM,QAAQ,KAAK;AAAA,QACrD,CAAC;AAAA,MACH,WAAW,KAAK,QAAQ;AACtB,YAAI,CAAC,MAAM,QAAQ,KAAK,MAAM,GAAG;AAC/B,gBAAM,MAAM,IAAI,MAAM,+BAA+B;AACrD,iBAAO,MAAM,GAAG;AAAA,QAClB;AACA,cAAM,OAAO,KAAK,OAAO,IAAI,MAAM,YAAY;AAC/C,eAAO,OAAO,MAAM,KAAK,MAAM,MAAM,KAAK;AAAA,MAC5C,WAAW,KAAK,cAAc,YAAY;AACxC,eAAO,OAAO,MAAM,KAAK,MAAM,CAAC,GAAG,KAAK;AAAA,MAC1C,OAAO;AACL,eAAO,OAAO,MAAM,KAAK,MAAM,KAAK;AAAA,MACtC;AAAA,IACF;AAAA;AAAA;;;ACpKA,IAAAC,kBAAA;AAAA;AAAA;AAGA,QAAI;AAEJ,QAAI;AAEF,eAAS;AAAA,IACX,SAAS,GAAG;AACV,YAAM;AAAA,IACR;AACA,QAAMC,iBAAgB;AACtB,QAAM,eAAe,iBAAkB;AACvC,QAAM,OAAO;AACb,QAAM,uBAAuB;AAE7B,QAAM,cAAc;AAEpB,QAAMC,UAAU,OAAO,UAAU,SAAU,QAAQ;AACjD,mBAAa,KAAK,IAAI;AACtB,eAAS,UAAU,CAAC;AAEpB,WAAK,WAAW,OAAO,WAAW,OAAO;AACzC,WAAK,SAAS,IAAID,eAAc,OAAO,KAAK;AAE5C,WAAK,SAAS,IAAI,OAAO;AAAA,QACvB,OAAO,KAAK;AAAA,MACd,CAAC;AAED,WAAK,cAAc,CAAC;AACpB,WAAK,UAAU;AACf,WAAK,cAAc;AACnB,WAAK,aAAa;AAClB,WAAK,aAAa;AAIlB,YAAM,KAAM,KAAK,uBAAuB,IAAI,qBAAqB,MAAM;AACvE,UAAI,OAAO,uBAAwB,IAAG,yBAAyB,OAAO;AACtE,WAAK,OAAO,GAAG;AAIf,aAAO,eAAe,MAAM,YAAY;AAAA,QACtC,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,OAAO,GAAG;AAAA,MACZ,CAAC;AACD,WAAK,WAAW,GAAG;AACnB,WAAK,OAAO,GAAG;AACf,WAAK,OAAO,GAAG;AAGf,WAAK,eAAe,CAAC;AAAA,IACvB;AAEA,IAAAC,QAAO,QAAQ;AAEf,SAAK,SAASA,SAAQ,YAAY;AAElC,IAAAA,QAAO,UAAU,mBAAmB,SAAU,KAAK;AACjD,YAAM,eAAe,CAAC,UAAU;AAC9B,gBAAQ,SAAS,MAAM;AACrB,gBAAM,SAAS,KAAK;AACpB,gBAAM,YAAY,GAAG;AAAA,QACvB,CAAC;AAAA,MACH;AAEA,UAAI,KAAK,gBAAgB,GAAG;AAC1B,qBAAa,KAAK,YAAY;AAC9B,aAAK,eAAe;AAAA,MACtB;AAEA,WAAK,YAAY,QAAQ,YAAY;AACrC,WAAK,YAAY,SAAS;AAAA,IAC5B;AAKA,IAAAA,QAAO,UAAU,WAAW,SAAU,IAAI;AACxC,YAAM,OAAO;AAEb,UAAI,KAAK,aAAa;AACpB,gBAAQ,SAAS,MAAM,GAAG,IAAI,MAAM,+DAA+D,CAAC,CAAC;AACrG;AAAA,MACF;AAEA,WAAK,cAAc;AAEnB,WAAK,qBAAqB,yBAAyB,SAAU,KAAK,WAAW;AAC3E,YAAI,KAAK,qBAAqB,uBAAwB,aAAY,KAAK,qBAAqB;AAC5F,YAAI,IAAK,QAAO,GAAG,GAAG;AACtB,aAAK,OAAO,QAAQ,WAAW,SAAUC,MAAK;AAC5C,cAAIA,MAAK;AACP,iBAAK,OAAO,IAAI;AAChB,mBAAO,GAAGA,IAAG;AAAA,UACf;AAGA,eAAK,aAAa;AAGlB,eAAK,OAAO,GAAG,SAAS,SAAUA,MAAK;AACrC,iBAAK,aAAa;AAClB,iBAAK,iBAAiBA,IAAG;AACzB,iBAAK,KAAK,SAASA,IAAG;AAAA,UACxB,CAAC;AAED,eAAK,OAAO,GAAG,gBAAgB,SAAU,KAAK;AAC5C,iBAAK,KAAK,gBAAgB;AAAA,cACxB,SAAS,IAAI;AAAA,cACb,SAAS,IAAI;AAAA,YACf,CAAC;AAAA,UACH,CAAC;AAGD,eAAK,KAAK,SAAS;AACnB,eAAK,iBAAiB,IAAI;AAE1B,aAAG;AAAA,QACL,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAEA,IAAAD,QAAO,UAAU,UAAU,SAAU,UAAU;AAC7C,UAAI,UAAU;AACZ,aAAK,SAAS,QAAQ;AACtB;AAAA,MACF;AAEA,aAAO,IAAI,KAAK,SAAS,CAAC,SAAS,WAAW;AAC5C,aAAK,SAAS,CAAC,UAAU;AACvB,cAAI,OAAO;AACT,mBAAO,KAAK;AAAA,UACd,OAAO;AACL,oBAAQ;AAAA,UACV;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAYA,IAAAA,QAAO,UAAU,QAAQ,SAAU,QAAQ,QAAQ,UAAU;AAC3D,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,UAAI,WAAW,QAAQ,WAAW,QAAW;AAC3C,cAAM,IAAI,UAAU,6CAA6C;AAAA,MACnE,WAAW,OAAO,OAAO,WAAW,YAAY;AAC9C,sBAAc,OAAO,iBAAiB,KAAK,qBAAqB;AAChE,iBAAS,QAAQ;AAEjB,YAAI,OAAO,WAAW,YAAY;AAChC,iBAAO,WAAW;AAAA,QACpB;AAAA,MACF,OAAO;AACL,sBAAc,OAAO,iBAAiB,KAAK,qBAAqB;AAChE,gBAAQ,IAAI,YAAY,QAAQ,QAAQ,QAAQ;AAChD,YAAI,CAAC,MAAM,UAAU;AACnB,cAAI,YAAY;AAChB,mBAAS,IAAI,KAAK,SAAS,CAAC,SAAS,WAAW;AAC9C,yBAAa;AACb,wBAAY;AAAA,UACd,CAAC,EAAE,MAAM,CAAC,QAAQ;AAChB,kBAAM,kBAAkB,GAAG;AAC3B,kBAAM;AAAA,UACR,CAAC;AACD,gBAAM,WAAW,CAAC,KAAK,QAAS,MAAM,UAAU,GAAG,IAAI,WAAW,GAAG;AAAA,QACvE;AAAA,MACF;AAEA,UAAI,aAAa;AACf,wBAAgB,MAAM;AAEtB,2BAAmB,WAAW,MAAM;AAClC,gBAAM,QAAQ,IAAI,MAAM,oBAAoB;AAE5C,kBAAQ,SAAS,MAAM;AACrB,kBAAM,YAAY,OAAO,KAAK,UAAU;AAAA,UAC1C,CAAC;AAED,wBAAc,KAAK;AAInB,gBAAM,WAAW,MAAM;AAAA,UAAC;AAGxB,gBAAM,QAAQ,KAAK,YAAY,QAAQ,KAAK;AAC5C,cAAI,QAAQ,IAAI;AACd,iBAAK,YAAY,OAAO,OAAO,CAAC;AAAA,UAClC;AAEA,eAAK,iBAAiB;AAAA,QACxB,GAAG,WAAW;AAEd,cAAM,WAAW,CAAC,KAAK,QAAQ;AAC7B,uBAAa,gBAAgB;AAC7B,wBAAc,KAAK,GAAG;AAAA,QACxB;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,YAAY;AACpB,cAAM,SAAS,KAAK;AACpB,gBAAQ,SAAS,MAAM;AACrB,gBAAM,YAAY,IAAI,MAAM,gEAAgE,CAAC;AAAA,QAC/F,CAAC;AACD,eAAO;AAAA,MACT;AAEA,UAAI,KAAK,SAAS;AAChB,cAAM,SAAS,KAAK;AACpB,gBAAQ,SAAS,MAAM;AACrB,gBAAM,YAAY,IAAI,MAAM,wCAAwC,CAAC;AAAA,QACvE,CAAC;AACD,eAAO;AAAA,MACT;AAEA,WAAK,YAAY,KAAK,KAAK;AAC3B,WAAK,iBAAiB;AACtB,aAAO;AAAA,IACT;AAGA,IAAAA,QAAO,UAAU,MAAM,SAAU,IAAI;AACnC,YAAM,OAAO;AAEb,WAAK,UAAU;AAEf,UAAI,CAAC,KAAK,YAAY;AACpB,aAAK,KAAK,WAAW,KAAK,IAAI,KAAK,MAAM,EAAE,CAAC;AAAA,MAC9C;AACA,UAAI;AACJ,UAAI,CAAC,IAAI;AACP,iBAAS,IAAI,KAAK,SAAS,SAAU,SAAS,QAAQ;AACpD,eAAK,CAAC,QAAS,MAAM,OAAO,GAAG,IAAI,QAAQ;AAAA,QAC7C,CAAC;AAAA,MACH;AACA,WAAK,OAAO,IAAI,WAAY;AAC1B,aAAK,iBAAiB,IAAI,MAAM,uBAAuB,CAAC;AAExD,gBAAQ,SAAS,MAAM;AACrB,eAAK,KAAK,KAAK;AACf,cAAI,GAAI,IAAG;AAAA,QACb,CAAC;AAAA,MACH,CAAC;AACD,aAAO;AAAA,IACT;AAEA,IAAAA,QAAO,UAAU,kBAAkB,WAAY;AAC7C,aAAO,KAAK,gBAAgB,KAAK,aAAa,UAAU,WAAW,KAAK,aAAa,UAAU;AAAA,IACjG;AAEA,IAAAA,QAAO,UAAU,mBAAmB,SAAU,mBAAmB;AAC/D,UAAI,CAAC,KAAK,YAAY;AACpB;AAAA,MACF;AACA,UAAI,KAAK,gBAAgB,GAAG;AAC1B;AAAA,MACF;AACA,YAAM,QAAQ,KAAK,YAAY,MAAM;AACrC,UAAI,CAAC,OAAO;AACV,YAAI,CAAC,mBAAmB;AACtB,eAAK,KAAK,OAAO;AAAA,QACnB;AACA;AAAA,MACF;AACA,WAAK,eAAe;AACpB,YAAM,OAAO,IAAI;AACjB,YAAM,OAAO;AACb,YAAM,KAAK,SAAS,WAAY;AAC9B,aAAK,iBAAiB;AAAA,MACxB,CAAC;AAAA,IACH;AAGA,IAAAA,QAAO,UAAU,SAAS,SAAU,OAAO;AACzC,UAAI,KAAK,iBAAiB,OAAO;AAC/B,aAAK,OAAO,OAAO,WAAY;AAAA,QAAC,CAAC;AAAA,MACnC,WAAW,KAAK,YAAY,QAAQ,KAAK,MAAM,IAAI;AACjD,aAAK,YAAY,OAAO,KAAK,YAAY,QAAQ,KAAK,GAAG,CAAC;AAAA,MAC5D;AAAA,IACF;AAEA,IAAAA,QAAO,UAAU,MAAM,WAAY;AAAA,IAAC;AACpC,IAAAA,QAAO,UAAU,QAAQ,WAAY;AAAA,IAAC;AAEtC,IAAAA,QAAO,UAAU,gBAAgB,SAAU,KAAK,QAAQ,SAAS;AAC/D,aAAO,KAAK,OAAO,cAAc,KAAK,QAAQ,OAAO;AAAA,IACvD;AAEA,IAAAA,QAAO,UAAU,gBAAgB,SAAU,KAAK,QAAQ;AACtD,aAAO,KAAK,OAAO,cAAc,KAAK,MAAM;AAAA,IAC9C;AAAA;AAAA;;;ACnTA;AAAA;AAAA;AACA,WAAO,UAAU;AAAA;AAAA;;;ACDjB,IAAAE,eAAA;AAAA;AAAA;AAEA,QAAMC,UAAS;AACf,QAAMC,YAAW;AACjB,QAAMC,cAAa;AACnB,QAAMC,UAAS;AACf,QAAM,QAAQ;AACd,QAAMC,QAAO;AACb,QAAMC,iBAAgB;AACtB,QAAM,EAAE,eAAAC,eAAc,IAAI;AAC1B,QAAM,EAAE,kBAAAC,mBAAkB,eAAAC,eAAc,IAAI;AAE5C,QAAM,cAAc,CAACR,YAAW;AAC9B,aAAO,MAAM,kBAAkBI,MAAK;AAAA,QAClC,YAAY,SAAS;AACnB,gBAAM,SAASJ,OAAM;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAEA,QAAM,KAAK,SAAU,mBAAmB;AACtC,WAAK,WAAWC;AAChB,WAAK,SAAS;AACd,WAAK,QAAQ,KAAK,OAAO;AACzB,WAAK,OAAO,YAAY,KAAK,MAAM;AACnC,WAAK,SAAS,CAAC;AACf,WAAK,aAAaC;AAClB,WAAK,QAAQ;AACb,WAAK,gBAAgBI;AACrB,WAAK,gBAAgBD;AACrB,WAAK,mBAAmBE;AACxB,WAAK,gBAAgBC;AACrB,WAAK,SAASL;AACd,WAAK,QAAQ;AAAA,IACf;AAEA,QAAI,OAAO,QAAQ,IAAI,yBAAyB,aAAa;AAC3D,aAAO,UAAU,IAAI,GAAG,gBAAmB;AAAA,IAC7C,OAAO;AACL,aAAO,UAAU,IAAI,GAAGH,OAAM;AAG9B,aAAO,eAAe,OAAO,SAAS,UAAU;AAAA,QAC9C,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,MAAM;AACJ,cAAI,SAAS;AACb,cAAI;AACF,qBAAS,IAAI,GAAG,gBAAmB;AAAA,UACrC,SAAS,KAAK;AACZ,gBAAI,IAAI,SAAS,oBAAoB;AACnC,oBAAM;AAAA,YACR;AAAA,UACF;AAGA,iBAAO,eAAe,OAAO,SAAS,UAAU;AAAA,YAC9C,OAAO;AAAA,UACT,CAAC;AAED,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;;;AC9DA,iBAAe;AAGR,IAAM,SAAS,WAAAS,QAAG;AAClB,IAAM,OAAO,WAAAA,QAAG;AAChB,IAAM,aAAa,WAAAA,QAAG;AACtB,IAAM,QAAQ,WAAAA,QAAG;AACjB,IAAM,QAAQ,WAAAA,QAAG;AACjB,IAAM,gBAAgB,WAAAA,QAAG;AACzB,IAAM,mBAAmB,WAAAA,QAAG;AAC5B,IAAM,gBAAgB,WAAAA,QAAG;AACzB,IAAM,SAAS,WAAAA,QAAG;AAClB,IAAM,gBAAgB,WAAAA,QAAG;AAGzB,IAAM,WAAW,WAAAA,QAAG;AAG3B,IAAO,cAAQ,WAAAA;", "names": ["bits", "value", "elementType", "i", "defaults", "escapeIdentifier", "escapeLiteral", "require_utils", "types", "TypeOverrides", "config", "defaults", "types", "Result", "Result", "Query", "DatabaseError", "password", "query", "types", "require_stream", "getStream", "getSecureStream", "Connection", "TypeOverrides", "Query", "defaults", "Connection", "Client", "Promise", "Pool", "Client", "err", "client", "response", "require_query", "require_client", "TypeOverrides", "Client", "err", "require_lib", "Client", "defaults", "Connection", "Result", "Pool", "TypeOverrides", "DatabaseError", "escapeIdentifier", "escapeLiteral", "pg"]}