// Test States and Districts endpoints
import fetch from 'node-fetch';

const API_BASE = 'http://localhost:3001/api';

async function testStatesAndDistricts() {
  console.log('🏛️ Testing States and Districts Endpoints...');
  
  try {
    // First, ensure we have a country to work with
    console.log('\n📋 Setting up test data...');
    
    // Get or create a continent
    let continentId;
    const continentsResponse = await fetch(`${API_BASE}/continents`);
    const continents = await continentsResponse.json();
    
    if (continents.data && continents.data.length > 0) {
      continentId = continents.data[0].id;
      console.log(`✅ Using existing continent: ${continents.data[0].name}`);
    } else {
      // Create a continent
      const newContinent = {
        name: 'Test Continent for States',
        code: 'TCS',
        display_order: 1
      };
      
      const createContinentResponse = await fetch(`${API_BASE}/continents`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newContinent)
      });
      
      const createdContinent = await createContinentResponse.json();
      continentId = createdContinent.data.id;
      console.log(`✅ Created test continent: ${createdContinent.data.name}`);
    }
    
    // Get or create a country
    let countryId;
    const countriesResponse = await fetch(`${API_BASE}/countries`);
    const countries = await countriesResponse.json();
    
    if (countries.data && countries.data.length > 0) {
      countryId = countries.data[0].id;
      console.log(`✅ Using existing country: ${countries.data[0].name}`);
    } else {
      // Create a country
      const newCountry = {
        continent_id: continentId,
        name: 'Test Country for States',
        code: 'TCS',
        currency: 'TCC',
        iso_code: '+999',
        nationality: 'Test',
        display_order: 1
      };
      
      const createCountryResponse = await fetch(`${API_BASE}/countries`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newCountry)
      });
      
      const createdCountry = await createCountryResponse.json();
      countryId = createdCountry.data.id;
      console.log(`✅ Created test country: ${createdCountry.data.name}`);
    }
    
    // Test States CRUD
    console.log('\n🏛️ Testing States CRUD...');
    
    // Read states
    const statesResponse = await fetch(`${API_BASE}/states`);
    if (statesResponse.ok) {
      const states = await statesResponse.json();
      console.log(`✅ Read states: ${states.data ? states.data.length : 0} found`);
    } else {
      console.log('❌ Failed to read states');
      return;
    }
    
    // Create state
    const newState = {
      country_id: countryId,
      name: 'Test State',
      code: 'TS',
      display_order: 1
    };
    
    const createStateResponse = await fetch(`${API_BASE}/states`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(newState)
    });
    
    if (createStateResponse.ok) {
      const createdState = await createStateResponse.json();
      console.log(`✅ Created state: ${createdState.data.name}`);
      
      const stateId = createdState.data.id;
      
      // Update state
      const updateStateData = { ...newState, name: 'Updated Test State' };
      const updateStateResponse = await fetch(`${API_BASE}/states/${stateId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateStateData)
      });
      
      if (updateStateResponse.ok) {
        console.log('✅ Updated state successfully');
      } else {
        console.log('❌ Failed to update state');
      }
      
      // Test Districts CRUD
      console.log('\n🏘️ Testing Districts CRUD...');
      
      // Read districts
      const districtsResponse = await fetch(`${API_BASE}/districts`);
      if (districtsResponse.ok) {
        const districts = await districtsResponse.json();
        console.log(`✅ Read districts: ${districts.data ? districts.data.length : 0} found`);
      } else {
        console.log('❌ Failed to read districts');
      }
      
      // Create district
      const newDistrict = {
        state_id: stateId,
        name: 'Test District',
        code: 'TD',
        display_order: 1
      };
      
      const createDistrictResponse = await fetch(`${API_BASE}/districts`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newDistrict)
      });
      
      if (createDistrictResponse.ok) {
        const createdDistrict = await createDistrictResponse.json();
        console.log(`✅ Created district: ${createdDistrict.data.name}`);
        
        const districtId = createdDistrict.data.id;
        
        // Update district
        const updateDistrictData = { ...newDistrict, name: 'Updated Test District' };
        const updateDistrictResponse = await fetch(`${API_BASE}/districts/${districtId}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(updateDistrictData)
        });
        
        if (updateDistrictResponse.ok) {
          console.log('✅ Updated district successfully');
        } else {
          console.log('❌ Failed to update district');
        }
        
        // Test hierarchical queries
        console.log('\n🔗 Testing Hierarchical Queries...');
        
        // Get states by country
        const statesByCountryResponse = await fetch(`${API_BASE}/countries/${countryId}/states`);
        if (statesByCountryResponse.ok) {
          const statesByCountry = await statesByCountryResponse.json();
          console.log(`✅ States by country: ${statesByCountry.data ? statesByCountry.data.length : 0} found`);
        }
        
        // Get districts by state
        const districtsByStateResponse = await fetch(`${API_BASE}/states/${stateId}/districts`);
        if (districtsByStateResponse.ok) {
          const districtsByState = await districtsByStateResponse.json();
          console.log(`✅ Districts by state: ${districtsByState.data ? districtsByState.data.length : 0} found`);
        }
        
        // Clean up - delete test data
        console.log('\n🧹 Cleaning up test data...');
        
        // Delete district
        const deleteDistrictResponse = await fetch(`${API_BASE}/districts/${districtId}`, {
          method: 'DELETE'
        });
        if (deleteDistrictResponse.ok) {
          console.log('✅ Deleted test district');
        }
      } else {
        const error = await createDistrictResponse.json();
        console.log('❌ Failed to create district:', error.error);
      }
      
      // Delete state
      const deleteStateResponse = await fetch(`${API_BASE}/states/${stateId}`, {
        method: 'DELETE'
      });
      if (deleteStateResponse.ok) {
        console.log('✅ Deleted test state');
      }
    } else {
      const error = await createStateResponse.json();
      console.log('❌ Failed to create state:', error.error);
    }
    
    console.log('\n✅ States and Districts testing completed!');
    
  } catch (error) {
    console.log('❌ Test error:', error.message);
  }
}

testStatesAndDistricts();
