{"version": 3, "sources": ["../../file-uri-to-path/index.js", "../../bindings/bindings.js", "../../sqlite3/lib/sqlite3-binding.js", "../../sqlite3/lib/trace.js", "../../sqlite3/lib/sqlite3.js"], "sourcesContent": ["\n/**\n * Module dependencies.\n */\n\nvar sep = require('path').sep || '/';\n\n/**\n * Module exports.\n */\n\nmodule.exports = fileUriToPath;\n\n/**\n * File URI to Path function.\n *\n * @param {String} uri\n * @return {String} path\n * @api public\n */\n\nfunction fileUriToPath (uri) {\n  if ('string' != typeof uri ||\n      uri.length <= 7 ||\n      'file://' != uri.substring(0, 7)) {\n    throw new TypeError('must pass in a file:// URI to convert to a file path');\n  }\n\n  var rest = decodeURI(uri.substring(7));\n  var firstSlash = rest.indexOf('/');\n  var host = rest.substring(0, firstSlash);\n  var path = rest.substring(firstSlash + 1);\n\n  // 2.  Scheme Definition\n  // As a special case, <host> can be the string \"localhost\" or the empty\n  // string; this is interpreted as \"the machine from which the URL is\n  // being interpreted\".\n  if ('localhost' == host) host = '';\n\n  if (host) {\n    host = sep + sep + host;\n  }\n\n  // 3.2  Drives, drive letters, mount points, file system root\n  // Drive letters are mapped into the top of a file URI in various ways,\n  // depending on the implementation; some applications substitute\n  // vertical bar (\"|\") for the colon after the drive letter, yielding\n  // \"file:///c|/tmp/test.txt\".  In some cases, the colon is left\n  // unchanged, as in \"file:///c:/tmp/test.txt\".  In other cases, the\n  // colon is simply omitted, as in \"file:///c/tmp/test.txt\".\n  path = path.replace(/^(.+)\\|/, '$1:');\n\n  // for Windows, we need to invert the path separators from what a URI uses\n  if (sep == '\\\\') {\n    path = path.replace(/\\//g, '\\\\');\n  }\n\n  if (/^.+\\:/.test(path)) {\n    // has Windows drive at beginning of path\n  } else {\n    // unix path…\n    path = sep + path;\n  }\n\n  return host + path;\n}\n", "/**\n * Module dependencies.\n */\n\nvar fs = require('fs'),\n  path = require('path'),\n  fileURLToPath = require('file-uri-to-path'),\n  join = path.join,\n  dirname = path.dirname,\n  exists =\n    (fs.accessSync &&\n      function(path) {\n        try {\n          fs.accessSync(path);\n        } catch (e) {\n          return false;\n        }\n        return true;\n      }) ||\n    fs.existsSync ||\n    path.existsSync,\n  defaults = {\n    arrow: process.env.NODE_BINDINGS_ARROW || ' → ',\n    compiled: process.env.NODE_BINDINGS_COMPILED_DIR || 'compiled',\n    platform: process.platform,\n    arch: process.arch,\n    nodePreGyp:\n      'node-v' +\n      process.versions.modules +\n      '-' +\n      process.platform +\n      '-' +\n      process.arch,\n    version: process.versions.node,\n    bindings: 'bindings.node',\n    try: [\n      // node-gyp's linked version in the \"build\" dir\n      ['module_root', 'build', 'bindings'],\n      // node-waf and gyp_addon (a.k.a node-gyp)\n      ['module_root', 'build', 'Debug', 'bindings'],\n      ['module_root', 'build', 'Release', 'bindings'],\n      // Debug files, for development (legacy behavior, remove for node v0.9)\n      ['module_root', 'out', 'Debug', 'bindings'],\n      ['module_root', 'Debug', 'bindings'],\n      // Release files, but manually compiled (legacy behavior, remove for node v0.9)\n      ['module_root', 'out', 'Release', 'bindings'],\n      ['module_root', 'Release', 'bindings'],\n      // Legacy from node-waf, node <= 0.4.x\n      ['module_root', 'build', 'default', 'bindings'],\n      // Production \"Release\" buildtype binary (meh...)\n      ['module_root', 'compiled', 'version', 'platform', 'arch', 'bindings'],\n      // node-qbs builds\n      ['module_root', 'addon-build', 'release', 'install-root', 'bindings'],\n      ['module_root', 'addon-build', 'debug', 'install-root', 'bindings'],\n      ['module_root', 'addon-build', 'default', 'install-root', 'bindings'],\n      // node-pre-gyp path ./lib/binding/{node_abi}-{platform}-{arch}\n      ['module_root', 'lib', 'binding', 'nodePreGyp', 'bindings']\n    ]\n  };\n\n/**\n * The main `bindings()` function loads the compiled bindings for a given module.\n * It uses V8's Error API to determine the parent filename that this function is\n * being invoked from, which is then used to find the root directory.\n */\n\nfunction bindings(opts) {\n  // Argument surgery\n  if (typeof opts == 'string') {\n    opts = { bindings: opts };\n  } else if (!opts) {\n    opts = {};\n  }\n\n  // maps `defaults` onto `opts` object\n  Object.keys(defaults).map(function(i) {\n    if (!(i in opts)) opts[i] = defaults[i];\n  });\n\n  // Get the module root\n  if (!opts.module_root) {\n    opts.module_root = exports.getRoot(exports.getFileName());\n  }\n\n  // Ensure the given bindings name ends with .node\n  if (path.extname(opts.bindings) != '.node') {\n    opts.bindings += '.node';\n  }\n\n  // https://github.com/webpack/webpack/issues/4175#issuecomment-342931035\n  var requireFunc =\n    typeof __webpack_require__ === 'function'\n      ? __non_webpack_require__\n      : require;\n\n  var tries = [],\n    i = 0,\n    l = opts.try.length,\n    n,\n    b,\n    err;\n\n  for (; i < l; i++) {\n    n = join.apply(\n      null,\n      opts.try[i].map(function(p) {\n        return opts[p] || p;\n      })\n    );\n    tries.push(n);\n    try {\n      b = opts.path ? requireFunc.resolve(n) : requireFunc(n);\n      if (!opts.path) {\n        b.path = n;\n      }\n      return b;\n    } catch (e) {\n      if (e.code !== 'MODULE_NOT_FOUND' &&\n          e.code !== 'QUALIFIED_PATH_RESOLUTION_FAILED' &&\n          !/not find/i.test(e.message)) {\n        throw e;\n      }\n    }\n  }\n\n  err = new Error(\n    'Could not locate the bindings file. Tried:\\n' +\n      tries\n        .map(function(a) {\n          return opts.arrow + a;\n        })\n        .join('\\n')\n  );\n  err.tries = tries;\n  throw err;\n}\nmodule.exports = exports = bindings;\n\n/**\n * Gets the filename of the JavaScript file that invokes this function.\n * Used to help find the root directory of a module.\n * Optionally accepts an filename argument to skip when searching for the invoking filename\n */\n\nexports.getFileName = function getFileName(calling_file) {\n  var origPST = Error.prepareStackTrace,\n    origSTL = Error.stackTraceLimit,\n    dummy = {},\n    fileName;\n\n  Error.stackTraceLimit = 10;\n\n  Error.prepareStackTrace = function(e, st) {\n    for (var i = 0, l = st.length; i < l; i++) {\n      fileName = st[i].getFileName();\n      if (fileName !== __filename) {\n        if (calling_file) {\n          if (fileName !== calling_file) {\n            return;\n          }\n        } else {\n          return;\n        }\n      }\n    }\n  };\n\n  // run the 'prepareStackTrace' function above\n  Error.captureStackTrace(dummy);\n  dummy.stack;\n\n  // cleanup\n  Error.prepareStackTrace = origPST;\n  Error.stackTraceLimit = origSTL;\n\n  // handle filename that starts with \"file://\"\n  var fileSchema = 'file://';\n  if (fileName.indexOf(fileSchema) === 0) {\n    fileName = fileURLToPath(fileName);\n  }\n\n  return fileName;\n};\n\n/**\n * Gets the root directory of a module, given an arbitrary filename\n * somewhere in the module tree. The \"root directory\" is the directory\n * containing the `package.json` file.\n *\n *   In:  /home/<USER>/node-native-module/lib/index.js\n *   Out: /home/<USER>/node-native-module\n */\n\nexports.getRoot = function getRoot(file) {\n  var dir = dirname(file),\n    prev;\n  while (true) {\n    if (dir === '.') {\n      // Avoids an infinite loop in rare cases, like the REPL\n      dir = process.cwd();\n    }\n    if (\n      exists(join(dir, 'package.json')) ||\n      exists(join(dir, 'node_modules'))\n    ) {\n      // Found the 'package.json' file or 'node_modules' dir; we're done\n      return dir;\n    }\n    if (prev === dir) {\n      // Got to the top\n      throw new Error(\n        'Could not find module root given file: \"' +\n          file +\n          '\". Do you have a `package.json` file? '\n      );\n    }\n    // Try the parent dir next\n    prev = dir;\n    dir = join(dir, '..');\n  }\n};\n", "module.exports = require('bindings')('node_sqlite3.node');\n", "// Inspired by https://github.com/tlrobinson/long-stack-traces\nconst util = require('util');\n\nfunction extendTrace(object, property, pos) {\n    const old = object[property];\n    object[property] = function() {\n        const error = new Error();\n        const name = object.constructor.name + '#' + property + '(' +\n            Array.prototype.slice.call(arguments).map(function(el) {\n                return util.inspect(el, false, 0);\n            }).join(', ') + ')';\n\n        if (typeof pos === 'undefined') pos = -1;\n        if (pos < 0) pos += arguments.length;\n        const cb = arguments[pos];\n        if (typeof arguments[pos] === 'function') {\n            arguments[pos] = function replacement() {\n                const err = arguments[0];\n                if (err && err.stack && !err.__augmented) {\n                    err.stack = filter(err).join('\\n');\n                    err.stack += '\\n--> in ' + name;\n                    err.stack += '\\n' + filter(error).slice(1).join('\\n');\n                    err.__augmented = true;\n                }\n                return cb.apply(this, arguments);\n            };\n        }\n        return old.apply(this, arguments);\n    };\n}\nexports.extendTrace = extendTrace;\n\n\nfunction filter(error) {\n    return error.stack.split('\\n').filter(function(line) {\n        return line.indexOf(__filename) < 0;\n    });\n}\n", "const path = require('path');\nconst sqlite3 = require('./sqlite3-binding.js');\nconst EventEmitter = require('events').EventEmitter;\nmodule.exports = exports = sqlite3;\n\nfunction normalizeMethod (fn) {\n    return function (sql) {\n        let errBack;\n        const args = Array.prototype.slice.call(arguments, 1);\n\n        if (typeof args[args.length - 1] === 'function') {\n            const callback = args[args.length - 1];\n            errBack = function(err) {\n                if (err) {\n                    callback(err);\n                }\n            };\n        }\n        const statement = new Statement(this, sql, errBack);\n        return fn.call(this, statement, args);\n    };\n}\n\nfunction inherits(target, source) {\n    for (const k in source.prototype)\n        target.prototype[k] = source.prototype[k];\n}\n\nsqlite3.cached = {\n    Database: function(file, a, b) {\n        if (file === '' || file === ':memory:') {\n            // Don't cache special databases.\n            return new Database(file, a, b);\n        }\n\n        let db;\n        file = path.resolve(file);\n\n        if (!sqlite3.cached.objects[file]) {\n            db = sqlite3.cached.objects[file] = new Database(file, a, b);\n        }\n        else {\n            // Make sure the callback is called.\n            db = sqlite3.cached.objects[file];\n            const callback = (typeof a === 'number') ? b : a;\n            if (typeof callback === 'function') {\n                function cb() { callback.call(db, null); }\n                if (db.open) process.nextTick(cb);\n                else db.once('open', cb);\n            }\n        }\n\n        return db;\n    },\n    objects: {}\n};\n\n\nconst Database = sqlite3.Database;\nconst Statement = sqlite3.Statement;\nconst Backup = sqlite3.Backup;\n\ninherits(Database, EventEmitter);\ninherits(Statement, EventEmitter);\ninherits(Backup, EventEmitter);\n\n// Database#prepare(sql, [bind1, bind2, ...], [callback])\nDatabase.prototype.prepare = normalizeMethod(function(statement, params) {\n    return params.length\n        ? statement.bind.apply(statement, params)\n        : statement;\n});\n\n// Database#run(sql, [bind1, bind2, ...], [callback])\nDatabase.prototype.run = normalizeMethod(function(statement, params) {\n    statement.run.apply(statement, params).finalize();\n    return this;\n});\n\n// Database#get(sql, [bind1, bind2, ...], [callback])\nDatabase.prototype.get = normalizeMethod(function(statement, params) {\n    statement.get.apply(statement, params).finalize();\n    return this;\n});\n\n// Database#all(sql, [bind1, bind2, ...], [callback])\nDatabase.prototype.all = normalizeMethod(function(statement, params) {\n    statement.all.apply(statement, params).finalize();\n    return this;\n});\n\n// Database#each(sql, [bind1, bind2, ...], [callback], [complete])\nDatabase.prototype.each = normalizeMethod(function(statement, params) {\n    statement.each.apply(statement, params).finalize();\n    return this;\n});\n\nDatabase.prototype.map = normalizeMethod(function(statement, params) {\n    statement.map.apply(statement, params).finalize();\n    return this;\n});\n\n// Database#backup(filename, [callback])\n// Database#backup(filename, destName, sourceName, filenameIsDest, [callback])\nDatabase.prototype.backup = function() {\n    let backup;\n    if (arguments.length <= 2) {\n        // By default, we write the main database out to the main database of the named file.\n        // This is the most likely use of the backup api.\n        backup = new Backup(this, arguments[0], 'main', 'main', true, arguments[1]);\n    } else {\n        // Otherwise, give the user full control over the sqlite3_backup_init arguments.\n        backup = new Backup(this, arguments[0], arguments[1], arguments[2], arguments[3], arguments[4]);\n    }\n    // Per the sqlite docs, exclude the following errors as non-fatal by default.\n    backup.retryErrors = [sqlite3.BUSY, sqlite3.LOCKED];\n    return backup;\n};\n\nStatement.prototype.map = function() {\n    const params = Array.prototype.slice.call(arguments);\n    const callback = params.pop();\n    params.push(function(err, rows) {\n        if (err) return callback(err);\n        const result = {};\n        if (rows.length) {\n            const keys = Object.keys(rows[0]);\n            const key = keys[0];\n            if (keys.length > 2) {\n                // Value is an object\n                for (let i = 0; i < rows.length; i++) {\n                    result[rows[i][key]] = rows[i];\n                }\n            } else {\n                const value = keys[1];\n                // Value is a plain value\n                for (let i = 0; i < rows.length; i++) {\n                    result[rows[i][key]] = rows[i][value];\n                }\n            }\n        }\n        callback(err, result);\n    });\n    return this.all.apply(this, params);\n};\n\nlet isVerbose = false;\n\nconst supportedEvents = [ 'trace', 'profile', 'change' ];\n\nDatabase.prototype.addListener = Database.prototype.on = function(type) {\n    const val = EventEmitter.prototype.addListener.apply(this, arguments);\n    if (supportedEvents.indexOf(type) >= 0) {\n        this.configure(type, true);\n    }\n    return val;\n};\n\nDatabase.prototype.removeListener = function(type) {\n    const val = EventEmitter.prototype.removeListener.apply(this, arguments);\n    if (supportedEvents.indexOf(type) >= 0 && !this._events[type]) {\n        this.configure(type, false);\n    }\n    return val;\n};\n\nDatabase.prototype.removeAllListeners = function(type) {\n    const val = EventEmitter.prototype.removeAllListeners.apply(this, arguments);\n    if (supportedEvents.indexOf(type) >= 0) {\n        this.configure(type, false);\n    }\n    return val;\n};\n\n// Save the stack trace over EIO callbacks.\nsqlite3.verbose = function() {\n    if (!isVerbose) {\n        const trace = require('./trace');\n        [\n            'prepare',\n            'get',\n            'run',\n            'all',\n            'each',\n            'map',\n            'close',\n            'exec'\n        ].forEach(function (name) {\n            trace.extendTrace(Database.prototype, name);\n        });\n        [\n            'bind',\n            'get',\n            'run',\n            'all',\n            'each',\n            'map',\n            'reset',\n            'finalize',\n        ].forEach(function (name) {\n            trace.extendTrace(Statement.prototype, name);\n        });\n        isVerbose = true;\n    }\n\n    return sqlite3;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAKA,QAAI,MAAM,eAAgB,OAAO;AAMjC,WAAO,UAAU;AAUjB,aAAS,cAAe,KAAK;AAC3B,UAAI,YAAY,OAAO,OACnB,IAAI,UAAU,KACd,aAAa,IAAI,UAAU,GAAG,CAAC,GAAG;AACpC,cAAM,IAAI,UAAU,sDAAsD;AAAA,MAC5E;AAEA,UAAI,OAAO,UAAU,IAAI,UAAU,CAAC,CAAC;AACrC,UAAI,aAAa,KAAK,QAAQ,GAAG;AACjC,UAAI,OAAO,KAAK,UAAU,GAAG,UAAU;AACvC,UAAI,OAAO,KAAK,UAAU,aAAa,CAAC;AAMxC,UAAI,eAAe,KAAM,QAAO;AAEhC,UAAI,MAAM;AACR,eAAO,MAAM,MAAM;AAAA,MACrB;AASA,aAAO,KAAK,QAAQ,WAAW,KAAK;AAGpC,UAAI,OAAO,MAAM;AACf,eAAO,KAAK,QAAQ,OAAO,IAAI;AAAA,MACjC;AAEA,UAAI,QAAQ,KAAK,IAAI,GAAG;AAAA,MAExB,OAAO;AAEL,eAAO,MAAM;AAAA,MACf;AAEA,aAAO,OAAO;AAAA,IAChB;AAAA;AAAA;;;ACjEA;AAAA;AAIA,QAAI,KAAK;AAAT,QACE,OAAO;AADT,QAEE,gBAAgB;AAFlB,QAGE,OAAO,KAAK;AAHd,QAIE,UAAU,KAAK;AAJjB,QAKE,SACG,GAAG,cACF,SAASA,OAAM;AACb,UAAI;AACF,WAAG,WAAWA,KAAI;AAAA,MACpB,SAAS,GAAG;AACV,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,KACF,GAAG,cACH,KAAK;AAhBT,QAiBE,WAAW;AAAA,MACT,OAAO,QAAQ,IAAI,uBAAuB;AAAA,MAC1C,UAAU,QAAQ,IAAI,8BAA8B;AAAA,MACpD,UAAU,QAAQ;AAAA,MAClB,MAAM,QAAQ;AAAA,MACd,YACE,WACA,QAAQ,SAAS,UACjB,MACA,QAAQ,WACR,MACA,QAAQ;AAAA,MACV,SAAS,QAAQ,SAAS;AAAA,MAC1B,UAAU;AAAA,MACV,KAAK;AAAA;AAAA,QAEH,CAAC,eAAe,SAAS,UAAU;AAAA;AAAA,QAEnC,CAAC,eAAe,SAAS,SAAS,UAAU;AAAA,QAC5C,CAAC,eAAe,SAAS,WAAW,UAAU;AAAA;AAAA,QAE9C,CAAC,eAAe,OAAO,SAAS,UAAU;AAAA,QAC1C,CAAC,eAAe,SAAS,UAAU;AAAA;AAAA,QAEnC,CAAC,eAAe,OAAO,WAAW,UAAU;AAAA,QAC5C,CAAC,eAAe,WAAW,UAAU;AAAA;AAAA,QAErC,CAAC,eAAe,SAAS,WAAW,UAAU;AAAA;AAAA,QAE9C,CAAC,eAAe,YAAY,WAAW,YAAY,QAAQ,UAAU;AAAA;AAAA,QAErE,CAAC,eAAe,eAAe,WAAW,gBAAgB,UAAU;AAAA,QACpE,CAAC,eAAe,eAAe,SAAS,gBAAgB,UAAU;AAAA,QAClE,CAAC,eAAe,eAAe,WAAW,gBAAgB,UAAU;AAAA;AAAA,QAEpE,CAAC,eAAe,OAAO,WAAW,cAAc,UAAU;AAAA,MAC5D;AAAA,IACF;AAQF,aAAS,SAAS,MAAM;AAEtB,UAAI,OAAO,QAAQ,UAAU;AAC3B,eAAO,EAAE,UAAU,KAAK;AAAA,MAC1B,WAAW,CAAC,MAAM;AAChB,eAAO,CAAC;AAAA,MACV;AAGA,aAAO,KAAK,QAAQ,EAAE,IAAI,SAASC,IAAG;AACpC,YAAI,EAAEA,MAAK,MAAO,MAAKA,EAAC,IAAI,SAASA,EAAC;AAAA,MACxC,CAAC;AAGD,UAAI,CAAC,KAAK,aAAa;AACrB,aAAK,cAAc,QAAQ,QAAQ,QAAQ,YAAY,CAAC;AAAA,MAC1D;AAGA,UAAI,KAAK,QAAQ,KAAK,QAAQ,KAAK,SAAS;AAC1C,aAAK,YAAY;AAAA,MACnB;AAGA,UAAI,cACF,OAAO,wBAAwB,aAC3B,0BACA;AAEN,UAAI,QAAQ,CAAC,GACX,IAAI,GACJ,IAAI,KAAK,IAAI,QACb,GACA,GACA;AAEF,aAAO,IAAI,GAAG,KAAK;AACjB,YAAI,KAAK;AAAA,UACP;AAAA,UACA,KAAK,IAAI,CAAC,EAAE,IAAI,SAAS,GAAG;AAC1B,mBAAO,KAAK,CAAC,KAAK;AAAA,UACpB,CAAC;AAAA,QACH;AACA,cAAM,KAAK,CAAC;AACZ,YAAI;AACF,cAAI,KAAK,OAAO,YAAY,QAAQ,CAAC,IAAI,YAAY,CAAC;AACtD,cAAI,CAAC,KAAK,MAAM;AACd,cAAE,OAAO;AAAA,UACX;AACA,iBAAO;AAAA,QACT,SAAS,GAAG;AACV,cAAI,EAAE,SAAS,sBACX,EAAE,SAAS,sCACX,CAAC,YAAY,KAAK,EAAE,OAAO,GAAG;AAChC,kBAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,YAAM,IAAI;AAAA,QACR,iDACE,MACG,IAAI,SAAS,GAAG;AACf,iBAAO,KAAK,QAAQ;AAAA,QACtB,CAAC,EACA,KAAK,IAAI;AAAA,MAChB;AACA,UAAI,QAAQ;AACZ,YAAM;AAAA,IACR;AACA,WAAO,UAAU,UAAU;AAQ3B,YAAQ,cAAc,SAAS,YAAY,cAAc;AACvD,UAAI,UAAU,MAAM,mBAClB,UAAU,MAAM,iBAChB,QAAQ,CAAC,GACT;AAEF,YAAM,kBAAkB;AAExB,YAAM,oBAAoB,SAAS,GAAG,IAAI;AACxC,iBAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,IAAI,GAAG,KAAK;AACzC,qBAAW,GAAG,CAAC,EAAE,YAAY;AAC7B,cAAI,aAAa,YAAY;AAC3B,gBAAI,cAAc;AAChB,kBAAI,aAAa,cAAc;AAC7B;AAAA,cACF;AAAA,YACF,OAAO;AACL;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAGA,YAAM,kBAAkB,KAAK;AAC7B,YAAM;AAGN,YAAM,oBAAoB;AAC1B,YAAM,kBAAkB;AAGxB,UAAI,aAAa;AACjB,UAAI,SAAS,QAAQ,UAAU,MAAM,GAAG;AACtC,mBAAW,cAAc,QAAQ;AAAA,MACnC;AAEA,aAAO;AAAA,IACT;AAWA,YAAQ,UAAU,SAAS,QAAQ,MAAM;AACvC,UAAI,MAAM,QAAQ,IAAI,GACpB;AACF,aAAO,MAAM;AACX,YAAI,QAAQ,KAAK;AAEf,gBAAM,QAAQ,IAAI;AAAA,QACpB;AACA,YACE,OAAO,KAAK,KAAK,cAAc,CAAC,KAChC,OAAO,KAAK,KAAK,cAAc,CAAC,GAChC;AAEA,iBAAO;AAAA,QACT;AACA,YAAI,SAAS,KAAK;AAEhB,gBAAM,IAAI;AAAA,YACR,6CACE,OACA;AAAA,UACJ;AAAA,QACF;AAEA,eAAO;AACP,cAAM,KAAK,KAAK,IAAI;AAAA,MACtB;AAAA,IACF;AAAA;AAAA;;;AC5NA;AAAA;AAAA,WAAO,UAAU,mBAAoB,mBAAmB;AAAA;AAAA;;;ACAxD;AAAA;AACA,QAAM,OAAO;AAEb,aAAS,YAAY,QAAQ,UAAU,KAAK;AACxC,YAAM,MAAM,OAAO,QAAQ;AAC3B,aAAO,QAAQ,IAAI,WAAW;AAC1B,cAAM,QAAQ,IAAI,MAAM;AACxB,cAAM,OAAO,OAAO,YAAY,OAAO,MAAM,WAAW,MACpD,MAAM,UAAU,MAAM,KAAK,SAAS,EAAE,IAAI,SAAS,IAAI;AACnD,iBAAO,KAAK,QAAQ,IAAI,OAAO,CAAC;AAAA,QACpC,CAAC,EAAE,KAAK,IAAI,IAAI;AAEpB,YAAI,OAAO,QAAQ,YAAa,OAAM;AACtC,YAAI,MAAM,EAAG,QAAO,UAAU;AAC9B,cAAM,KAAK,UAAU,GAAG;AACxB,YAAI,OAAO,UAAU,GAAG,MAAM,YAAY;AACtC,oBAAU,GAAG,IAAI,SAAS,cAAc;AACpC,kBAAM,MAAM,UAAU,CAAC;AACvB,gBAAI,OAAO,IAAI,SAAS,CAAC,IAAI,aAAa;AACtC,kBAAI,QAAQ,OAAO,GAAG,EAAE,KAAK,IAAI;AACjC,kBAAI,SAAS,cAAc;AAC3B,kBAAI,SAAS,OAAO,OAAO,KAAK,EAAE,MAAM,CAAC,EAAE,KAAK,IAAI;AACpD,kBAAI,cAAc;AAAA,YACtB;AACA,mBAAO,GAAG,MAAM,MAAM,SAAS;AAAA,UACnC;AAAA,QACJ;AACA,eAAO,IAAI,MAAM,MAAM,SAAS;AAAA,MACpC;AAAA,IACJ;AACA,YAAQ,cAAc;AAGtB,aAAS,OAAO,OAAO;AACnB,aAAO,MAAM,MAAM,MAAM,IAAI,EAAE,OAAO,SAAS,MAAM;AACjD,eAAO,KAAK,QAAQ,UAAU,IAAI;AAAA,MACtC,CAAC;AAAA,IACL;AAAA;AAAA;;;ACrCA;AAAA;AAAA,QAAM,OAAO;AACb,QAAM,UAAU;AAChB,QAAM,eAAe,iBAAkB;AACvC,WAAO,UAAU,UAAU;AAE3B,aAAS,gBAAiB,IAAI;AAC1B,aAAO,SAAU,KAAK;AAClB,YAAI;AACJ,cAAM,OAAO,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC;AAEpD,YAAI,OAAO,KAAK,KAAK,SAAS,CAAC,MAAM,YAAY;AAC7C,gBAAM,WAAW,KAAK,KAAK,SAAS,CAAC;AACrC,oBAAU,SAAS,KAAK;AACpB,gBAAI,KAAK;AACL,uBAAS,GAAG;AAAA,YAChB;AAAA,UACJ;AAAA,QACJ;AACA,cAAM,YAAY,IAAI,UAAU,MAAM,KAAK,OAAO;AAClD,eAAO,GAAG,KAAK,MAAM,WAAW,IAAI;AAAA,MACxC;AAAA,IACJ;AAEA,aAAS,SAAS,QAAQ,QAAQ;AAC9B,iBAAW,KAAK,OAAO;AACnB,eAAO,UAAU,CAAC,IAAI,OAAO,UAAU,CAAC;AAAA,IAChD;AAEA,YAAQ,SAAS;AAAA,MACb,UAAU,SAAS,MAAM,GAAG,GAAG;AAC3B,YAAI,SAAS,MAAM,SAAS,YAAY;AAEpC,iBAAO,IAAI,SAAS,MAAM,GAAG,CAAC;AAAA,QAClC;AAEA,YAAI;AACJ,eAAO,KAAK,QAAQ,IAAI;AAExB,YAAI,CAAC,QAAQ,OAAO,QAAQ,IAAI,GAAG;AAC/B,eAAK,QAAQ,OAAO,QAAQ,IAAI,IAAI,IAAI,SAAS,MAAM,GAAG,CAAC;AAAA,QAC/D,OACK;AAED,eAAK,QAAQ,OAAO,QAAQ,IAAI;AAChC,gBAAM,WAAY,OAAO,MAAM,WAAY,IAAI;AAC/C,cAAI,OAAO,aAAa,YAAY;AAChC,gBAASC,MAAT,WAAc;AAAE,uBAAS,KAAK,IAAI,IAAI;AAAA,YAAG;AAAhC,qBAAAA;AACT,gBAAI,GAAG,KAAM,SAAQ,SAASA,GAAE;AAAA,gBAC3B,IAAG,KAAK,QAAQA,GAAE;AAAA,UAC3B;AAAA,QACJ;AAEA,eAAO;AAAA,MACX;AAAA,MACA,SAAS,CAAC;AAAA,IACd;AAGA,QAAM,WAAW,QAAQ;AACzB,QAAM,YAAY,QAAQ;AAC1B,QAAM,SAAS,QAAQ;AAEvB,aAAS,UAAU,YAAY;AAC/B,aAAS,WAAW,YAAY;AAChC,aAAS,QAAQ,YAAY;AAG7B,aAAS,UAAU,UAAU,gBAAgB,SAAS,WAAW,QAAQ;AACrE,aAAO,OAAO,SACR,UAAU,KAAK,MAAM,WAAW,MAAM,IACtC;AAAA,IACV,CAAC;AAGD,aAAS,UAAU,MAAM,gBAAgB,SAAS,WAAW,QAAQ;AACjE,gBAAU,IAAI,MAAM,WAAW,MAAM,EAAE,SAAS;AAChD,aAAO;AAAA,IACX,CAAC;AAGD,aAAS,UAAU,MAAM,gBAAgB,SAAS,WAAW,QAAQ;AACjE,gBAAU,IAAI,MAAM,WAAW,MAAM,EAAE,SAAS;AAChD,aAAO;AAAA,IACX,CAAC;AAGD,aAAS,UAAU,MAAM,gBAAgB,SAAS,WAAW,QAAQ;AACjE,gBAAU,IAAI,MAAM,WAAW,MAAM,EAAE,SAAS;AAChD,aAAO;AAAA,IACX,CAAC;AAGD,aAAS,UAAU,OAAO,gBAAgB,SAAS,WAAW,QAAQ;AAClE,gBAAU,KAAK,MAAM,WAAW,MAAM,EAAE,SAAS;AACjD,aAAO;AAAA,IACX,CAAC;AAED,aAAS,UAAU,MAAM,gBAAgB,SAAS,WAAW,QAAQ;AACjE,gBAAU,IAAI,MAAM,WAAW,MAAM,EAAE,SAAS;AAChD,aAAO;AAAA,IACX,CAAC;AAID,aAAS,UAAU,SAAS,WAAW;AACnC,UAAI;AACJ,UAAI,UAAU,UAAU,GAAG;AAGvB,iBAAS,IAAI,OAAO,MAAM,UAAU,CAAC,GAAG,QAAQ,QAAQ,MAAM,UAAU,CAAC,CAAC;AAAA,MAC9E,OAAO;AAEH,iBAAS,IAAI,OAAO,MAAM,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC;AAAA,MAClG;AAEA,aAAO,cAAc,CAAC,QAAQ,MAAM,QAAQ,MAAM;AAClD,aAAO;AAAA,IACX;AAEA,cAAU,UAAU,MAAM,WAAW;AACjC,YAAM,SAAS,MAAM,UAAU,MAAM,KAAK,SAAS;AACnD,YAAM,WAAW,OAAO,IAAI;AAC5B,aAAO,KAAK,SAAS,KAAK,MAAM;AAC5B,YAAI,IAAK,QAAO,SAAS,GAAG;AAC5B,cAAM,SAAS,CAAC;AAChB,YAAI,KAAK,QAAQ;AACb,gBAAM,OAAO,OAAO,KAAK,KAAK,CAAC,CAAC;AAChC,gBAAM,MAAM,KAAK,CAAC;AAClB,cAAI,KAAK,SAAS,GAAG;AAEjB,qBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,qBAAO,KAAK,CAAC,EAAE,GAAG,CAAC,IAAI,KAAK,CAAC;AAAA,YACjC;AAAA,UACJ,OAAO;AACH,kBAAM,QAAQ,KAAK,CAAC;AAEpB,qBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,qBAAO,KAAK,CAAC,EAAE,GAAG,CAAC,IAAI,KAAK,CAAC,EAAE,KAAK;AAAA,YACxC;AAAA,UACJ;AAAA,QACJ;AACA,iBAAS,KAAK,MAAM;AAAA,MACxB,CAAC;AACD,aAAO,KAAK,IAAI,MAAM,MAAM,MAAM;AAAA,IACtC;AAEA,QAAI,YAAY;AAEhB,QAAM,kBAAkB,CAAE,SAAS,WAAW,QAAS;AAEvD,aAAS,UAAU,cAAc,SAAS,UAAU,KAAK,SAAS,MAAM;AACpE,YAAM,MAAM,aAAa,UAAU,YAAY,MAAM,MAAM,SAAS;AACpE,UAAI,gBAAgB,QAAQ,IAAI,KAAK,GAAG;AACpC,aAAK,UAAU,MAAM,IAAI;AAAA,MAC7B;AACA,aAAO;AAAA,IACX;AAEA,aAAS,UAAU,iBAAiB,SAAS,MAAM;AAC/C,YAAM,MAAM,aAAa,UAAU,eAAe,MAAM,MAAM,SAAS;AACvE,UAAI,gBAAgB,QAAQ,IAAI,KAAK,KAAK,CAAC,KAAK,QAAQ,IAAI,GAAG;AAC3D,aAAK,UAAU,MAAM,KAAK;AAAA,MAC9B;AACA,aAAO;AAAA,IACX;AAEA,aAAS,UAAU,qBAAqB,SAAS,MAAM;AACnD,YAAM,MAAM,aAAa,UAAU,mBAAmB,MAAM,MAAM,SAAS;AAC3E,UAAI,gBAAgB,QAAQ,IAAI,KAAK,GAAG;AACpC,aAAK,UAAU,MAAM,KAAK;AAAA,MAC9B;AACA,aAAO;AAAA,IACX;AAGA,YAAQ,UAAU,WAAW;AACzB,UAAI,CAAC,WAAW;AACZ,cAAM,QAAQ;AACd;AAAA,UACI;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACJ,EAAE,QAAQ,SAAU,MAAM;AACtB,gBAAM,YAAY,SAAS,WAAW,IAAI;AAAA,QAC9C,CAAC;AACD;AAAA,UACI;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACJ,EAAE,QAAQ,SAAU,MAAM;AACtB,gBAAM,YAAY,UAAU,WAAW,IAAI;AAAA,QAC/C,CAAC;AACD,oBAAY;AAAA,MAChB;AAEA,aAAO;AAAA,IACX;AAAA;AAAA;", "names": ["path", "i", "cb"]}