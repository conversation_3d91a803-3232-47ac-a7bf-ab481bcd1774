import {
  require_path
} from "./chunk-UQQ6GOXD.js";
import {
  require_crypto
} from "./chunk-W6W4ZKYN.js";
import {
  __commonJS,
  __require
} from "./chunk-TM6AOUSD.js";

// vite:cjs-external-facade:@mapbox/node-pre-gyp
import * as m from "@mapbox/node-pre-gyp";
var require_node_pre_gyp = __commonJS({
  "vite:cjs-external-facade:@mapbox/node-pre-gyp"(exports, module) {
    module.exports = m;
  }
});

// node_modules/bcrypt/promises.js
var require_promises = __commonJS({
  "node_modules/bcrypt/promises.js"(exports, module) {
    "use strict";
    var Promise2 = global.Promise;
    module.exports.promise = function(fn, context, args) {
      if (!Array.isArray(args)) {
        args = Array.prototype.slice.call(args);
      }
      if (typeof fn !== "function") {
        return Promise2.reject(new Error("fn must be a function"));
      }
      return new Promise2(function(resolve, reject) {
        args.push(function(err, data) {
          if (err) {
            reject(err);
          } else {
            resolve(data);
          }
        });
        fn.apply(context, args);
      });
    };
    module.exports.reject = function(err) {
      return Promise2.reject(err);
    };
    module.exports.use = function(promise) {
      Promise2 = promise;
    };
  }
});

// node_modules/bcrypt/bcrypt.js
var require_bcrypt = __commonJS({
  "node_modules/bcrypt/bcrypt.js"(exports, module) {
    var nodePreGyp = require_node_pre_gyp();
    var path = require_path();
    var binding_path = nodePreGyp.find(path.resolve(path.join(__dirname, "./package.json")));
    var bindings = __require(binding_path);
    var crypto = require_crypto();
    var promises = require_promises();
    module.exports.genSaltSync = function genSaltSync(rounds, minor) {
      if (!rounds) {
        rounds = 10;
      } else if (typeof rounds !== "number") {
        throw new Error("rounds must be a number");
      }
      if (!minor) {
        minor = "b";
      } else if (minor !== "b" && minor !== "a") {
        throw new Error('minor must be either "a" or "b"');
      }
      return bindings.gen_salt_sync(minor, rounds, crypto.randomBytes(16));
    };
    module.exports.genSalt = function genSalt(rounds, minor, cb) {
      var error;
      if (typeof arguments[0] === "function") {
        cb = arguments[0];
        rounds = 10;
        minor = "b";
      } else if (typeof arguments[1] === "function") {
        cb = arguments[1];
        minor = "b";
      }
      if (!cb) {
        return promises.promise(genSalt, this, [rounds, minor]);
      }
      if (!rounds) {
        rounds = 10;
      } else if (typeof rounds !== "number") {
        error = new Error("rounds must be a number");
        return process.nextTick(function() {
          cb(error);
        });
      }
      if (!minor) {
        minor = "b";
      } else if (minor !== "b" && minor !== "a") {
        error = new Error('minor must be either "a" or "b"');
        return process.nextTick(function() {
          cb(error);
        });
      }
      crypto.randomBytes(16, function(error2, randomBytes) {
        if (error2) {
          cb(error2);
          return;
        }
        bindings.gen_salt(minor, rounds, randomBytes, cb);
      });
    };
    module.exports.hashSync = function hashSync(data, salt) {
      if (data == null || salt == null) {
        throw new Error("data and salt arguments required");
      }
      if (!(typeof data === "string" || data instanceof Buffer) || typeof salt !== "string" && typeof salt !== "number") {
        throw new Error("data must be a string or Buffer and salt must either be a salt string or a number of rounds");
      }
      if (typeof salt === "number") {
        salt = module.exports.genSaltSync(salt);
      }
      return bindings.encrypt_sync(data, salt);
    };
    module.exports.hash = function hash(data, salt, cb) {
      var error;
      if (typeof data === "function") {
        error = new Error("data must be a string or Buffer and salt must either be a salt string or a number of rounds");
        return process.nextTick(function() {
          data(error);
        });
      }
      if (typeof salt === "function") {
        error = new Error("data must be a string or Buffer and salt must either be a salt string or a number of rounds");
        return process.nextTick(function() {
          salt(error);
        });
      }
      if (cb && typeof cb !== "function") {
        return promises.reject(new Error("cb must be a function or null to return a Promise"));
      }
      if (!cb) {
        return promises.promise(hash, this, [data, salt]);
      }
      if (data == null || salt == null) {
        error = new Error("data and salt arguments required");
        return process.nextTick(function() {
          cb(error);
        });
      }
      if (!(typeof data === "string" || data instanceof Buffer) || typeof salt !== "string" && typeof salt !== "number") {
        error = new Error("data must be a string or Buffer and salt must either be a salt string or a number of rounds");
        return process.nextTick(function() {
          cb(error);
        });
      }
      if (typeof salt === "number") {
        return module.exports.genSalt(salt, function(err, salt2) {
          return bindings.encrypt(data, salt2, cb);
        });
      }
      return bindings.encrypt(data, salt, cb);
    };
    module.exports.compareSync = function compareSync(data, hash) {
      if (data == null || hash == null) {
        throw new Error("data and hash arguments required");
      }
      if (!(typeof data === "string" || data instanceof Buffer) || typeof hash !== "string") {
        throw new Error("data must be a string or Buffer and hash must be a string");
      }
      return bindings.compare_sync(data, hash);
    };
    module.exports.compare = function compare(data, hash, cb) {
      var error;
      if (typeof data === "function") {
        error = new Error("data and hash arguments required");
        return process.nextTick(function() {
          data(error);
        });
      }
      if (typeof hash === "function") {
        error = new Error("data and hash arguments required");
        return process.nextTick(function() {
          hash(error);
        });
      }
      if (cb && typeof cb !== "function") {
        return promises.reject(new Error("cb must be a function or null to return a Promise"));
      }
      if (!cb) {
        return promises.promise(compare, this, [data, hash]);
      }
      if (data == null || hash == null) {
        error = new Error("data and hash arguments required");
        return process.nextTick(function() {
          cb(error);
        });
      }
      if (!(typeof data === "string" || data instanceof Buffer) || typeof hash !== "string") {
        error = new Error("data and hash must be strings");
        return process.nextTick(function() {
          cb(error);
        });
      }
      return bindings.compare(data, hash, cb);
    };
    module.exports.getRounds = function getRounds(hash) {
      if (hash == null) {
        throw new Error("hash argument required");
      }
      if (typeof hash !== "string") {
        throw new Error("hash must be a string");
      }
      return bindings.get_rounds(hash);
    };
  }
});
export default require_bcrypt();
//# sourceMappingURL=bcrypt.js.map
