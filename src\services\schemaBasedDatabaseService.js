// Schema-Based Database Service
// Maps directly to the SQL schema tables with proper relationships and CRUD operations

class SchemaBasedDatabaseService {
    constructor() {
        this.baseURL = this.getApiBaseUrl();
        this.isOnline = false;
        this.storagePrefix = 'mygroup_';
        this.initializeConnection();
        this.initializeMockData();
    }

    // Get API base URL from various sources
    getApiBaseUrl() {
        // Check for environment variables in different contexts
        if (typeof window !== 'undefined') {
            // Browser environment - check for runtime config
            if (window.env?.REACT_APP_API_URL) {
                return window.env.REACT_APP_API_URL;
            }
            if (window.ENV?.API_URL) {
                return window.ENV.API_URL;
            }
        }
        
        // Fallback to default
        return 'http://localhost:3001/api';
    }

    async initializeConnection() {
        try {
            const response = await fetch(`${this.baseURL}/health`);
            this.isOnline = response.ok;
            console.log('Database connection:', this.isOnline ? 'Online' : 'Offline');
        } catch (error) {
            this.isOnline = false;
            console.log('Database offline, using localStorage fallback');
        }
    }

    initializeMockData() {
        // Initialize roles based on schema
        this.initializeRoles();
        this.initializeGroupData();
        this.initializeUsers();
        this.initializeLocationData();
    }

    initializeRoles() {
        const existingRoles = this.getFromStorage('my_role');
        if (!existingRoles || existingRoles.length === 0) {
            const roles = [
                { id: 1, role: 'admin', role_description: 'System Administrator' },
                { id: 2, role: 'corporate', role_description: 'Corporate User' },
                { id: 3, role: 'headoffice', role_description: 'Head Office User' },
                { id: 4, role: 'regional', role_description: 'Regional Office User' },
                { id: 5, role: 'branch', role_description: 'Branch Office User' },
                { id: 6, role: 'partner', role_description: 'Partner User' },
                { id: 7, role: 'customer', role_description: 'Customer User' },
                { id: 8, role: 'app_admin', role_description: 'Application Administrator' }
            ];
            this.saveToStorage('my_role', roles);
        }
    }

    initializeGroupData() {
        const existingGroups = this.getFromStorage('my_group_data');
        if (!existingGroups || existingGroups.length === 0) {
            const groups = [
                {
                    id: 1,
                    name: 'MyGroup Corporate',
                    logo: '/assets/logos/corporate.png',
                    icon: 'corporate-icon',
                    background_color: '#1e40af',
                    color: '#ffffff',
                    group_url: 'corporate.mygroup.com',
                    group_name: 'MyGroup Corporate'
                },
                {
                    id: 2,
                    name: 'MyGroup Partners',
                    logo: '/assets/logos/partners.png',
                    icon: 'partner-icon',
                    background_color: '#059669',
                    color: '#ffffff',
                    group_url: 'partners.mygroup.com',
                    group_name: 'MyGroup Partners'
                }
            ];
            this.saveToStorage('my_group_data', groups);
        }
    }

    initializeUsers() {
        const existingUsers = this.getFromStorage('my_users');
        if (!existingUsers || existingUsers.length === 0) {
            const users = [
                {
                    id: 1,
                    user_name: 'admin',
                    password: 'admin123', // Default admin password - in production, this should be hashed
                    role_id: 1, // admin
                    group_id: 1,
                    created_on: new Date().toISOString(),
                    modified_on: new Date().toISOString(),
                    status: 1, // active
                    ip_address: '127.0.0.1',
                    last_logged_in: new Date().toISOString(),
                    mobile_number: '+1234567890',
                    email: '<EMAIL>'
                },
                {
                    id: 2,
                    user_name: 'corporate1',
                    password: 'corporate123',
                    role_id: 2, // corporate
                    group_id: 1,
                    created_on: new Date().toISOString(),
                    modified_on: new Date().toISOString(),
                    status: 1,
                    ip_address: '127.0.0.1',
                    last_logged_in: new Date().toISOString(),
                    mobile_number: '+1234567891',
                    email: '<EMAIL>'
                },
                {
                    id: 3,
                    user_name: 'headoffice1',
                    password: 'headoffice123',
                    role_id: 3, // headoffice
                    group_id: 1,
                    created_on: new Date().toISOString(),
                    modified_on: new Date().toISOString(),
                    status: 1,
                    ip_address: '127.0.0.1',
                    last_logged_in: new Date().toISOString(),
                    mobile_number: '+1234567892',
                    email: '<EMAIL>'
                },
                {
                    id: 4,
                    user_name: 'regional1',
                    password: 'regional123',
                    role_id: 4, // regional
                    group_id: 1,
                    created_on: new Date().toISOString(),
                    modified_on: new Date().toISOString(),
                    status: 1,
                    ip_address: '127.0.0.1',
                    last_logged_in: new Date().toISOString(),
                    mobile_number: '+1234567893',
                    email: '<EMAIL>'
                },
                {
                    id: 5,
                    user_name: 'branch1',
                    password: 'branch123',
                    role_id: 5, // branch
                    group_id: 1,
                    created_on: new Date().toISOString(),
                    modified_on: new Date().toISOString(),
                    status: 1,
                    ip_address: '127.0.0.1',
                    last_logged_in: new Date().toISOString(),
                    mobile_number: '+1234567894',
                    email: '<EMAIL>'
                },
                {
                    id: 6,
                    user_name: 'partner1',
                    password: 'partner123',
                    role_id: 6, // partner
                    group_id: 2, // partner group
                    created_on: new Date().toISOString(),
                    modified_on: new Date().toISOString(),
                    status: 1,
                    ip_address: '127.0.0.1',
                    last_logged_in: new Date().toISOString(),
                    mobile_number: '+1234567895',
                    email: '<EMAIL>'
                },
                {
                    id: 7,
                    user_name: 'customer1',
                    password: 'customer123',
                    role_id: 7, // customer
                    group_id: 1,
                    created_on: new Date().toISOString(),
                    modified_on: new Date().toISOString(),
                    status: 1,
                    ip_address: '127.0.0.1',
                    last_logged_in: new Date().toISOString(),
                    mobile_number: '+1234567896',
                    email: '<EMAIL>'
                }
            ];
            this.saveToStorage('my_users', users);
        }
    }

    initializeLocationData() {
        // Initialize continents
        const continents = this.getFromStorage('my_continents');
        if (!continents || continents.length === 0) {
            const mockContinents = [
                { id: 1, name: 'Asia', code: 'AS', display_order: 1 },
                { id: 2, name: 'Europe', code: 'EU', display_order: 2 },
                { id: 3, name: 'North America', code: 'NA', display_order: 3 }
            ];
            this.saveToStorage('my_continents', mockContinents);
        }

        // Initialize countries
        const countries = this.getFromStorage('my_country');
        if (!countries || countries.length === 0) {
            const mockCountries = [
                {
                    id: 1,
                    name: 'India',
                    continent_id: 1,
                    code: 'IN',
                    currency: 'INR',
                    flag: '/flags/in.png',
                    iso_code: 'IND',
                    nationality: 1,
                    display_order: 1
                },
                {
                    id: 2,
                    name: 'United States',
                    continent_id: 3,
                    code: 'US',
                    currency: 'USD',
                    flag: '/flags/us.png',
                    iso_code: 'USA',
                    nationality: 2,
                    display_order: 2
                }
            ];
            this.saveToStorage('my_country', mockCountries);
        }
    }

    // Generic API call with fallback
    async apiCall(endpoint, options = {}) {
        if (!this.isOnline) {
            throw new Error('Database offline');
        }

        const response = await fetch(`${this.baseURL}${endpoint}`, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
                ...options.headers
            },
            ...options
        });

        if (!response.ok) {
            throw new Error(`API Error: ${response.status} ${response.statusText}`);
        }

        return await response.json();
    }

    // Local storage operations
    getFromStorage(key) {
        try {
            const data = localStorage.getItem(`${this.storagePrefix}${key}`);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.error('Error reading from storage:', error);
            return null;
        }
    }

    saveToStorage(key, data) {
        try {
            localStorage.setItem(`${this.storagePrefix}${key}`, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('Error saving to storage:', error);
            return false;
        }
    }

    removeFromStorage(key) {
        try {
            localStorage.removeItem(`${this.storagePrefix}${key}`);
            return true;
        } catch (error) {
            console.error('Error removing from storage:', error);
            return false;
        }
    }

    // Authentication based on schema
    async authenticateUser(username, password) {
        try {
            if (this.isOnline) {
                const result = await this.apiCall('/auth/login', {
                    method: 'POST',
                    body: JSON.stringify({ username, password })
                });
                
                if (result.success) {
                    localStorage.setItem('authToken', result.token);
                    return {
                        success: true,
                        user: result.user
                    };
                }
                return { success: false, error: result.error };
            }
        } catch (error) {
            console.log('API authentication failed, using local fallback');
        }

        // Fallback authentication using schema structure
        return this.authenticateUserLocal(username, password);
    }

    async authenticateUserLocal(username, password) {
        const users = this.getFromStorage('my_users') || [];
        const roles = this.getFromStorage('my_role') || [];
        const groups = this.getFromStorage('my_group_data') || [];

        const user = users.find(u => u.user_name === username && u.status === 1);

        if (user) {
            // Simple password validation - in production, use proper hashing
            if (user.password !== password) {
                return { success: false, error: 'Invalid username or password' };
            }

            const userRole = roles.find(r => r.id === user.role_id);
            const userGroup = groups.find(g => g.id === user.group_id);

            // Update last login
            const userIndex = users.findIndex(u => u.id === user.id);
            if (userIndex !== -1) {
                users[userIndex].last_logged_in = new Date().toISOString();
                users[userIndex].ip_address = '127.0.0.1'; // In real app, get actual IP
                this.saveToStorage('my_users', users);
            }

            return {
                success: true,
                user: {
                    id: user.id,
                    username: user.user_name,
                    email: user.email,
                    mobile_number: user.mobile_number,
                    role: userRole?.role || 'unknown',
                    role_id: user.role_id,
                    role_description: userRole?.role_description || '',
                    group_id: user.group_id,
                    group_name: userGroup?.group_name || '',
                    last_logged_in: users[userIndex].last_logged_in
                }
            };
        }

        return { success: false, error: 'Invalid username or password' };
    }

    // User Management CRUD Operations
    async getUsers(roleFilter = null) {
        try {
            if (this.isOnline) {
                const endpoint = roleFilter ? `/users?role=${roleFilter}` : '/users';
                const result = await this.apiCall(endpoint);
                return result.users || [];
            }
        } catch (error) {
            console.log('API failed, using local storage');
        }

        // Fallback to localStorage
        const users = this.getFromStorage('my_users') || [];
        const roles = this.getFromStorage('my_role') || [];
        const groups = this.getFromStorage('my_group_data') || [];

        let filteredUsers = users;
        if (roleFilter) {
            const role = roles.find(r => r.role === roleFilter);
            if (role) {
                filteredUsers = users.filter(u => u.role_id === role.id);
            }
        }

        // Enrich users with role and group information
        return filteredUsers.map(user => {
            const userRole = roles.find(r => r.id === user.role_id);
            const userGroup = groups.find(g => g.id === user.group_id);
            return {
                ...user,
                role_name: userRole?.role || 'unknown',
                role_description: userRole?.role_description || '',
                group_name: userGroup?.group_name || ''
            };
        });
    }

    async createUser(userData) {
        const newUser = {
            id: Date.now(),
            user_name: userData.username,
            password: userData.password, // In real app, this would be hashed
            role_id: userData.role_id,
            group_id: userData.group_id || 1,
            created_on: new Date().toISOString(),
            modified_on: new Date().toISOString(),
            status: 1, // active
            ip_address: '127.0.0.1',
            last_logged_in: new Date().toISOString(),
            mobile_number: userData.mobile_number,
            email: userData.email
        };

        try {
            if (this.isOnline) {
                const result = await this.apiCall('/users', {
                    method: 'POST',
                    body: JSON.stringify(newUser)
                });
                return result.user;
            }
        } catch (error) {
            console.log('API failed, saving to local storage');
        }

        // Fallback to localStorage
        const users = this.getFromStorage('my_users') || [];
        users.push(newUser);
        this.saveToStorage('my_users', users);
        return newUser;
    }

    async updateUser(userId, userData) {
        try {
            if (this.isOnline) {
                const result = await this.apiCall(`/users/${userId}`, {
                    method: 'PUT',
                    body: JSON.stringify({
                        ...userData,
                        modified_on: new Date().toISOString()
                    })
                });
                return result.user;
            }
        } catch (error) {
            console.log('API failed, updating local storage');
        }

        // Fallback to localStorage
        const users = this.getFromStorage('my_users') || [];
        const userIndex = users.findIndex(user => user.id === userId);
        if (userIndex !== -1) {
            users[userIndex] = {
                ...users[userIndex],
                ...userData,
                modified_on: new Date().toISOString()
            };
            this.saveToStorage('my_users', users);
            return users[userIndex];
        }
        throw new Error('User not found');
    }

    async deleteUser(userId) {
        try {
            if (this.isOnline) {
                await this.apiCall(`/users/${userId}`, {
                    method: 'DELETE'
                });
                return true;
            }
        } catch (error) {
            console.log('API failed, deleting from local storage');
        }

        // Fallback to localStorage
        const users = this.getFromStorage('my_users') || [];
        const filteredUsers = users.filter(user => user.id !== userId);
        this.saveToStorage('my_users', filteredUsers);
        return true;
    }

    // Role Management
    async getRoles() {
        try {
            if (this.isOnline) {
                const result = await this.apiCall('/roles');
                return result.roles || [];
            }
        } catch (error) {
            console.log('API failed, using local storage');
        }

        return this.getFromStorage('my_role') || [];
    }

    // Group Management
    async getGroups() {
        try {
            if (this.isOnline) {
                const result = await this.apiCall('/groups');
                return result.groups || [];
            }
        } catch (error) {
            console.log('API failed, using local storage');
        }

        return this.getFromStorage('my_group_data') || [];
    }

    // Location Management
    async getCountries() {
        try {
            if (this.isOnline) {
                const result = await this.apiCall('/countries');
                return result.countries || [];
            }
        } catch (error) {
            console.log('API failed, using local storage');
        }

        return this.getFromStorage('my_country') || [];
    }

    async getStates(countryId = null) {
        try {
            if (this.isOnline) {
                const endpoint = countryId ? `/states?country_id=${countryId}` : '/states';
                const result = await this.apiCall(endpoint);
                return result.states || [];
            }
        } catch (error) {
            console.log('API failed, using local storage');
        }

        const states = this.getFromStorage('my_state') || [];
        return countryId ? states.filter(s => s.country_id === countryId) : states;
    }

    async getDistricts(stateId = null) {
        try {
            if (this.isOnline) {
                const endpoint = stateId ? `/districts?state_id=${stateId}` : '/districts';
                const result = await this.apiCall(endpoint);
                return result.districts || [];
            }
        } catch (error) {
            console.log('API failed, using local storage');
        }

        const districts = this.getFromStorage('my_district') || [];
        return stateId ? districts.filter(d => d.state_id === stateId) : districts;
    }
}

// Create singleton instance
const schemaBasedDatabaseService = new SchemaBasedDatabaseService();

export default schemaBasedDatabaseService;
