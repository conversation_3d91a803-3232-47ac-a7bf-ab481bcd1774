{"version": 3, "sources": ["vite:cjs-external-facade:@mapbox/node-pre-gyp", "../../bcrypt/promises.js", "../../bcrypt/bcrypt.js"], "sourcesContent": ["import * as m from \"vite-cjs-external-facade:@mapbox/node-pre-gyp\";module.exports = m;", "'use strict';\n\nvar Promise = global.Promise;\n\n/// encapsulate a method with a node-style callback in a Promise\n/// @param {object} 'this' of the encapsulated function\n/// @param {function} function to be encapsulated\n/// @param {Array-like} args to be passed to the called function\n/// @return {Promise} a Promise encapsulating the function\nmodule.exports.promise = function (fn, context, args) {\n\n    if (!Array.isArray(args)) {\n        args = Array.prototype.slice.call(args);\n    }\n\n    if (typeof fn !== 'function') {\n        return Promise.reject(new Error('fn must be a function'));\n    }\n\n    return new Promise(function(resolve, reject) {\n        args.push(function(err, data) {\n            if (err) {\n                reject(err);\n            } else {\n                resolve(data);\n            }\n        });\n\n        fn.apply(context, args);\n    });\n};\n\n/// @param {err} the error to be thrown\nmodule.exports.reject = function (err) {\n    return Promise.reject(err);\n};\n\n/// changes the promise implementation that bcrypt uses\n/// @param {Promise} the implementation to use\nmodule.exports.use = function(promise) {\n  Promise = promise;\n};\n", "'use strict';\n\nvar nodePreGyp = require('@mapbox/node-pre-gyp');\nvar path = require('path');\nvar binding_path = nodePreGyp.find(path.resolve(path.join(__dirname, './package.json')));\nvar bindings = require(binding_path);\n\nvar crypto = require('crypto');\n\nvar promises = require('./promises');\n\n/// generate a salt (sync)\n/// @param {Number} [rounds] number of rounds (default 10)\n/// @return {String} salt\nmodule.exports.genSaltSync = function genSaltSync(rounds, minor) {\n    // default 10 rounds\n    if (!rounds) {\n        rounds = 10;\n    } else if (typeof rounds !== 'number') {\n        throw new Error('rounds must be a number');\n    }\n\n    if(!minor) {\n        minor = 'b';\n    } else if(minor !== 'b' && minor !== 'a') {\n        throw new Error('minor must be either \"a\" or \"b\"');\n    }\n\n    return bindings.gen_salt_sync(minor, rounds, crypto.randomBytes(16));\n};\n\n/// generate a salt\n/// @param {Number} [rounds] number of rounds (default 10)\n/// @param {Function} cb callback(err, salt)\nmodule.exports.genSalt = function genSalt(rounds, minor, cb) {\n    var error;\n\n    // if callback is first argument, then use defaults for others\n    if (typeof arguments[0] === 'function') {\n        // have to set callback first otherwise arguments are overriden\n        cb = arguments[0];\n        rounds = 10;\n        minor = 'b';\n    // callback is second argument\n    } else if (typeof arguments[1] === 'function') {\n        // have to set callback first otherwise arguments are overriden\n        cb = arguments[1];\n        minor = 'b';\n    }\n\n    if (!cb) {\n        return promises.promise(genSalt, this, [rounds, minor]);\n    }\n\n    // default 10 rounds\n    if (!rounds) {\n        rounds = 10;\n    } else if (typeof rounds !== 'number') {\n        // callback error asynchronously\n        error = new Error('rounds must be a number');\n        return process.nextTick(function() {\n            cb(error);\n        });\n    }\n\n    if(!minor) {\n        minor = 'b'\n    } else if(minor !== 'b' && minor !== 'a') {\n        error = new Error('minor must be either \"a\" or \"b\"');\n        return process.nextTick(function() {\n            cb(error);\n        });\n    }\n\n    crypto.randomBytes(16, function(error, randomBytes) {\n        if (error) {\n            cb(error);\n            return;\n        }\n\n        bindings.gen_salt(minor, rounds, randomBytes, cb);\n    });\n};\n\n/// hash data using a salt\n/// @param {String|Buffer} data the data to encrypt\n/// @param {String} salt the salt to use when hashing\n/// @return {String} hash\nmodule.exports.hashSync = function hashSync(data, salt) {\n    if (data == null || salt == null) {\n        throw new Error('data and salt arguments required');\n    }\n\n    if (!(typeof data === 'string' || data instanceof Buffer) || (typeof salt !== 'string' && typeof salt !== 'number')) {\n        throw new Error('data must be a string or Buffer and salt must either be a salt string or a number of rounds');\n    }\n\n    if (typeof salt === 'number') {\n        salt = module.exports.genSaltSync(salt);\n    }\n\n    return bindings.encrypt_sync(data, salt);\n};\n\n/// hash data using a salt\n/// @param {String|Buffer} data the data to encrypt\n/// @param {String} salt the salt to use when hashing\n/// @param {Function} cb callback(err, hash)\nmodule.exports.hash = function hash(data, salt, cb) {\n    var error;\n\n    if (typeof data === 'function') {\n        error = new Error('data must be a string or Buffer and salt must either be a salt string or a number of rounds');\n        return process.nextTick(function() {\n            data(error);\n        });\n    }\n\n    if (typeof salt === 'function') {\n        error = new Error('data must be a string or Buffer and salt must either be a salt string or a number of rounds');\n        return process.nextTick(function() {\n            salt(error);\n        });\n    }\n\n    // cb exists but is not a function\n    // return a rejecting promise\n    if (cb && typeof cb !== 'function') {\n        return promises.reject(new Error('cb must be a function or null to return a Promise'));\n    }\n\n    if (!cb) {\n        return promises.promise(hash, this, [data, salt]);\n    }\n\n    if (data == null || salt == null) {\n        error = new Error('data and salt arguments required');\n        return process.nextTick(function() {\n            cb(error);\n        });\n    }\n\n    if (!(typeof data === 'string' || data instanceof Buffer) || (typeof salt !== 'string' && typeof salt !== 'number')) {\n        error = new Error('data must be a string or Buffer and salt must either be a salt string or a number of rounds');\n        return process.nextTick(function() {\n            cb(error);\n        });\n    }\n\n\n    if (typeof salt === 'number') {\n        return module.exports.genSalt(salt, function(err, salt) {\n            return bindings.encrypt(data, salt, cb);\n        });\n    }\n\n    return bindings.encrypt(data, salt, cb);\n};\n\n/// compare raw data to hash\n/// @param {String|Buffer} data the data to hash and compare\n/// @param {String} hash expected hash\n/// @return {bool} true if hashed data matches hash\nmodule.exports.compareSync = function compareSync(data, hash) {\n    if (data == null || hash == null) {\n        throw new Error('data and hash arguments required');\n    }\n\n    if (!(typeof data === 'string' || data instanceof Buffer) || typeof hash !== 'string') {\n        throw new Error('data must be a string or Buffer and hash must be a string');\n    }\n\n    return bindings.compare_sync(data, hash);\n};\n\n/// compare raw data to hash\n/// @param {String|Buffer} data the data to hash and compare\n/// @param {String} hash expected hash\n/// @param {Function} cb callback(err, matched) - matched is true if hashed data matches hash\nmodule.exports.compare = function compare(data, hash, cb) {\n    var error;\n\n    if (typeof data === 'function') {\n        error = new Error('data and hash arguments required');\n        return process.nextTick(function() {\n            data(error);\n        });\n    }\n\n    if (typeof hash === 'function') {\n        error = new Error('data and hash arguments required');\n        return process.nextTick(function() {\n            hash(error);\n        });\n    }\n\n    // cb exists but is not a function\n    // return a rejecting promise\n    if (cb && typeof cb !== 'function') {\n        return promises.reject(new Error('cb must be a function or null to return a Promise'));\n    }\n\n    if (!cb) {\n        return promises.promise(compare, this, [data, hash]);\n    }\n\n    if (data == null || hash == null) {\n        error = new Error('data and hash arguments required');\n        return process.nextTick(function() {\n            cb(error);\n        });\n    }\n\n    if (!(typeof data === 'string' || data instanceof Buffer) || typeof hash !== 'string') {\n        error = new Error('data and hash must be strings');\n        return process.nextTick(function() {\n            cb(error);\n        });\n    }\n\n    return bindings.compare(data, hash, cb);\n};\n\n/// @param {String} hash extract rounds from this hash\n/// @return {Number} the number of rounds used to encrypt a given hash\nmodule.exports.getRounds = function getRounds(hash) {\n    if (hash == null) {\n        throw new Error('hash argument required');\n    }\n\n    if (typeof hash !== 'string') {\n        throw new Error('hash must be a string');\n    }\n\n    return bindings.get_rounds(hash);\n};\n"], "mappings": ";;;;;;;;;;;;AAAA,YAAY,OAAO;AAAnB;AAAA;AAAmE,WAAO,UAAU;AAAA;AAAA;;;ACApF;AAAA;AAAA;AAEA,QAAIA,WAAU,OAAO;AAOrB,WAAO,QAAQ,UAAU,SAAU,IAAI,SAAS,MAAM;AAElD,UAAI,CAAC,MAAM,QAAQ,IAAI,GAAG;AACtB,eAAO,MAAM,UAAU,MAAM,KAAK,IAAI;AAAA,MAC1C;AAEA,UAAI,OAAO,OAAO,YAAY;AAC1B,eAAOA,SAAQ,OAAO,IAAI,MAAM,uBAAuB,CAAC;AAAA,MAC5D;AAEA,aAAO,IAAIA,SAAQ,SAAS,SAAS,QAAQ;AACzC,aAAK,KAAK,SAAS,KAAK,MAAM;AAC1B,cAAI,KAAK;AACL,mBAAO,GAAG;AAAA,UACd,OAAO;AACH,oBAAQ,IAAI;AAAA,UAChB;AAAA,QACJ,CAAC;AAED,WAAG,MAAM,SAAS,IAAI;AAAA,MAC1B,CAAC;AAAA,IACL;AAGA,WAAO,QAAQ,SAAS,SAAU,KAAK;AACnC,aAAOA,SAAQ,OAAO,GAAG;AAAA,IAC7B;AAIA,WAAO,QAAQ,MAAM,SAAS,SAAS;AACrC,MAAAA,WAAU;AAAA,IACZ;AAAA;AAAA;;;ACzCA;AAAA;AAEA,QAAI,aAAa;AACjB,QAAI,OAAO;AACX,QAAI,eAAe,WAAW,KAAK,KAAK,QAAQ,KAAK,KAAK,WAAW,gBAAgB,CAAC,CAAC;AACvF,QAAI,WAAW,UAAQ,YAAY;AAEnC,QAAI,SAAS;AAEb,QAAI,WAAW;AAKf,WAAO,QAAQ,cAAc,SAAS,YAAY,QAAQ,OAAO;AAE7D,UAAI,CAAC,QAAQ;AACT,iBAAS;AAAA,MACb,WAAW,OAAO,WAAW,UAAU;AACnC,cAAM,IAAI,MAAM,yBAAyB;AAAA,MAC7C;AAEA,UAAG,CAAC,OAAO;AACP,gBAAQ;AAAA,MACZ,WAAU,UAAU,OAAO,UAAU,KAAK;AACtC,cAAM,IAAI,MAAM,iCAAiC;AAAA,MACrD;AAEA,aAAO,SAAS,cAAc,OAAO,QAAQ,OAAO,YAAY,EAAE,CAAC;AAAA,IACvE;AAKA,WAAO,QAAQ,UAAU,SAAS,QAAQ,QAAQ,OAAO,IAAI;AACzD,UAAI;AAGJ,UAAI,OAAO,UAAU,CAAC,MAAM,YAAY;AAEpC,aAAK,UAAU,CAAC;AAChB,iBAAS;AACT,gBAAQ;AAAA,MAEZ,WAAW,OAAO,UAAU,CAAC,MAAM,YAAY;AAE3C,aAAK,UAAU,CAAC;AAChB,gBAAQ;AAAA,MACZ;AAEA,UAAI,CAAC,IAAI;AACL,eAAO,SAAS,QAAQ,SAAS,MAAM,CAAC,QAAQ,KAAK,CAAC;AAAA,MAC1D;AAGA,UAAI,CAAC,QAAQ;AACT,iBAAS;AAAA,MACb,WAAW,OAAO,WAAW,UAAU;AAEnC,gBAAQ,IAAI,MAAM,yBAAyB;AAC3C,eAAO,QAAQ,SAAS,WAAW;AAC/B,aAAG,KAAK;AAAA,QACZ,CAAC;AAAA,MACL;AAEA,UAAG,CAAC,OAAO;AACP,gBAAQ;AAAA,MACZ,WAAU,UAAU,OAAO,UAAU,KAAK;AACtC,gBAAQ,IAAI,MAAM,iCAAiC;AACnD,eAAO,QAAQ,SAAS,WAAW;AAC/B,aAAG,KAAK;AAAA,QACZ,CAAC;AAAA,MACL;AAEA,aAAO,YAAY,IAAI,SAASC,QAAO,aAAa;AAChD,YAAIA,QAAO;AACP,aAAGA,MAAK;AACR;AAAA,QACJ;AAEA,iBAAS,SAAS,OAAO,QAAQ,aAAa,EAAE;AAAA,MACpD,CAAC;AAAA,IACL;AAMA,WAAO,QAAQ,WAAW,SAAS,SAAS,MAAM,MAAM;AACpD,UAAI,QAAQ,QAAQ,QAAQ,MAAM;AAC9B,cAAM,IAAI,MAAM,kCAAkC;AAAA,MACtD;AAEA,UAAI,EAAE,OAAO,SAAS,YAAY,gBAAgB,WAAY,OAAO,SAAS,YAAY,OAAO,SAAS,UAAW;AACjH,cAAM,IAAI,MAAM,6FAA6F;AAAA,MACjH;AAEA,UAAI,OAAO,SAAS,UAAU;AAC1B,eAAO,OAAO,QAAQ,YAAY,IAAI;AAAA,MAC1C;AAEA,aAAO,SAAS,aAAa,MAAM,IAAI;AAAA,IAC3C;AAMA,WAAO,QAAQ,OAAO,SAAS,KAAK,MAAM,MAAM,IAAI;AAChD,UAAI;AAEJ,UAAI,OAAO,SAAS,YAAY;AAC5B,gBAAQ,IAAI,MAAM,6FAA6F;AAC/G,eAAO,QAAQ,SAAS,WAAW;AAC/B,eAAK,KAAK;AAAA,QACd,CAAC;AAAA,MACL;AAEA,UAAI,OAAO,SAAS,YAAY;AAC5B,gBAAQ,IAAI,MAAM,6FAA6F;AAC/G,eAAO,QAAQ,SAAS,WAAW;AAC/B,eAAK,KAAK;AAAA,QACd,CAAC;AAAA,MACL;AAIA,UAAI,MAAM,OAAO,OAAO,YAAY;AAChC,eAAO,SAAS,OAAO,IAAI,MAAM,mDAAmD,CAAC;AAAA,MACzF;AAEA,UAAI,CAAC,IAAI;AACL,eAAO,SAAS,QAAQ,MAAM,MAAM,CAAC,MAAM,IAAI,CAAC;AAAA,MACpD;AAEA,UAAI,QAAQ,QAAQ,QAAQ,MAAM;AAC9B,gBAAQ,IAAI,MAAM,kCAAkC;AACpD,eAAO,QAAQ,SAAS,WAAW;AAC/B,aAAG,KAAK;AAAA,QACZ,CAAC;AAAA,MACL;AAEA,UAAI,EAAE,OAAO,SAAS,YAAY,gBAAgB,WAAY,OAAO,SAAS,YAAY,OAAO,SAAS,UAAW;AACjH,gBAAQ,IAAI,MAAM,6FAA6F;AAC/G,eAAO,QAAQ,SAAS,WAAW;AAC/B,aAAG,KAAK;AAAA,QACZ,CAAC;AAAA,MACL;AAGA,UAAI,OAAO,SAAS,UAAU;AAC1B,eAAO,OAAO,QAAQ,QAAQ,MAAM,SAAS,KAAKC,OAAM;AACpD,iBAAO,SAAS,QAAQ,MAAMA,OAAM,EAAE;AAAA,QAC1C,CAAC;AAAA,MACL;AAEA,aAAO,SAAS,QAAQ,MAAM,MAAM,EAAE;AAAA,IAC1C;AAMA,WAAO,QAAQ,cAAc,SAAS,YAAY,MAAM,MAAM;AAC1D,UAAI,QAAQ,QAAQ,QAAQ,MAAM;AAC9B,cAAM,IAAI,MAAM,kCAAkC;AAAA,MACtD;AAEA,UAAI,EAAE,OAAO,SAAS,YAAY,gBAAgB,WAAW,OAAO,SAAS,UAAU;AACnF,cAAM,IAAI,MAAM,2DAA2D;AAAA,MAC/E;AAEA,aAAO,SAAS,aAAa,MAAM,IAAI;AAAA,IAC3C;AAMA,WAAO,QAAQ,UAAU,SAAS,QAAQ,MAAM,MAAM,IAAI;AACtD,UAAI;AAEJ,UAAI,OAAO,SAAS,YAAY;AAC5B,gBAAQ,IAAI,MAAM,kCAAkC;AACpD,eAAO,QAAQ,SAAS,WAAW;AAC/B,eAAK,KAAK;AAAA,QACd,CAAC;AAAA,MACL;AAEA,UAAI,OAAO,SAAS,YAAY;AAC5B,gBAAQ,IAAI,MAAM,kCAAkC;AACpD,eAAO,QAAQ,SAAS,WAAW;AAC/B,eAAK,KAAK;AAAA,QACd,CAAC;AAAA,MACL;AAIA,UAAI,MAAM,OAAO,OAAO,YAAY;AAChC,eAAO,SAAS,OAAO,IAAI,MAAM,mDAAmD,CAAC;AAAA,MACzF;AAEA,UAAI,CAAC,IAAI;AACL,eAAO,SAAS,QAAQ,SAAS,MAAM,CAAC,MAAM,IAAI,CAAC;AAAA,MACvD;AAEA,UAAI,QAAQ,QAAQ,QAAQ,MAAM;AAC9B,gBAAQ,IAAI,MAAM,kCAAkC;AACpD,eAAO,QAAQ,SAAS,WAAW;AAC/B,aAAG,KAAK;AAAA,QACZ,CAAC;AAAA,MACL;AAEA,UAAI,EAAE,OAAO,SAAS,YAAY,gBAAgB,WAAW,OAAO,SAAS,UAAU;AACnF,gBAAQ,IAAI,MAAM,+BAA+B;AACjD,eAAO,QAAQ,SAAS,WAAW;AAC/B,aAAG,KAAK;AAAA,QACZ,CAAC;AAAA,MACL;AAEA,aAAO,SAAS,QAAQ,MAAM,MAAM,EAAE;AAAA,IAC1C;AAIA,WAAO,QAAQ,YAAY,SAAS,UAAU,MAAM;AAChD,UAAI,QAAQ,MAAM;AACd,cAAM,IAAI,MAAM,wBAAwB;AAAA,MAC5C;AAEA,UAAI,OAAO,SAAS,UAAU;AAC1B,cAAM,IAAI,MAAM,uBAAuB;AAAA,MAC3C;AAEA,aAAO,SAAS,WAAW,IAAI;AAAA,IACnC;AAAA;AAAA;", "names": ["Promise", "error", "salt"]}