import {
  require_fs
} from "./chunk-FUSWORVU.js";
import {
  require_path
} from "./chunk-UQQ6GOXD.js";
import {
  __commonJS,
  __export,
  __reExport,
  __toESM
} from "./chunk-TM6AOUSD.js";

// node_modules/sqlite/build/utils/format-error.js
var require_format_error = __commonJS({
  "node_modules/sqlite/build/utils/format-error.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.formatError = void 0;
    function formatError(err) {
      if (err instanceof Error) {
        return err;
      }
      if (typeof err === "object") {
        const newError = new Error();
        for (let prop in err) {
          newError[prop] = err[prop];
        }
        if (err.message) {
          newError.message = err.message;
        }
        return newError;
      }
      if (typeof err === "string") {
        return new Error(err);
      }
      return new Error(err);
    }
    exports.formatError = formatError;
  }
});

// node_modules/sqlite/build/Statement.js
var require_Statement = __commonJS({
  "node_modules/sqlite/build/Statement.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.Statement = void 0;
    var format_error_1 = require_format_error();
    var Statement = class {
      constructor(stmt) {
        this.stmt = stmt;
      }
      /**
       * Returns the underlying sqlite3 Statement instance
       */
      getStatementInstance() {
        return this.stmt;
      }
      /**
       * Binds parameters to the prepared statement.
       *
       * Binding parameters with this function completely resets the statement object and row cursor
       * and removes all previously bound parameters, if any.
       */
      bind(...params) {
        return new Promise((resolve, reject) => {
          this.stmt.bind(...params, (err) => {
            if (err) {
              return reject((0, format_error_1.formatError)(err));
            }
            resolve();
          });
        });
      }
      /**
       * Resets the row cursor of the statement and preserves the parameter bindings.
       * Use this function to re-execute the same query with the same bindings.
       */
      reset() {
        return new Promise((resolve) => {
          this.stmt.reset(() => {
            resolve();
          });
        });
      }
      /**
       * Finalizes the statement. This is typically optional, but if you experience long delays before
       * the next query is executed, explicitly finalizing your statement might be necessary.
       * This might be the case when you run an exclusive query (see section Control Flow).
       * After the statement is finalized, all further function calls on that statement object
       * will throw errors.
       */
      finalize() {
        return new Promise((resolve, reject) => {
          this.stmt.finalize((err) => {
            if (err) {
              return reject((0, format_error_1.formatError)(err));
            }
            resolve();
          });
        });
      }
      /**
       * Binds parameters and executes the statement.
       *
       * If you specify bind parameters, they will be bound to the statement before it is executed.
       * Note that the bindings and the row cursor are reset when you specify even a single bind parameter.
       *
       * The execution behavior is identical to the Database#run method with the difference that the
       * statement will not be finalized after it is run. This means you can run it multiple times.
       *
       * @param {any} [params, ...] When the SQL statement contains placeholders, you
       * can pass them in here. They will be bound to the statement before it is
       * executed. There are three ways of passing bind parameters: directly in
       * the function's arguments, as an array, and as an object for named
       * parameters. This automatically sanitizes inputs.
       */
      run(...params) {
        return new Promise((resolve, reject) => {
          const stmt = this;
          this.stmt.run(...params, function(err) {
            if (err) {
              return reject((0, format_error_1.formatError)(err));
            }
            resolve({
              stmt,
              lastID: this.lastID,
              changes: this.changes
            });
          });
        });
      }
      /**
       * Binds parameters, executes the statement and retrieves the first result row.
       * The parameters are the same as the Statement#run function, with the following differences:
       *
       * Using this method can leave the database locked, as the database awaits further
       * calls to Statement#get to retrieve subsequent rows. To inform the database that you
       * are finished retrieving rows, you should either finalize (with Statement#finalize)
       * or reset (with Statement#reset) the statement.
       *
       * @param {any} [params, ...] When the SQL statement contains placeholders, you
       * can pass them in here. They will be bound to the statement before it is
       * executed. There are three ways of passing bind parameters: directly in
       * the function's arguments, as an array, and as an object for named
       * parameters. This automatically sanitizes inputs.
       */
      get(...params) {
        return new Promise((resolve, reject) => {
          this.stmt.get(...params, (err, row) => {
            if (err) {
              return reject((0, format_error_1.formatError)(err));
            }
            resolve(row);
          });
        });
      }
      /**
       * Binds parameters, executes the statement and calls the callback with all result rows.
       * The parameters are the same as the Statement#run function, with the following differences:
       *
       * If the result set is empty, it will resolve to an empty array, otherwise it contains an
       * object for each result row which in turn contains the values of that row.
       * Like with Statement#run, the statement will not be finalized after executing this function.
       *
       * @param {any} [params, ...] When the SQL statement contains placeholders, you
       * can pass them in here. They will be bound to the statement before it is
       * executed. There are three ways of passing bind parameters: directly in
       * the function's arguments, as an array, and as an object for named
       * parameters. This automatically sanitizes inputs.
       *
       * @see https://github.com/mapbox/node-sqlite3/wiki/API#databaseallsql-param--callback
       */
      all(...params) {
        return new Promise((resolve, reject) => {
          this.stmt.all(...params, (err, rows) => {
            if (err) {
              return reject((0, format_error_1.formatError)(err));
            }
            resolve(rows);
          });
        });
      }
      each(...params) {
        return new Promise((resolve, reject) => {
          const callback = params.pop();
          if (!callback || typeof callback !== "function") {
            throw new Error("sqlite: Last param of Statement#each() must be a callback function");
          }
          if (params.length > 0) {
            const positional = params.pop();
            if (typeof positional === "function") {
              throw new Error("sqlite: Statement#each() should only have a single callback defined. See readme for usage.");
            }
            params.push(positional);
          }
          this.stmt.each(...params, (err, row) => {
            if (err) {
              return callback((0, format_error_1.formatError)(err), null);
            }
            callback(null, row);
          }, (err, count) => {
            if (err) {
              return reject((0, format_error_1.formatError)(err));
            }
            resolve(count);
          });
        });
      }
    };
    exports.Statement = Statement;
  }
});

// node_modules/sqlite/build/utils/migrate.js
var require_migrate = __commonJS({
  "node_modules/sqlite/build/utils/migrate.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.migrate = exports.readMigrations = void 0;
    var fs = require_fs();
    var path = require_path();
    async function readMigrations(migrationPath) {
      const migrationsPath = migrationPath || path.join(process.cwd(), "migrations");
      const location = path.resolve(migrationsPath);
      const migrationFiles = await new Promise((resolve, reject) => {
        fs.readdir(location, (err, files) => {
          if (err) {
            return reject(err);
          }
          resolve(files.map((x) => x.match(/^(\d+).(.*?)\.sql$/)).filter((x) => x !== null).map((x) => ({ id: Number(x[1]), name: x[2], filename: x[0] })).sort((a, b) => Math.sign(a.id - b.id)));
        });
      });
      if (!migrationFiles.length) {
        throw new Error(`No migration files found in '${location}'.`);
      }
      return Promise.all(migrationFiles.map((migration) => new Promise((resolve, reject) => {
        const filename = path.join(location, migration.filename);
        fs.readFile(filename, "utf-8", (err, data) => {
          if (err) {
            return reject(err);
          }
          const [up, down] = data.split(/^--\s+?down\b/im);
          const migrationData = migration;
          migrationData.up = up.replace(/^-- .*?$/gm, "").trim();
          migrationData.down = down ? down.trim() : "";
          resolve(migrationData);
        });
      })));
    }
    exports.readMigrations = readMigrations;
    async function migrate(db, config = {}) {
      config.force = config.force || false;
      config.table = config.table || "migrations";
      const { force, table } = config;
      const migrations = config.migrations ? config.migrations : await readMigrations(config.migrationsPath);
      await db.run(`CREATE TABLE IF NOT EXISTS "${table}" (
  id   INTEGER PRIMARY KEY,
  name TEXT    NOT NULL,
  up   TEXT    NOT NULL,
  down TEXT    NOT NULL
)`);
      let dbMigrations = await db.all(`SELECT id, name, up, down FROM "${table}" ORDER BY id ASC`);
      const lastMigration = migrations[migrations.length - 1];
      for (const migration of dbMigrations.slice().sort((a, b) => Math.sign(b.id - a.id))) {
        if (!migrations.some((x) => x.id === migration.id) || force && migration.id === lastMigration.id) {
          await db.run("BEGIN");
          try {
            await db.exec(migration.down);
            await db.run(`DELETE FROM "${table}" WHERE id = ?`, migration.id);
            await db.run("COMMIT");
            dbMigrations = dbMigrations.filter((x) => x.id !== migration.id);
          } catch (err) {
            await db.run("ROLLBACK");
            throw err;
          }
        } else {
          break;
        }
      }
      const lastMigrationId = dbMigrations.length ? dbMigrations[dbMigrations.length - 1].id : 0;
      for (const migration of migrations) {
        if (migration.id > lastMigrationId) {
          await db.run("BEGIN");
          try {
            await db.exec(migration.up);
            await db.run(`INSERT INTO "${table}" (id, name, up, down) VALUES (?, ?, ?, ?)`, migration.id, migration.name, migration.up, migration.down);
            await db.run("COMMIT");
          } catch (err) {
            await db.run("ROLLBACK");
            throw err;
          }
        }
      }
    }
    exports.migrate = migrate;
  }
});

// node_modules/sqlite/build/utils/strings.js
var require_strings = __commonJS({
  "node_modules/sqlite/build/utils/strings.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.toSqlParams = void 0;
    function toSqlParams(sql, params = []) {
      if (typeof sql === "string") {
        return {
          sql,
          params
        };
      }
      return {
        sql: sql.sql,
        params: sql.values
      };
    }
    exports.toSqlParams = toSqlParams;
  }
});

// node_modules/sqlite/build/Database.js
var require_Database = __commonJS({
  "node_modules/sqlite/build/Database.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.Database = void 0;
    var Statement_1 = require_Statement();
    var migrate_1 = require_migrate();
    var strings_1 = require_strings();
    var format_error_1 = require_format_error();
    var Database2 = class {
      constructor(config) {
        this.config = config;
        this.db = null;
      }
      /**
       * Event handler when verbose mode is enabled.
       * @see https://github.com/mapbox/node-sqlite3/wiki/Debugging
       */
      on(event, listener) {
        this.db.on(event, listener);
      }
      /**
       * Returns the underlying sqlite3 Database instance
       */
      getDatabaseInstance() {
        return this.db;
      }
      /**
       * Opens the database
       */
      open() {
        return new Promise((resolve, reject) => {
          let { filename, mode, driver } = this.config;
          if (filename === null || filename === void 0) {
            throw new Error("sqlite: filename cannot be null / undefined");
          }
          if (!driver) {
            throw new Error("sqlite: driver is not defined");
          }
          if (mode) {
            this.db = new driver(filename, mode, (err) => {
              if (err) {
                return reject((0, format_error_1.formatError)(err));
              }
              resolve();
            });
          } else {
            this.db = new driver(filename, (err) => {
              if (err) {
                return reject((0, format_error_1.formatError)(err));
              }
              resolve();
            });
          }
        });
      }
      /**
       * Closes the database.
       */
      close() {
        return new Promise((resolve, reject) => {
          this.db.close((err) => {
            if (err) {
              return reject((0, format_error_1.formatError)(err));
            }
            resolve();
          });
        });
      }
      /**
       * @see https://github.com/mapbox/node-sqlite3/wiki/API#databaseconfigureoption-value
       */
      configure(option, value) {
        this.db.configure(option, value);
      }
      /**
       * Runs the SQL query with the specified parameters. It does not retrieve any result data.
       * The function returns the Database object for which it was called to allow for function chaining.
       *
       * @param {string} sql The SQL query to run.
       *
       * @param {any} [params, ...] When the SQL statement contains placeholders, you
       * can pass them in here. They will be bound to the statement before it is
       * executed. There are three ways of passing bind parameters: directly in
       * the function's arguments, as an array, and as an object for named
       * parameters. This automatically sanitizes inputs.
       *
       * @see https://github.com/mapbox/node-sqlite3/wiki/API#databaserunsql-param--callback
       */
      run(sql, ...params) {
        return new Promise((resolve, reject) => {
          const sqlObj = (0, strings_1.toSqlParams)(sql, params);
          this.db.run(sqlObj.sql, ...sqlObj.params, function(err) {
            if (err) {
              return reject((0, format_error_1.formatError)(err));
            }
            resolve({
              stmt: new Statement_1.Statement(this.stmt),
              lastID: this.lastID,
              changes: this.changes
            });
          });
        });
      }
      /**
       * Runs the SQL query with the specified parameters and resolves with
       * with the first result row afterwards. If the result set is empty, returns undefined.
       *
       * The property names correspond to the column names of the result set.
       * It is impossible to access them by column index; the only supported way is by column name.
       *
       * @param {string} sql The SQL query to run.
       *
       * @param {any} [params, ...] When the SQL statement contains placeholders, you
       * can pass them in here. They will be bound to the statement before it is
       * executed. There are three ways of passing bind parameters: directly in
       * the function's arguments, as an array, and as an object for named
       * parameters. This automatically sanitizes inputs.
       *
       * @see https://github.com/mapbox/node-sqlite3/wiki/API#databasegetsql-param--callback
       */
      get(sql, ...params) {
        return new Promise((resolve, reject) => {
          const sqlObj = (0, strings_1.toSqlParams)(sql, params);
          this.db.get(sqlObj.sql, ...sqlObj.params, (err, row) => {
            if (err) {
              return reject((0, format_error_1.formatError)(err));
            }
            resolve(row);
          });
        });
      }
      each(sql, ...params) {
        return new Promise((resolve, reject) => {
          const callback = params.pop();
          if (!callback || typeof callback !== "function") {
            throw new Error("sqlite: Last param of Database#each() must be a callback function");
          }
          if (params.length > 0) {
            const positional = params.pop();
            if (typeof positional === "function") {
              throw new Error("sqlite: Database#each() should only have a single callback defined. See readme for usage.");
            }
            params.push(positional);
          }
          const sqlObj = (0, strings_1.toSqlParams)(sql, params);
          this.db.each(sqlObj.sql, ...sqlObj.params, (err, row) => {
            if (err) {
              return callback((0, format_error_1.formatError)(err), null);
            }
            callback(null, row);
          }, (err, count) => {
            if (err) {
              return reject((0, format_error_1.formatError)(err));
            }
            resolve(count);
          });
        });
      }
      /**
       * Runs the SQL query with the specified parameters. The parameters are the same as the
       * Database#run function, with the following differences:
       *
       * If the result set is empty, it will be an empty array, otherwise it will
       * have an object for each result row which
       * in turn contains the values of that row, like the Database#get function.
       *
       * Note that it first retrieves all result rows and stores them in memory.
       * For queries that have potentially large result sets, use the Database#each
       * function to retrieve all rows or Database#prepare followed by multiple
       * Statement#get calls to retrieve a previously unknown amount of rows.
       *
       * @param {string} sql The SQL query to run.
       *
       * @param {any} [params, ...] When the SQL statement contains placeholders, you
       * can pass them in here. They will be bound to the statement before it is
       * executed. There are three ways of passing bind parameters: directly in
       * the function's arguments, as an array, and as an object for named
       * parameters. This automatically sanitizes inputs.
       *
       * @see https://github.com/mapbox/node-sqlite3/wiki/API#databaseallsql-param--callback
       */
      all(sql, ...params) {
        return new Promise((resolve, reject) => {
          const sqlObj = (0, strings_1.toSqlParams)(sql, params);
          this.db.all(sqlObj.sql, ...sqlObj.params, (err, rows) => {
            if (err) {
              return reject((0, format_error_1.formatError)(err));
            }
            resolve(rows);
          });
        });
      }
      /**
       * Runs all SQL queries in the supplied string. No result rows are retrieved. If a query fails,
       * no subsequent statements will be executed (wrap it in a transaction if you want all
       * or none to be executed).
       *
       * Note: This function will only execute statements up to the first NULL byte.
       * Comments are not allowed and will lead to runtime errors.
       *
       * @param {string} sql The SQL query to run.
       * @see https://github.com/mapbox/node-sqlite3/wiki/API#databaseexecsql-callback
       */
      exec(sql) {
        return new Promise((resolve, reject) => {
          const sqlObj = (0, strings_1.toSqlParams)(sql);
          this.db.exec(sqlObj.sql, (err) => {
            if (err) {
              return reject((0, format_error_1.formatError)(err));
            }
            resolve();
          });
        });
      }
      /**
       * Prepares the SQL statement and optionally binds the specified parameters.
       * When bind parameters are supplied, they are bound to the prepared statement.
       *
       * @param {string} sql The SQL query to run.
       * @param {any} [params, ...] When the SQL statement contains placeholders, you
       * can pass them in here. They will be bound to the statement before it is
       * executed. There are three ways of passing bind parameters: directly in
       * the function's arguments, as an array, and as an object for named
       * parameters. This automatically sanitizes inputs.
       * @returns Promise<Statement> Statement object
       */
      prepare(sql, ...params) {
        return new Promise((resolve, reject) => {
          const sqlObj = (0, strings_1.toSqlParams)(sql, params);
          const stmt = this.db.prepare(sqlObj.sql, ...sqlObj.params, (err) => {
            if (err) {
              return reject(err);
            }
            resolve(new Statement_1.Statement(stmt));
          });
        });
      }
      /**
       * Loads a compiled SQLite extension into the database connection object.
       *
       * @param {string} path Filename of the extension to load
       */
      loadExtension(path) {
        return new Promise((resolve, reject) => {
          this.db.loadExtension(path, (err) => {
            if (err) {
              return reject((0, format_error_1.formatError)(err));
            }
            resolve();
          });
        });
      }
      /**
       * Performs a database migration.
       */
      async migrate(config) {
        await (0, migrate_1.migrate)(this, config);
      }
      /**
       * The methods underneath requires creative work to implement. PRs / proposals accepted!
       */
      /*
       * Unsure if serialize can be made into a promise.
       */
      serialize() {
        throw new Error("sqlite: Currently not implemented. Use getDatabaseInstance().serialize() instead.");
      }
      /*
       * Unsure if parallelize can be made into a promise.
       */
      parallelize() {
        throw new Error("sqlite: Currently not implemented. Use getDatabaseInstance().parallelize() instead.");
      }
    };
    exports.Database = Database2;
  }
});

// node_modules/sqlite/build/index.mjs
var build_exports = {};
__export(build_exports, {
  open: () => open
});
__reExport(build_exports, __toESM(require_Statement(), 1));
__reExport(build_exports, __toESM(require_Database(), 1));
var import_Database = __toESM(require_Database(), 1);
async function open(config) {
  const db = new import_Database.default.Database(config);
  await db.open();
  return db;
}
export {
  open
};
//# sourceMappingURL=sqlite.js.map
