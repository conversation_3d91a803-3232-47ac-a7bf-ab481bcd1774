{"version": 3, "sources": ["../../sqlite/src/utils/format-error.ts", "../../sqlite/src/Statement.ts", "../../sqlite/src/utils/migrate.ts", "../../sqlite/src/utils/strings.ts", "../../sqlite/src/Database.ts", "../../sqlite/build/index.mjs"], "sourcesContent": [null, null, null, null, null, "export * from \"./Statement.js\";\nexport * from \"./Database.js\";\nimport Database from \"./Database.js\";\n\n/**\n * Opens a database for manipulation. Most users will call this to get started.\n */\nexport async function open(config) {\n  const db = new Database.Database(config);\n  await db.open();\n  return db;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,aAAgB,YAAa,KAAQ;AACnC,UAAI,eAAe,OAAO;AACxB,eAAO;;AAGT,UAAI,OAAO,QAAQ,UAAU;AAC3B,cAAM,WAAW,IAAI,MAAK;AAE1B,iBAAS,QAAQ,KAAK;AACpB,mBAAS,IAAI,IAAI,IAAI,IAAI;;AAI3B,YAAI,IAAI,SAAS;AACf,mBAAS,UAAU,IAAI;;AAGzB,eAAO;;AAGT,UAAI,OAAO,QAAQ,UAAU;AAC3B,eAAO,IAAI,MAAM,GAAG;;AAGtB,aAAO,IAAI,MAAM,GAAG;IACtB;AAzBA,YAAA,cAAA;;;;;;;;;;ACEA,QAAA,iBAAA;AAKA,QAAa,YAAb,MAAsB;MAGpB,YAAa,MAAO;AAClB,aAAK,OAAO;MACd;;;;MAKA,uBAAoB;AAClB,eAAO,KAAK;MACd;;;;;;;MAQA,QAAS,QAAa;AACpB,eAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,eAAK,KAAK,KAAK,GAAG,QAAQ,SAAM;AAC9B,gBAAI,KAAK;AACP,qBAAO,QAAO,GAAA,eAAA,aAAY,GAAG,CAAC;;AAGhC,oBAAO;UACT,CAAC;QACH,CAAC;MACH;;;;;MAMA,QAAK;AACH,eAAO,IAAI,QAAQ,aAAU;AAC3B,eAAK,KAAK,MAAM,MAAK;AACnB,oBAAO;UACT,CAAC;QACH,CAAC;MACH;;;;;;;;MASA,WAAQ;AACN,eAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,eAAK,KAAK,SAAS,SAAM;AACvB,gBAAI,KAAK;AACP,qBAAO,QAAO,GAAA,eAAA,aAAY,GAAG,CAAC;;AAGhC,oBAAO;UACT,CAAC;QACH,CAAC;MACH;;;;;;;;;;;;;;;;MAiBA,OAAQ,QAAa;AACnB,eAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,gBAAM,OAAO;AAEb,eAAK,KAAK,IAAI,GAAG,QAAQ,SAAU,KAAG;AACpC,gBAAI,KAAK;AACP,qBAAO,QAAO,GAAA,eAAA,aAAY,GAAG,CAAC;;AAGhC,oBAAQ;cACN;cACA,QAAQ,KAAK;cACb,SAAS,KAAK;aACf;UACH,CAAC;QACH,CAAC;MACH;;;;;;;;;;;;;;;;MAiBA,OAAiB,QAAa;AAC5B,eAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,eAAK,KAAK,IAAI,GAAG,QAAQ,CAAC,KAAK,QAAW;AACxC,gBAAI,KAAK;AACP,qBAAO,QAAO,GAAA,eAAA,aAAY,GAAG,CAAC;;AAGhC,oBAAQ,GAAG;UACb,CAAC;QACH,CAAC;MACH;;;;;;;;;;;;;;;;;MAkBA,OAAmB,QAAa;AAC9B,eAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,eAAK,KAAK,IAAI,GAAG,QAAQ,CAAC,KAAK,SAAY;AACzC,gBAAI,KAAK;AACP,qBAAO,QAAO,GAAA,eAAA,aAAY,GAAG,CAAC;;AAGhC,oBAAQ,IAAI;UACd,CAAC;QACH,CAAC;MACH;MAyCA,QAAkB,QAAa;AAC7B,eAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,gBAAM,WAAkC,OAAO,IAAG;AAElD,cAAI,CAAC,YAAY,OAAO,aAAa,YAAY;AAC/C,kBAAM,IAAI,MACR,oEAAoE;;AAIxE,cAAI,OAAO,SAAS,GAAG;AACrB,kBAAM,aAAa,OAAO,IAAG;AAE7B,gBAAI,OAAO,eAAe,YAAY;AACpC,oBAAM,IAAI,MACR,4FAA4F;;AAIhG,mBAAO,KAAK,UAAU;;AAGxB,eAAK,KAAK,KACR,GAAG,QACH,CAAC,KAAK,QAAO;AACX,gBAAI,KAAK;AACP,qBAAO,UAAS,GAAA,eAAA,aAAY,GAAG,GAAG,IAAI;;AAGxC,qBAAS,MAAM,GAAG;UACpB,GACA,CAAC,KAAK,UAAS;AACb,gBAAI,KAAK;AACP,qBAAO,QAAO,GAAA,eAAA,aAAY,GAAG,CAAC;;AAGhC,oBAAQ,KAAK;UACf,CAAC;QAEL,CAAC;MACH;;AAtOF,YAAA,YAAA;;;;;;;;;;ACPA,QAAA,KAAA;AACA,QAAA,OAAA;AAQO,mBAAe,eAAgB,eAAsB;AAC1D,YAAM,iBAAiB,iBAAiB,KAAK,KAAK,QAAQ,IAAG,GAAI,YAAY;AAC7E,YAAM,WAAW,KAAK,QAAQ,cAAc;AAK5C,YAAM,iBAAiB,MAAM,IAAI,QAC/B,CAAC,SAAS,WAAU;AAClB,WAAG,QAAQ,UAAU,CAAC,KAAK,UAAS;AAClC,cAAI,KAAK;AACP,mBAAO,OAAO,GAAG;;AAGnB,kBACE,MACG,IAAI,OAAK,EAAE,MAAM,oBAAoB,CAAC,EACtC,OAAO,OAAK,MAAM,IAAI,EACtB,IAAI,QAAM,EAAE,IAAI,OAAO,EAAE,CAAC,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAC,EAAG,EAC3D,KAAK,CAAC,GAAG,MAAM,KAAK,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;QAE7C,CAAC;MACH,CAAC;AAGH,UAAI,CAAC,eAAe,QAAQ;AAC1B,cAAM,IAAI,MAAM,gCAAgC,QAAQ,IAAI;;AAM9D,aAAO,QAAQ,IACb,eAAe,IACb,eACE,IAAI,QAAuB,CAAC,SAAS,WAAU;AAC7C,cAAM,WAAW,KAAK,KAAK,UAAU,UAAU,QAAQ;AACvD,WAAG,SAAS,UAAU,SAAS,CAAC,KAAK,SAAQ;AAC3C,cAAI,KAAK;AACP,mBAAO,OAAO,GAAG;;AAGnB,gBAAM,CAAC,IAAI,IAAI,IAAI,KAAK,MAAM,iBAAiB;AAE/C,gBAAM,gBAAgB;AACtB,wBAAc,KAAK,GAAG,QAAQ,cAAc,EAAE,EAAE,KAAI;AACpD,wBAAc,OAAO,OAAO,KAAK,KAAI,IAAK;AAC1C,kBAAQ,aAA8B;QACxC,CAAC;MACH,CAAC,CAAC,CACL;IAEL;AApDA,YAAA,iBAAA;AAyDO,mBAAe,QAAS,IAAc,SAA0B,CAAA,GAAE;AACvE,aAAO,QAAQ,OAAO,SAAS;AAC/B,aAAO,QAAQ,OAAO,SAAS;AAE/B,YAAM,EAAE,OAAO,MAAK,IAAK;AACzB,YAAM,aAAa,OAAO,aACtB,OAAO,aACP,MAAM,eAAe,OAAO,cAAc;AAG9C,YAAM,GAAG,IAAI,+BAA+B,KAAK;;;;;EAKjD;AAGA,UAAI,eAAe,MAAM,GAAG,IAC1B,mCAAmC,KAAK,mBAAmB;AAK7D,YAAM,gBAAgB,WAAW,WAAW,SAAS,CAAC;AACtD,iBAAW,aAAa,aACrB,MAAK,EACL,KAAK,CAAC,GAAG,MAAM,KAAK,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG;AACzC,YACE,CAAC,WAAW,KAAK,OAAK,EAAE,OAAO,UAAU,EAAE,KAC1C,SAAS,UAAU,OAAO,cAAc,IACzC;AACA,gBAAM,GAAG,IAAI,OAAO;AACpB,cAAI;AACF,kBAAM,GAAG,KAAK,UAAU,IAAI;AAC5B,kBAAM,GAAG,IAAI,gBAAgB,KAAK,kBAAkB,UAAU,EAAE;AAChE,kBAAM,GAAG,IAAI,QAAQ;AACrB,2BAAe,aAAa,OAAO,OAAK,EAAE,OAAO,UAAU,EAAE;mBACtD,KAAK;AACZ,kBAAM,GAAG,IAAI,UAAU;AACvB,kBAAM;;eAEH;AACL;;;AAKJ,YAAM,kBAAkB,aAAa,SACjC,aAAa,aAAa,SAAS,CAAC,EAAE,KACtC;AACJ,iBAAW,aAAa,YAAY;AAClC,YAAI,UAAU,KAAK,iBAAiB;AAClC,gBAAM,GAAG,IAAI,OAAO;AACpB,cAAI;AACF,kBAAM,GAAG,KAAK,UAAU,EAAE;AAC1B,kBAAM,GAAG,IACP,gBAAgB,KAAK,8CACrB,UAAU,IACV,UAAU,MACV,UAAU,IACV,UAAU,IAAI;AAEhB,kBAAM,GAAG,IAAI,QAAQ;mBACd,KAAK;AACZ,kBAAM,GAAG,IAAI,UAAU;AACvB,kBAAM;;;;IAId;AAtEA,YAAA,UAAA;;;;;;;;;;ACzDA,aAAgB,YACd,KACA,SAAgB,CAAA,GAAE;AAElB,UAAI,OAAO,QAAQ,UAAU;AAC3B,eAAO;UACL;UACA;;;AAIJ,aAAO;QACL,KAAK,IAAI;QACT,QAAQ,IAAI;;IAEhB;AAfA,YAAA,cAAA;;;;;;;;;;ACNA,QAAA,cAAA;AACA,QAAA,YAAA;AACA,QAAA,YAAA;AAGA,QAAA,iBAAA;AAKA,QAAaA,YAAb,MAAqB;MAOnB,YAAa,QAAsB;AACjC,aAAK,SAAS;AACd,aAAK,KAAK;MACZ;;;;;MAMA,GAAI,OAAe,UAAQ;AACzB,aAAK,GAAG,GAAG,OAAO,QAAQ;MAC5B;;;;MAKA,sBAAmB;AACjB,eAAO,KAAK;MACd;;;;MAKA,OAAI;AACF,eAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,cAAI,EAAE,UAAU,MAAM,OAAM,IAAK,KAAK;AAGtC,cAAI,aAAa,QAAQ,aAAa,QAAW;AAC/C,kBAAM,IAAI,MAAM,6CAA6C;;AAG/D,cAAI,CAAC,QAAQ;AACX,kBAAM,IAAI,MAAM,+BAA+B;;AAGjD,cAAI,MAAM;AACR,iBAAK,KAAK,IAAI,OAAO,UAAU,MAAM,SAAM;AACzC,kBAAI,KAAK;AACP,uBAAO,QAAO,GAAA,eAAA,aAAY,GAAG,CAAC;;AAGhC,sBAAO;YACT,CAAC;iBACI;AACL,iBAAK,KAAK,IAAI,OAAO,UAAU,SAAM;AACnC,kBAAI,KAAK;AACP,uBAAO,QAAO,GAAA,eAAA,aAAY,GAAG,CAAC;;AAGhC,sBAAO;YACT,CAAC;;QAEL,CAAC;MACH;;;;MAKA,QAAK;AACH,eAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,eAAK,GAAG,MAAM,SAAM;AAClB,gBAAI,KAAK;AACP,qBAAO,QAAO,GAAA,eAAA,aAAY,GAAG,CAAC;;AAGhC,oBAAO;UACT,CAAC;QACH,CAAC;MACH;;;;MAKA,UAAW,QAAiC,OAAU;AACpD,aAAK,GAAG,UAAU,QAAe,KAAK;MACxC;;;;;;;;;;;;;;;MAgBA,IACE,QACG,QAAa;AAEhB,eAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,gBAAM,UAAS,GAAA,UAAA,aAAY,KAAK,MAAM;AAEtC,eAAK,GAAG,IAAI,OAAO,KAAK,GAAG,OAAO,QAAQ,SAAU,KAAG;AACrD,gBAAI,KAAK;AACP,qBAAO,QAAO,GAAA,eAAA,aAAY,GAAG,CAAC;;AAGhC,oBAAQ;cACN,MAAM,IAAI,YAAA,UAAgB,KAAK,IAAI;cACnC,QAAQ,KAAK;cACb,SAAS,KAAK;aACf;UACH,CAAC;QACH,CAAC;MACH;;;;;;;;;;;;;;;;;;MAmBA,IACE,QACG,QAAa;AAEhB,eAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,gBAAM,UAAS,GAAA,UAAA,aAAY,KAAK,MAAM;AAEtC,eAAK,GAAG,IAAI,OAAO,KAAK,GAAG,OAAO,QAAQ,CAAC,KAAK,QAAW;AACzD,gBAAI,KAAK;AACP,qBAAO,QAAO,GAAA,eAAA,aAAY,GAAG,CAAC;;AAGhC,oBAAQ,GAAG;UACb,CAAC;QACH,CAAC;MACH;MA6CA,KAAe,QAAyB,QAAa;AACnD,eAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,gBAAM,WAAkC,OAAO,IAAG;AAElD,cAAI,CAAC,YAAY,OAAO,aAAa,YAAY;AAC/C,kBAAM,IAAI,MACR,mEAAmE;;AAIvE,cAAI,OAAO,SAAS,GAAG;AACrB,kBAAM,aAAa,OAAO,IAAG;AAE7B,gBAAI,OAAO,eAAe,YAAY;AACpC,oBAAM,IAAI,MACR,2FAA2F;;AAI/F,mBAAO,KAAK,UAAU;;AAGxB,gBAAM,UAAS,GAAA,UAAA,aAAY,KAAK,MAAM;AAEtC,eAAK,GAAG,KACN,OAAO,KACP,GAAG,OAAO,QACV,CAAC,KAAK,QAAO;AACX,gBAAI,KAAK;AACP,qBAAO,UAAS,GAAA,eAAA,aAAY,GAAG,GAAG,IAAI;;AAGxC,qBAAS,MAAM,GAAG;UACpB,GACA,CAAC,KAAK,UAAS;AACb,gBAAI,KAAK;AACP,qBAAO,QAAO,GAAA,eAAA,aAAY,GAAG,CAAC;;AAGhC,oBAAQ,KAAK;UACf,CAAC;QAEL,CAAC;MACH;;;;;;;;;;;;;;;;;;;;;;;;MAyBA,IAAgB,QAAyB,QAAa;AACpD,eAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,gBAAM,UAAS,GAAA,UAAA,aAAY,KAAK,MAAM;AAEtC,eAAK,GAAG,IAAI,OAAO,KAAK,GAAG,OAAO,QAAQ,CAAC,KAAK,SAAY;AAC1D,gBAAI,KAAK;AACP,qBAAO,QAAO,GAAA,eAAA,aAAY,GAAG,CAAC;;AAGhC,oBAAQ,IAAI;UACd,CAAC;QACH,CAAC;MACH;;;;;;;;;;;;MAaA,KAAM,KAAoB;AACxB,eAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,gBAAM,UAAS,GAAA,UAAA,aAAY,GAAG;AAE9B,eAAK,GAAG,KAAK,OAAO,KAAK,SAAM;AAC7B,gBAAI,KAAK;AACP,qBAAO,QAAO,GAAA,eAAA,aAAY,GAAG,CAAC;;AAGhC,oBAAO;UACT,CAAC;QACH,CAAC;MACH;;;;;;;;;;;;;MAcA,QAAS,QAAyB,QAAa;AAC7C,eAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,gBAAM,UAAS,GAAA,UAAA,aAAY,KAAK,MAAM;AAEtC,gBAAM,OAAO,KAAK,GAAG,QAAQ,OAAO,KAAK,GAAG,OAAO,QAAQ,SAAM;AAC/D,gBAAI,KAAK;AACP,qBAAO,OAAO,GAAG;;AAGnB,oBAAQ,IAAI,YAAA,UAAgB,IAAI,CAAC;UACnC,CAAC;QACH,CAAC;MACH;;;;;;MAOA,cAAe,MAAY;AACzB,eAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,eAAK,GAAG,cAAc,MAAM,SAAM;AAChC,gBAAI,KAAK;AACP,qBAAO,QAAO,GAAA,eAAA,aAAY,GAAG,CAAC;;AAGhC,oBAAO;UACT,CAAC;QACH,CAAC;MACH;;;;MAKA,MAAM,QAAS,QAAwB;AACrC,eAAM,GAAA,UAAA,SAAQ,MAAM,MAAM;MAC5B;;;;;;;MASA,YAAS;AACP,cAAM,IAAI,MACR,mFAAmF;MAEvF;;;;MAKA,cAAW;AACT,cAAM,IAAI,MACR,qFAAqF;MAEzF;;AAtXF,YAAA,WAAAA;;;;;ACbA;AAAA;AAAA;AAAA;AAAA,0BAAc;AACd,0BAAc;AACd,sBAAqB;AAKrB,eAAsB,KAAK,QAAQ;AACjC,QAAM,KAAK,IAAI,gBAAAC,QAAS,SAAS,MAAM;AACvC,QAAM,GAAG,KAAK;AACd,SAAO;AACT;", "names": ["Database", "Database"]}