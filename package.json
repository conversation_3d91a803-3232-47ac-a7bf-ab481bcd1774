{"name": "mygroup", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "node server.js", "dev": "vite", "dev:server": "nodemon server.js", "dev:full": "concurrently \"npm run dev\" \"npm run dev:server\"", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "db:setup": "node database/setup.js", "db:migrate": "node database/migrate.js", "db:seed": "node database/seed.js"}, "dependencies": {"bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "bootstrap": "^5.3.2", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "mysql2": "^3.6.5", "pg": "^8.16.1", "react": "^18.2.0", "react-bootstrap": "^2.9.1", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "sqlite": "^5.1.1", "sqlite3": "^5.1.6"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "concurrently": "^8.2.2", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "nodemon": "^3.0.2", "vite": "^5.0.0"}}